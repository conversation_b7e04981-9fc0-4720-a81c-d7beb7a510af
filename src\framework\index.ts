// 所有需要被 AutoImport 扫描的函数和对象都集中放在这一个文件里

export { useFramework } from './fx';
export { parseClass, parseStyle } from './style';
export { useFakePageList } from './devel';
export { injectDIWScrollManager, provideDIWScrollManager } from './scroll';
export { formatCurrency, formatDateTime, formatNumber, parseNumber } from '../util/number';
export { useSwipe } from './composables/swipe';
export { getFileExtensionLowerCase, detectFileTypeFromFileName } from '../util/file';
export { useDebounceFn } from '../util/vueuse';

export function parseJSON(s: any) {
	if (typeof s === 'string') {
		try {
			return JSON.parse(s);
		} catch (err) {
			console.error(err);
		}
	}

	return null;
}

export function defineDIWListViewMeta<T = number>(meta: DIWListViewMeta<T>): DIWListViewMeta<T> {
	return meta;
}
