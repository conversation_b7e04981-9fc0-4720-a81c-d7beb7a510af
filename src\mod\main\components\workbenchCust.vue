<!-- 工作台 - 客户 -->

<template>
	<view>
		<template v-if="workbenchStat">
			<InfoGroup v-if="isDef(workbenchStat.orderStat?.completedCount) || isDef(workbenchStat.orderStat?.lastYearTradeAmount)" title="交易情况">
				<InfoBlock class="w-full">
					<InfoItem
						v-if="isDef(workbenchStat.orderStat?.completedCount)"
						class="flex-1"
						title="已完成合同"
						:value="workbenchStat.orderStat?.completedCount"
						unit="单"
						@click="viewOrders('7')"
					/>
					<InfoItem
						v-if="isDef(workbenchStat.orderStat?.lastYearTradeAmount)"
						class="flex-1"
						title="一年内交易额"
						custom
						@click="viewOrders('14', '我的合同-一年内交易额')"
					>
						<text class="text-sm text-red-600 font-bold">¥</text>
						<text class="text-lg text-red-600 font-bold">{{ lastYearTradeAmount }}</text>
					</InfoItem>
				</InfoBlock>
			</InfoGroup>

			<InfoGroup
				v-if="isDef(workbenchStat.accountStat) && isDef(workbenchStat.accountStat.totalWithdrawal) && isDef(workbenchStat.accountStat.totalRecharge)"
				title="提现充值"
			>
				<InfoBlock class="w-full">
					<InfoItem
						v-if="isDef(workbenchStat.accountStat.totalWithdrawal)"
						class="flex-1"
						title="累计提现"
						custom
						@click="openAccountInfo('withdrawal')"
					>
						<text class="text-sm text-red-600 font-bold">¥</text>
						<text class="text-lg text-red-600 font-bold">{{ totalWithdrawal }}</text>
					</InfoItem>
					<InfoItem v-if="isDef(workbenchStat.accountStat.totalRecharge)" class="flex-1" title="累计充值" custom @click="openAccountInfo('fund')">
						<text class="text-sm text-red-600 font-bold">¥</text>
						<text class="text-lg text-red-600 font-bold">{{ totalRecharge }}</text>
					</InfoItem>
				</InfoBlock>
			</InfoGroup>

			<InfoGroup
				v-if="
					isDef(workbenchStat.orderStat?.pendingAuditNum) ||
					isDef(workbenchStat.orderStat?.pendingConfirmAdjustNum) ||
					isDef(workbenchStat.orderStat?.pendingSignNum)
				"
				title="合同处理"
			>
				<InfoBlock
					v-if="isDef(workbenchStat.orderStat?.pendingAuditNum)"
					class="w-half"
					title="合同待审核"
					:value="workbenchStat.orderStat.pendingAuditNum"
					unit="单"
					icon="iconfont icon-tubiao-daishenhehetong-moren"
					bg="bg-orange-50"
					fg="text-orange-600"
					@click="viewOrders('1')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.orderStat?.pendingConfirmAdjustNum)"
					class="w-half"
					title="合同调整待确认"
					:value="workbenchStat.orderStat.pendingConfirmAdjustNum"
					unit="单"
					icon="iconfont icon-hetongdaiqueren"
					bg="bg-amber-50"
					fg="text-amber-600"
					@click="viewOrders('2')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.orderStat?.pendingSignNum)"
					class="w-half"
					title="合同待签约"
					:value="workbenchStat.orderStat.pendingSignNum"
					unit="单"
					icon="iconfont icon-daiqianyue"
					bg="bg-purple-50"
					fg="text-purple-600"
					@click="viewOrders('3')"
				/>
			</InfoGroup>

			<InfoGroup
				v-if="
					isDef(workbenchStat.orderStat?.yarnPendingPayAmount) ||
					isDef(workbenchStat.orderStat?.pendingViolateAmount) ||
					isDef(workbenchStat.orderStat?.pendingSealAmount) ||
					isDef(workbenchStat.orderStat?.pendingPickup) ||
					isDef(workbenchStat.deliverStat?.pendingNum) ||
					isDef(workbenchStat.deliverStat?.signPendingNum) ||
					isDef(workbenchStat.outBillStat?.deliveredNum) ||
					isDef(workbenchStat.outBillStat?.waitingReceivedNum)
				"
				title="合同履约"
			>
				<InfoBlock class="w-full" custom>
					<InfoItem v-if="isDef(workbenchStat.orderStat?.yarnPendingPayAmount)" class="flex-1" title="待付款" custom @click="viewOrders('4')">
						<text class="text-sm text-red-600 font-bold">¥</text>
						<text class="text-lg text-red-600 font-bold">{{ yarnPendingPayAmount }}</text>
					</InfoItem>
					<InfoItem
						v-if="isDef(workbenchStat.orderStat?.pendingViolateAmount) || isDef(workbenchStat.orderStat?.pendingSealAmount)"
						class="flex-1"
						custom
					>
						<view class="flex flex-col items-start">
							<view v-if="isDef(workbenchStat.orderStat?.pendingViolateAmount)" @click="viewOrders('5', '我的合同-待付违约金')">
								<text class="text-xs">待付违约金额</text>
								<view>
									<text class="text-xs text-red-600 font-bold">¥</text>
									<text class="text-red-600 font-bold">{{ pendingViolateAmount }}</text>
								</view>
							</view>
							<view v-if="isDef(workbenchStat.orderStat?.pendingSealAmount)" @click="viewOrders('8', '我的合同-待付中止合同金额')">
								<text class="text-xs">待付中止合同金额</text>
								<view>
									<text class="text-xs text-red-600 font-bold">¥</text>
									<text class="text-red-600 font-bold">{{ pendingSealAmount }}</text>
								</view>
							</view>
						</view>
					</InfoItem>
				</InfoBlock>
				<InfoBlock
					v-if="isDef(workbenchStat.orderStat?.pendingPickup)"
					class="w-half"
					title="发货/锁批/货转申请"
					:value="workbenchStat.orderStat.pendingPickup"
					unit="单"
					icon="iconfont icon-baoguofahuo-xianxing"
					bg="bg-indigo-50"
					fg="text-indigo-600"
					@click="viewOrders('6')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.deliverStat?.pendingNum)"
					class="w-half"
					title="待确认申请的提货单"
					:value="workbenchStat.deliverStat.pendingNum"
					unit="单"
					icon="iconfont icon-daishenqing"
					bg="bg-yellow-50"
					fg="text-yellow-600"
					@click="viewDeliverOrders('waitAudit')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.deliverStat?.signPendingNum)"
					class="w-half"
					title="货转待签署"
					:value="workbenchStat.deliverStat.signPendingNum"
					unit="单"
					icon="iconfont icon-daiqianshu"
					bg="bg-pink-50"
					fg="text-pink-600"
					@click="viewDeliverOrders('waitSign')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.outBillStat?.deliveredNum)"
					class="w-half"
					title="已发货/可提货"
					:value="workbenchStat.outBillStat.deliveredNum"
					unit="单"
					icon="iconfont icon-ketihuo"
					bg="bg-green-50"
					fg="text-green-600"
					@click="viewDeliverConfirm('doneTran')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.outBillStat?.waitingReceivedNum)"
					class="w-half"
					title="待确认收货"
					:value="workbenchStat.outBillStat.waitingReceivedNum"
					unit="单"
					icon="iconfont icon-dengdaiquerenshouhuo"
					bg="bg-cyan-50"
					fg="text-cyan-600"
					@click="viewDeliverConfirm('waitConfirm')"
				/>
			</InfoGroup>

			<InfoGroup v-if="!!workbenchStat.deliverStat && !!workbenchStat.invoiceStat" title="发票处理">
				<InfoBlock
					v-if="isDef(workbenchStat.deliverStat?.canKpNum)"
					class="w-half"
					title="待申请开票"
					:value="workbenchStat.deliverStat.canKpNum"
					unit="单"
					icon="iconfont icon-fapiaokaiju"
					bg="bg-blue-50"
					fg="text-blue-600"
					@click="viewDeliverOrders('invoiceApply')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.invoiceStat?.kpRecordNum)"
					class="w-half"
					title="发票开具情况"
					:value="workbenchStat.invoiceStat.kpRecordNum"
					unit="单"
					icon="iconfont icon-fapiao"
					bg="bg-emerald-50"
					fg="text-emerald-600"
				/>
			</InfoGroup>

			<InfoGroup
				v-if="isDef(workbenchStat.shippingRecordStat?.pendingNum) || isDef(workbenchStat.shippingRecordStat?.rejectNum)"
				title="收货地址修改"
			>
				<InfoBlock
					v-if="isDef(workbenchStat.shippingRecordStat.pendingNum)"
					class="w-half"
					title="待审核"
					:value="workbenchStat.shippingRecordStat.pendingNum"
					unit="单"
					icon="iconfont icon-dizhi"
					bg="bg-blue-50"
					fg="text-blue-600"
					@click="viewAddressAuditList('todo')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.shippingRecordStat.rejectNum)"
					class="w-half"
					title="审核不通过"
					:value="workbenchStat.shippingRecordStat.rejectNum"
					unit="单"
					icon="iconfont icon-shenhebutongguo"
					bg="bg-red-50"
					fg="text-red-600"
					@click="viewAddressAuditList('done')"
				/>
			</InfoGroup>

			<InfoGroup
				v-if="isDef(workbenchStat.extractingRecordStat?.pendingNum) || isDef(workbenchStat.extractingRecordStat?.rejectNum)"
				title="自提信息修改"
			>
				<InfoBlock
					v-if="isDef(workbenchStat.extractingRecordStat.pendingNum)"
					class="w-half"
					title="待审核"
					:value="workbenchStat.extractingRecordStat.pendingNum"
					unit="单"
					icon="iconfont icon-ziti"
					bg="bg-violet-50"
					fg="text-violet-600"
					@click="viewPickupInfoAuditList('todo')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.extractingRecordStat.rejectNum)"
					class="w-half"
					title="审核不通过"
					:value="workbenchStat.extractingRecordStat.rejectNum"
					unit="单"
					icon="iconfont icon-shenhebutongguo"
					bg="bg-red-50"
					fg="text-red-600"
					@click="viewPickupInfoAuditList('done')"
				/>
			</InfoGroup>

			<InfoGroup v-if="isDef(workbenchStat.aheadLockStat?.pendingNum) || isDef(workbenchStat.aheadLockStat?.rejectNum)" title="锁批申请结果">
				<InfoBlock
					v-if="isDef(workbenchStat.aheadLockStat.pendingNum)"
					class="w-half"
					title="待审核"
					:value="workbenchStat.aheadLockStat.pendingNum"
					unit="单"
					icon="iconfont icon-LIMS_picijieguoluru"
					bg="bg-orange-50"
					fg="text-orange-600"
					@click="viewLockBatchList('todo')"
				/>
				<InfoBlock
					v-if="isDef(workbenchStat.aheadLockStat.rejectNum)"
					class="w-half"
					title="审核不通过"
					:value="workbenchStat.aheadLockStat.rejectNum"
					unit="单"
					icon="iconfont icon-shenhebutongguo"
					bg="bg-red-50"
					fg="text-red-600"
					@click="viewLockBatchList('done')"
				/>
			</InfoGroup>
		</template>

		<!--
		<text v-if="workbenchStat" class="whitespace-pre-wrap break-all">{{ JSON.stringify(workbenchStat, null, 4) }}</text>
		-->
	</view>
</template>

<script setup lang="ts">
import InfoGroup from './InfoGroup.vue';
import InfoBlock from './InfoBlock.vue';
import InfoItem from './InfoItem.vue';
import { useStatStore } from '@/store/stat';
import { isDef } from './common';

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		styleIsolation: 'shared',
	},
});

// #endif

const { protect, navigateTo, checkAuth } = useFramework();

const { workbenchStat } = storeToRefs(useStatStore());

const yarnPendingPayAmount = computed(() => {
	if (isDef(workbenchStat.value?.orderStat?.yarnPendingPayAmount)) {
		return formatCurrency(workbenchStat.value?.orderStat?.yarnPendingPayAmount);
	}

	return formatCurrency(0);
});

const pendingViolateAmount = computed(() => {
	if (isDef(workbenchStat.value?.orderStat?.pendingViolateAmount)) {
		return formatCurrency(workbenchStat.value?.orderStat?.pendingViolateAmount);
	}

	return formatCurrency(0);
});

const pendingSealAmount = computed(() => {
	if (isDef(workbenchStat.value?.orderStat?.pendingSealAmount)) {
		return formatCurrency(workbenchStat.value?.orderStat?.pendingSealAmount);
	}

	return formatCurrency(0);
});

const totalWithdrawal = computed(() => {
	if (isDef(workbenchStat.value?.accountStat?.totalWithdrawal)) {
		return formatCurrency(workbenchStat.value?.accountStat?.totalWithdrawal);
	}

	return formatCurrency(0);
});

const totalRecharge = computed(() => {
	if (isDef(workbenchStat.value?.accountStat?.totalRecharge)) {
		return formatCurrency(workbenchStat.value?.accountStat?.totalRecharge);
	}

	return formatCurrency(0);
});

const lastYearTradeAmount = computed(() => {
	if (isDef(workbenchStat.value?.orderStat?.lastYearTradeAmount)) {
		return formatCurrency(workbenchStat.value?.orderStat?.lastYearTradeAmount);
	}

	return formatCurrency(0);
});

function openAccountInfo(preset: string = '') {
	protect(async () => {
		switch (preset) {
			case 'withdrawal':
				if (!checkAuth('pay_virtual_account_withdraw', false)) {
					return;
				}
				if (!checkAuth('pay_virtual_account_application_view', false)) {
					return;
				}
				await navigateTo({ url: '/mod/pay/cashFlowList', params: { preset } });
				break;
			case 'fund':
				if (!checkAuth('pay_virtual_account_application_view', false)) {
					return;
				}
				await navigateTo('/mod/pay/fundList');
				break;
			default:
				await navigateTo('/mod/pay/home');
				break;
		}
	});
}

function viewOrders(status: string = '', pageTitle: string = '') {
	navigateTo({ url: '/mod/order/buyerOrderList', params: { status, pageTitle } });
}

function viewDeliverOrders(preset: string = '') {
	navigateTo({ url: '/mod/deliver/deliverTabs', params: { preset } });
}

function viewDeliverConfirm(preset: string = '') {
	navigateTo({ url: '/mod/deliver/confirm', params: { preset } });
}

function viewPickupInfoAuditList(preset: string = '') {
	navigateTo({ url: '/mod/deliver/pickupInfoAuditList', params: { preset } });
}

function viewAddressAuditList(preset: string = '') {
	navigateTo({ url: '/mod/deliver/addressAuditList', params: { preset } });
}

function viewLockBatchList(preset: string = '') {
	navigateTo({ url: '/mod/deliver/lockBatchList', params: { preset } });
}
</script>
