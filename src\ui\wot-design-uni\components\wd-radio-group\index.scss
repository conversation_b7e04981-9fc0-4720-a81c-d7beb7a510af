@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(radio-group) {
		background-color: $wot-dark-background2;
	}
}

@include b(radio-group) {
	background-color: $wot-radio-bg;
	font-size: 0;

	// 上下20px 左右15px 内部间隔12px
	@include when(button) {
		width: 100%;
		height: 100%;
		padding: 8px 3px 20px 15px;
		box-sizing: border-box;
		overflow: hidden;
		height: auto;
	}
}
