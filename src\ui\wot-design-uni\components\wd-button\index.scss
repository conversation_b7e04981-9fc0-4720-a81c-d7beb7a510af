@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(button) {
		@include when(info) {
			background: $wot-dark-background4;
			color: $wot-dark-color3;
		}

		@include when(plain) {
			background: transparent;

			@include when(info) {
				color: $wot-dark-color;

				&::after {
					border-color: $wot-dark-background5;
				}
			}
		}

		@include when(text) {
			@include when(disabled) {
				color: $wot-dark-color-gray;
				background: transparent;
			}
		}

		@include when(icon) {
			color: $wot-dark-color;

			@include when(disabled) {
				color: $wot-dark-color-gray;
				background: transparent;
			}
		}
	}
}

@include b(button) {
	margin-left: initial;
	margin-right: initial;
	position: relative;
	display: inline-block;
	outline: none;
	-webkit-appearance: none;
	outline: none;
	background: transparent;
	box-sizing: border-box;
	border: none;
	border-radius: 0;
	color: $wot-button-normal-color;
	transition: opacity 0.2s;
	user-select: none;
	font-weight: normal;

	&::before {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		height: 100%;
		background: $wot-color-black;
		border: inherit;
		border-color: $wot-color-black;
		border-radius: inherit;
		transform: translate(-50%, -50%);
		opacity: 0;
		content: ' ';
	}

	&::after {
		border: none;
		border-radius: 0;
	}

	@include e(content) {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
	}

	@include m(active) {
		&:active::before {
			opacity: 0.15;
		}
	}

	@include when(disabled) {
		opacity: $wot-button-disabled-opacity;
	}

	@include e(loading) {
		margin-right: 5px;
		animation: wd-rotate 0.8s linear infinite;
		animation-duration: 2s;
	}

	@include e(loading-svg) {
		width: 100%;
		height: 100%;
		background-size: cover;
		background-repeat: no-repeat;
	}

	@include when(loading) {
	}

	@include when(primary) {
		background: $wot-button-primary-bg-color;
		color: $wot-button-primary-color;
	}

	@include when(success) {
		background: $wot-button-success-bg-color;
		color: $wot-button-success-color;
	}

	@include when(info) {
		background: $wot-button-info-bg-color;
		color: $wot-button-info-color;
	}

	@include when(warning) {
		background: $wot-button-warning-bg-color;
		color: $wot-button-warning-color;
	}

	@include when(error) {
		background: $wot-button-error-bg-color;
		color: $wot-button-error-color;
	}

	@include when(small) {
		height: $wot-button-small-height;
		padding: $wot-button-small-padding;
		border-radius: $wot-button-small-radius;
		font-size: $wot-button-small-fs;
		font-weight: normal;

		.wd-button__loading {
			width: $wot-button-small-loading;
			height: $wot-button-small-loading;
		}
	}

	@include when(medium) {
		height: $wot-button-medium-height;
		padding: $wot-button-medium-padding;
		border-radius: $wot-button-medium-radius;
		font-size: $wot-button-medium-fs;
		min-width: 120px;

		@include when(round) {
			@include when(icon) {
				min-width: 0;
				border-radius: 50%;
			}

			@include when(text) {
				border-radius: 0;
				min-width: 0;
			}
		}

		.wd-button__loading {
			width: $wot-button-medium-loading;
			height: $wot-button-medium-loading;
		}
	}

	@include when(large) {
		height: $wot-button-large-height;
		padding: $wot-button-large-padding;
		border-radius: $wot-button-large-radius;
		font-size: $wot-button-large-fs;

		&::after {
			border-radius: $wot-button-large-radius;
		}

		.wd-button__loading {
			width: $wot-button-large-loading;
			height: $wot-button-large-loading;
		}
	}

	@include when(round) {
		border-radius: 999px;
	}

	@include when(text) {
		color: $wot-button-primary-bg-color;
		min-width: 0;
		padding: 4px 0;

		&::after {
			display: none;
		}

		&.wd-button--active {
			opacity: $wot-button-text-hover-opacity;

			&:active::before {
				display: none;
			}
		}

		@include when(disabled) {
			color: $wot-button-normal-disabled-color;
			background: transparent;
		}
	}

	@include when(plain) {
		background: $wot-button-plain-bg-color;
		border: 1px solid currentColor;

		@include when(primary) {
			color: $wot-button-primary-bg-color;
		}

		@include when(success) {
			color: $wot-button-success-bg-color;
		}

		@include when(info) {
			color: $wot-button-info-plain-normal-color;
			border-color: $wot-button-info-plain-border-color;
		}

		@include when(warning) {
			color: $wot-button-warning-bg-color;
		}

		@include when(error) {
			color: $wot-button-error-bg-color;
		}
	}

	@include when(hairline) {
		border-width: 0;

		&.is-plain {
			@include halfPixelBorderSurround();

			&::before {
				border-radius: inherit;
			}

			&::after {
				border-color: inherit;
			}

			&.is-round {
				&::after {
					border-radius: inherit !important;
				}
			}

			&.is-large {
				&::after {
					border-radius: calc(2 * $wot-button-large-radius);
				}
			}

			&.is-medium {
				&::after {
					border-radius: calc(2 * $wot-button-medium-radius);
				}
			}

			&.is-small {
				&::after {
					border-radius: calc(2 * $wot-button-small-radius);
				}
			}
		}
	}

	@include when(block) {
		display: block;
	}

	@include when(icon) {
		width: $wot-button-icon-size;
		height: $wot-button-icon-size;
		padding: 0;
		border-radius: 50%;
		color: $wot-button-icon-color;

		&::after {
			display: none;
		}

		:deep(.wd-button__icon) {
			margin-right: 0;
		}

		@include when(disabled) {
			color: $wot-button-icon-disabled-color;
			background: transparent;
		}
	}

	@include edeep(icon) {
		display: block;
		margin-right: 6px;
		font-size: $wot-button-icon-fs;
		vertical-align: middle;
	}

	@include e(text) {
		user-select: none;
		white-space: nowrap;
	}
}

@keyframes wd-rotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}
