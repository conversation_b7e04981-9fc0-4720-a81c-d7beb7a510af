@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(collapse-item) {
		@include halfPixelBorder('top', 0, $wot-dark-border-color);

		@include e(title) {
			color: $wot-dark-color;
		}

		@include e(body) {
			color: $wot-dark-color3;
		}

		@include when(disabled) {
			.wd-collapse-item__title {
				color: $wot-dark-color-gray;
			}
			.wd-collapse-item__arrow {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(collapse-item) {
	position: relative;
	@include halfPixelBorder('top');

	@include e(header) {
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: $wot-collapse-header-padding;
		overflow: hidden;
		user-select: none;

		@include when(expanded) {
			@include halfPixelBorder('bottom');
		}

		@include when(custom) {
			display: block;
		}
	}

	@include e(title) {
		color: $wot-collapse-title-color;
		font-weight: $wot-fw-medium;
		font-size: $wot-collapse-title-fs;
	}

	@include edeep(arrow) {
		display: block;
		font-size: $wot-collapse-arrow-size;
		color: $wot-collapse-arrow-color;
		transition: transform 0.3s;

		@include when(retract) {
			transform: rotate(-180deg);
		}
	}

	@include e(wrapper) {
		position: relative;
		overflow: hidden;
		will-change: height;
	}

	@include e(body) {
		color: $wot-collapse-body-color;
		font-size: $wot-collapse-body-fs;
		padding: $wot-collapse-body-padding;
		line-height: 1.43;
	}

	@include when(disabled) {
		.wd-collapse-item__title {
			color: $wot-collapse-disabled-color;
		}
		.wd-collapse-item__arrow {
			color: $wot-collapse-disabled-color;
		}
	}
}
