<template>
	<DIWAppPage :title="title">
		<DemoHome1 v-if="pq.mode === 'demoHome1'" />
		<List1 v-else-if="pq.mode === 'list1'" />
	</DIWAppPage>
</template>

<script setup lang="ts">
import DemoHome1 from './components/demoHome1.vue';
import List1 from './components/list1.vue';

const props = defineProps<{ mode: string }>();

const { usePageQuery } = useFramework();

const pq = usePageQuery();

const title = computed(() => {
	switch (pq.value.mode) {
		case 'demoHome1':
			return '首页示例';

		case 'list1':
			return '列表';

		case 'form1':
			return '简单表单';
	}
});
</script>
