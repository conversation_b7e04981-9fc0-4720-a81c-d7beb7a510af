<template>
	<view class="alarm-section">
		<view class="alarm-card">
			<view class="alarm-header">
				<view class="title-wrapper">
					<view class="title-wrapper-inner">
						<view class="title-indicator"></view>
						<text class="title">{{ t('abnormalAlarm') }}</text>
					</view>
					<wd-icon name="arrow-right" size="24rpx" color="#1D2129" @click="toAlarmHistoryList" />
				</view>
			</view>

			<!-- 表头 -->
			<view class="alarm-table-header">
				<text class="header-cell machine">{{ t('machine') }}</text>
				<text class="header-cell alarm">{{ t('alarm') }}</text>
				<text class="header-cell time">{{ t('alarmTime') }}</text>
				<text class="header-cell duration">{{ t('duration') }}</text>
				<text class="header-cell status">{{ t('recovered') }}</text>
			</view>

			<!-- 报警列表 -->
			<scroll-view scroll-y class="alarm-list">
				<view v-for="(alarm, index) in alarmList" :key="index" class="alarm-row">
					<text class="cell machine">{{ alarm.machineName }}</text>
					<text class="cell alarm">{{ alarm.alarmName }}</text>
					<!-- 暂时时间只取时分秒 -->
					<text class="cell time">{{ alarm.startTime.split(' ')[1] }}</text>
					<text class="cell duration">{{ alarm.durationTime }}</text>
					<text class="cell status" :class="{ recovered: alarm.isRestore === 0, 'not-recovered': alarm.isRestore === 1 }">
						{{ alarm.isRestore === 0 ? t('yes') : t('no') }}
					</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const props = defineProps<{
	needRefresh: boolean;
}>();
const { apiGet, navigateTo } = useFramework();

// 监听props.needRefresh变化，重新获取数据
watch(
	() => props.needRefresh,
	async (newVal) => {
		if (newVal) {
			await getAlarmList();
		}
	}
);

onMounted(async () => {
	await getAlarmList();
});

// 获取告警列表
async function getAlarmList() {
	const d = await apiGet('mesmultidata/MachineAlarm/list');
	console.log(d);
	alarmList.value = d || [];
	//isRestore 0 是 1 否
}

// 跳转告警历史列表
function toAlarmHistoryList() {
	navigateTo('/mod/alarm/alarmHistoryList');
}

// 使用国际化
const { t } = useI18n();

// 模拟报警数据
const alarmList = ref<any[]>([]);
</script>

<style lang="scss" scoped>
.alarm-section {
	margin-top: -76rpx;
	width: 702rpx;
	height: 476rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 702rpx 476rpx;
	background-repeat: no-repeat;
	background-position: 0 0;
}

.alarm-card {
	width: 100%;
	height: 476rpx;
	padding: 0rpx 36rpx 0 36rpx;
	display: flex;
	flex-direction: column;
}

.alarm-header {
	padding: 38rpx 36rpx 0 36rpx;
	.title-wrapper {
		display: flex;
		justify-content: flex-start;
		gap: 18rpx;
		align-items: center;
	}

	.title-wrapper-inner {
		display: flex;
		align-items: center;

		.title-indicator {
			width: 8rpx;
			height: 32rpx;
			background: #0082f0;
			border-radius: 4rpx;
			margin-right: 16rpx;
		}

		.title {
			font-size: 30rpx;
			font-weight: normal;
			line-height: 24rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #1d2129;
		}
	}
}

.alarm-table-header {
	height: 68rpx;
	margin: 7rpx 14rpx 7rpx 14rpx;
	display: flex;
	background: rgba(0, 130, 240, 0.102);

	.header-cell {
		font-size: 24rpx;
		font-weight: normal;
		line-height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		letter-spacing: 0rpx;
		color: #1d2129;
		text-align: center;

		&.machine {
			flex: 1.2;
			padding-left: 24rpx;
			justify-content: flex-start;
		}

		&.alarm {
			flex: 1.5;
			display: flex;
			justify-content: flex-start;
			flex-wrap: wrap;
		}

		&.time {
			flex: 1.2;
		}

		&.duration {
			flex: 1;
		}

		&.status {
			flex: 1;
		}
	}
}

.alarm-list {
	height: 375rpx;
	padding: 0 7rpx;
	overflow-y: auto;
	// 优化滚动条
	&::-webkit-scrollbar {
		width: 2rpx;
	}

	.alarm-row {
		min-height: 64rpx;
		display: flex;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			// border-bottom: none;
		}

		.cell {
			font-size: 24rpx;
			font-weight: normal;
			line-height: 30rpx;
			letter-spacing: 0rpx;
			color: #1d2129;
			display: flex;
			justify-content: center;
			align-items: center;

			&.machine {
				flex: 1.2;
				padding-left: 24rpx;
				justify-content: flex-start;
			}

			&.alarm {
				flex: 1.5;
				justify-content: flex-start;
			}

			&.time {
				flex: 1.2;
			}

			&.duration {
				flex: 1;
			}

			&.status {
				flex: 1;
				font-weight: 600;

				&.recovered {
					color: #0082f0;
				}

				&.not-recovered {
					color: #ef4444;
				}
			}
		}
	}
}
</style>
