<template>
	<DIWAppPage mode="1">
		<template #appBar>
			<view>appbar</view>
		</template>
		<DIWScrollView>
			<DIWListView :meta="listViewMeta">
				<template #default="{ data }">
					<view>
						<text class="text-xs whitespace-pre-wrap break-all">{{ JSON.stringify(data, null, 4) }}</text>
					</view>
				</template>
			</DIWListView>
		</DIWScrollView>
		<template #bottomBar>
			<view>bottom</view>
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { apiGet, protect, navigateTo } = useFramework();

const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		rowKey: 'roleId',
		async loadData(pageIndex) {
			const size1 = 20;
			const size2 = 5;

			const isReload = pageIndex === undefined;

			let current = isReload ? 1 : pageIndex;
			let size = isReload ? size1 : size2;

			const d = await apiGet({
				url: 'admin/user/page',
				params: {
					current,
					size,
				},
			});

			if (isReload) {
				return { hasMore: d.total > size, items: d.records, trackInfo: 1 + size1 / size2 };
			}

			return { hasMore: d.total > current * size, items: d.records, trackInfo: current + 1 };
		},
	});
});
</script>
