export type FunctionArgs<Args extends any[] = any[], Return = void> = (...args: Args) => Return;

export interface FunctionWrapperOptions<Args extends any[] = any[], This = any> {
	fn: FunctionArgs<Args, This>;
	args: Args;
	thisArg: This;
}

export type EventFilter<Args extends any[] = any[], This = any, Invoke extends AnyFn = AnyFn> = (
	invoke: Invoke,
	options: FunctionWrapperOptions<Args, This>
) => ReturnType<Invoke> | Promisify<ReturnType<Invoke>>;
/**
 * Void function
 */
export type Fn = () => void;

/**
 * Any function
 */
export type AnyFn = (...args: any[]) => any;

/**
 * A ref that allow to set null or undefined
 */
export type RemovableRef<T> = Omit<Ref<T>, 'value'> & {
	get value(): T;
	set value(value: T | null | undefined);
};

/**
 * Maybe it's a computed ref, or a readonly value, or a getter function
 */
export type ReadonlyRefOrGetter<T> = ComputedRef<T> | (() => T);

/**
 * Make all the nested attributes of an object or array to MaybeRef<T>
 *
 * Good for accepting options that will be wrapped with `reactive` or `ref`
 *
 * ```ts
 * UnwrapRef<DeepMaybeRef<T>> === T
 * ```
 */
export type DeepMaybeRef<T> =
	T extends Ref<infer V> ? MaybeRef<V> : T extends Array<any> | object ? { [K in keyof T]: DeepMaybeRef<T[K]> } : MaybeRef<T>;

export type Arrayable<T> = T[] | T;

/**
 * Infers the element type of an array
 */
export type ElementOf<T> = T extends (infer E)[] ? E : never;

export type ShallowUnwrapRef<T> = T extends Ref<infer P> ? P : T;

export type Awaitable<T> = Promise<T> | T;

export type ArgumentsType<T> = T extends (...args: infer U) => any ? U : never;

/**
 * Compatible with versions below TypeScript 4.5 Awaited
 */
export type Awaited<T> = T extends null | undefined
	? T // special case for `null | undefined` when not in `--strictNullChecks` mode
	: T extends object & { then: (onfulfilled: infer F, ...args: infer _) => any } // `await` only unwraps object types with a callable `then`. Non-object types are not unwrapped
		? F extends (value: infer V, ...args: infer _) => any // if the argument to `then` is callable, extracts the first argument
			? Awaited<V> // recursively unwrap the value
			: never // the argument to `then` was not callable
		: T; // non-object or non-thenable

export type Promisify<T> = Promise<Awaited<T>>;

export type PromisifyFn<T extends AnyFn> = (...args: ArgumentsType<T>) => Promisify<ReturnType<T>>;

export function createFilterWrapper<T extends AnyFn>(filter: EventFilter, fn: T) {
	function wrapper(this: any, ...args: ArgumentsType<T>) {
		return new Promise<Awaited<ReturnType<T>>>((resolve, reject) => {
			// make sure it's a promise
			Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args }))
				.then(resolve)
				.catch(reject);
		});
	}

	return wrapper;
}

export const noop = () => {};

export function debounceFilter(ms: MaybeRefOrGetter<number>, options: DebounceFilterOptions = {}) {
	let timer: ReturnType<typeof setTimeout> | undefined;
	let maxTimer: ReturnType<typeof setTimeout> | undefined | null;
	let lastRejector: AnyFn = noop;

	const _clearTimeout = (timer: ReturnType<typeof setTimeout>) => {
		clearTimeout(timer);
		lastRejector();
		lastRejector = noop;
	};

	let lastInvoker: () => void;

	const filter: EventFilter = (invoke) => {
		const duration = toValue(ms);
		const maxDuration = toValue(options.maxWait);

		if (timer) _clearTimeout(timer);

		if (duration <= 0 || (maxDuration !== undefined && maxDuration <= 0)) {
			if (maxTimer) {
				_clearTimeout(maxTimer);
				maxTimer = null;
			}
			return Promise.resolve(invoke());
		}

		return new Promise((resolve, reject) => {
			lastRejector = options.rejectOnCancel ? reject : resolve;
			lastInvoker = invoke;
			// Create the maxTimer. Clears the regular timer on invoke
			if (maxDuration && !maxTimer) {
				maxTimer = setTimeout(() => {
					if (timer) _clearTimeout(timer);
					maxTimer = null;
					resolve(lastInvoker());
				}, maxDuration);
			}

			// Create the regular timer. Clears the max timer on invoke
			timer = setTimeout(() => {
				if (maxTimer) _clearTimeout(maxTimer);
				maxTimer = null;
				resolve(invoke());
			}, duration);
		});
	};

	return filter;
}

export interface DebounceFilterOptions {
	/**
	 * The maximum time allowed to be delayed before it's invoked.
	 * In milliseconds.
	 */
	maxWait?: MaybeRefOrGetter<number>;

	/**
	 * Whether to reject the last call if it's been cancel.
	 *
	 * @default false
	 */
	rejectOnCancel?: boolean;
}

export type UseDebounceFnReturn<T extends FunctionArgs> = PromisifyFn<T>;

export function useDebounceFn<T extends FunctionArgs>(
	fn: T,
	ms: MaybeRefOrGetter<number> = 200,
	options: DebounceFilterOptions = {}
): UseDebounceFnReturn<T> {
	return createFilterWrapper(debounceFilter(ms, options), fn);
}
