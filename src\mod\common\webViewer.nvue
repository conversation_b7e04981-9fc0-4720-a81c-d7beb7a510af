<template>
	<web-view class="diw-webview" :src="src" @onPostMessage="handlePostMessage" />
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount, watchEffect } from 'vue';
import { useFramework } from '@/framework/index';

const { usePageQuery, usePageResult, apiPost } = useFramework();

const pq = usePageQuery();

const { finishPage, setPageResult } = usePageResult();

const src = computed(() => {
	return pq.value?.url;
});

watchEffect(() => {
	const title = pq.value?.title;
	if (typeof title === 'string') {
		uni.setNavigationBarTitle({ title });
	}
});

// #ifdef H5

function handleMessage(ev: any) {
	if (ev.data && ev.data.cookie === pq.value?.cookie) {
		if (ev.data.bizType && ev.data.bizId) {
			notifyQiyuesuoSuccess(ev.data.bizId, ev.data.bizType);
		} else {
			const pages = getCurrentPages();
			console.log(pages, pages);
			for (const p of pages) {
				console.log('ppp', p);
			}
			console.log(window.history);
			setPageResult();
			uni.navigateBack({ delta: 2 });
		}
	}
}

onMounted(() => {
	window.addEventListener('message', handleMessage);
});

onBeforeUnmount(() => {
	window.removeEventListener('message', handleMessage);
});

// #endif

function handlePostMessage(ev: any) {
	/* #ifdef APP-NVUE */
	const data = ev.detail?.data;
	if (data && data[0]) {
		const cmd = data[0];
		if (cmd.cookie === pq.value?.cookie) {
			if (cmd.bizType && cmd.bizId) {
				notifyQiyuesuoSuccess(cmd.bizId, cmd.bizType);
			} else {
				finishPage();
			}
		}
	}
	/* #endif */
}

function notifyQiyuesuoSuccess(bizId: string, bizType: string) {
	apiPost({ url: 'order/qiyuesuo/sign/success', data: { bizId, bizType } }).catch((err) => {
		console.error(err);
	});
}
</script>

<style scoped lang="scss">
.diw-webview {
	/* #ifdef APP-NVUE */
	flex: 1;
	/* #endif */
}
</style>
