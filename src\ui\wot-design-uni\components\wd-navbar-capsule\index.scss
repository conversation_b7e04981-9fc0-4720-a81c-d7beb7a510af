@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(navbar-capsule) {
		&::before {
			border: 2rpx solid $wot-dark-border-color;
		}

		&::after {
			background: $wot-dark-border-color;
		}

		:deep(.wd-navbar-capsule__icon) {
			color: $wot-dark-color;
		}
	}
}

@include b(navbar-capsule) {
	position: relative;
	box-sizing: border-box;
	width: $wot-navbar-capsule-width;
	height: $wot-navbar-capsule-height;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 200%;
		height: 200%;
		transform: scale(0.5);
		transform-origin: 0 0;
		box-sizing: border-box;
		border-radius: calc($wot-navbar-capsule-border-radius * 2);
		border: 2rpx solid $wot-navbar-capsule-border-color;
	}

	&::after {
		content: '';
		display: block;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translateY(-50%);
		width: 1px;
		height: 36rpx;
		background: $wot-navbar-capsule-border-color;
	}

	&:empty {
		display: none;
	}

	@include edeep(icon) {
		flex: 1;
		position: relative;
		color: $wot-navbar-desc-font-color;
		font-size: $wot-navbar-capsule-icon-size;
	}
}
