@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(radio) {
		@include e(shape) {
			background: transparent;
		}
		@include e(label) {
			color: $wot-dark-color;
		}
		@include when(button) {
			.wd-radio__label {
				background-color: $wot-dark-background;
			}
			@include when(checked) {
				.wd-radio__label {
					background-color: $wot-dark-background2;
				}
			}
		}

		@include when(disabled) {
			.wd-radio__label {
				color: $wot-dark-color-gray;
			}
			@include when(checked) {
				.wd-radio__label {
					color: $wot-dark-color-gray;
				}
			}

			@include when(button) {
				.wd-radio__label {
					border-color: #c8c9cc;
					background: #3a3a3c;
					color: $wot-dark-color-gray;
				}
				@include when(checked) {
					.wd-radio__label {
						border-color: #c8c9cc;
						background: #3a3a3c;
						color: #c8c9cc;
					}
				}
			}

			@include when(dot) {
				.wd-radio__shape {
					border-color: #c8c9cc;
					background: #3a3a3c;
					&::before {
						background-color: #c8c9cc;
					}
				}
			}
		}
	}
}

@include b(radio) {
	display: flex;
	margin-top: $wot-radio-margin;
	justify-content: space-between;
	align-items: center;
	text-align: center;
	line-height: 1.2;

	@include when(first) {
		margin-top: 0;
	}
	@include e(shape) {
		position: relative;
		display: inline-block;
		width: $wot-radio-size;
		height: $wot-radio-size;
		font-size: $wot-radio-size;
		color: transparent;
		display: none;
		vertical-align: middle;
		transition: background 0.2s;
	}
	@include e(input) {
		position: absolute;
		width: 0;
		height: 0;
		margin: 0;
		opacity: 0;
	}
	@include e(label) {
		display: inline-block;
		vertical-align: top;
		font-size: $wot-radio-label-fs;
		color: $wot-radio-label-color;
		line-height: 20px;
		word-break: break-all;
	}
	@include when(checked) {
		.wd-radio__shape {
			color: $wot-radio-checked-color;
			border-color: currentColor;
			display: inline-block;
		}
		.wd-radio__check {
			color: $wot-radio-checked-color;
			opacity: 1;
		}
	}

	@include when(dot) {
		.wd-radio__shape {
			border: 2px solid $wot-radio-dot-border-color;
			border-radius: 50%;
			position: relative;
			display: inline-block;
			box-sizing: border-box;
			transition: none;

			&::before {
				content: '';
				position: absolute;
				width: $wot-radio-dot-size;
				height: $wot-radio-dot-size;
				left: calc(50% - #{$wot-radio-dot-size / 2});
				top: calc(50% - #{$wot-radio-dot-size / 2});
				border-radius: 50%;
				background-color: #fff;
				transform: scale(0);
				transition: transform 0.2s ease-in;
			}
		}
		@include when(checked) {
			.wd-radio__shape {
				background-color: currentColor;
				border-color: currentColor;
				// background-color: $wot-radio-dot-checked-bg;
				// border-color: $wot-radio-dot-checked-border-color;
				&::before {
					transform: scale(1);
				}
			}
		}
	}

	@include when(button) {
		display: inline-block;
		margin-top: 0;
		margin-right: 10px;

		.wd-radio__shape {
			display: none;
		}
		.wd-radio__label {
			height: $wot-radio-button-height;
			min-width: $wot-radio-button-min-width;
			max-width: $wot-radio-button-max-width;
			padding: 5px 15px;
			margin-right: 0;
			border-radius: $wot-radio-button-radius;
			background-color: $wot-radio-button-bg;
			font-size: $wot-radio-button-fs;
			box-sizing: border-box;
			border: 1px solid $wot-radio-button-border;
			transition: all 0.2s;
			@include lineEllipsis;
		}
		@include when(checked) {
			.wd-radio__label {
				color: $wot-radio-checked-color;
				border-color: currentColor;
				background-color: $wot-radio-bg;
			}
		}
	}

	&.icon-placement-left {
		flex-direction: row-reverse;
	}

	@include when(inline) {
		display: inline-block;
		margin-top: 0;
		margin-right: $wot-radio-margin;

		@include when(first) {
			margin-left: 0;
		}
		.wd-radio__shape {
			display: block;
			margin-right: 4px;
			float: left;
			&::after {
				content: '';
				display: table;
				clear: both;
			}
		}

		@include when(dot) {
			.wd-radio__shape {
				margin-top: 2px;
			}
			@include when(large) {
				.wd-radio__shape {
					margin-top: 0;
				}
			}
		}

		&.icon-placement-right {
			.wd-radio__shape {
				margin-right: 0;
				margin-left: 4px;
				float: right;
			}
		}
	}

	@include when(disabled) {
		.wd-radio__label {
			color: $wot-radio-disabled-label-color;
		}
		@include when(checked) {
			.wd-radio__shape {
				color: $wot-radio-disabled-label-color;
			}
			.wd-radio__check {
				color: $wot-radio-disabled-label-color;
			}
			.wd-radio__label {
				color: $wot-radio-disabled-label-color;
			}
		}

		@include when(button) {
			.wd-radio__label {
				border-color: $wot-radio-disabled-color;
				background: $wot-radio-disabled-color;
				border-color: $wot-radio-button-border;
				color: $wot-radio-disabled-label-color;
			}
			@include when(checked) {
				.wd-radio__label {
					border-color: $wot-radio-button-disabled-border;
					background: $wot-radio-disabled-color;
				}
			}
		}

		@include when(dot) {
			.wd-radio__shape {
				background: $wot-radio-dot-disabled-bg;
				border-color: $wot-radio-dot-disabled-border;
			}
		}
	}

	// 以下内容用于解决父子组件样式隔离的问题 —— START
	@include when(cell-radio) {
		padding: 13px 15px;
		margin: 0;

		@include when(large) {
			padding: 14px 15px;
		}
	}

	@include when(button-radio) {
		display: inline-flex;
		width: 33.3333%;
		padding: 12px 12px 0px 0px;
		box-sizing: border-box;

		.wd-radio__label {
			width: 100%;
			max-width: inherit;
		}
	}
	@include when(large) {
		.wd-radio__shape {
			width: $wot-radio-large-size;
			height: $wot-radio-large-size;
			font-size: $wot-radio-large-size;
		}
		.wd-radio__label {
			font-size: $wot-radio-large-label-fs;
		}

		@include when(dot) {
			.wd-radio__shape {
				&::before {
					width: $wot-radio-dot-large-size;
					height: $wot-radio-dot-large-size;
					left: calc(50% - #{$wot-radio-dot-large-size / 2});
					top: calc(50% - #{$wot-radio-dot-large-size / 2});
				}
			}
		}
	}
	// 以下内容用于解决父子组件样式隔离的问题 —— END
}
