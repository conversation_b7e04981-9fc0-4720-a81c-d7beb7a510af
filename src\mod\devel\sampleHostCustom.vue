<template>
	<DIWAppPage>
		<DIWScrollView v-if="pq.mode === 'demoHome1'">
			<DemoHome1 />
		</DIWScrollView>
		<List1 v-else-if="pq.mode === 'list1'" />
	</DIWAppPage>
</template>

<script setup lang="ts">
import DemoHome1 from './components/demoHome1.vue';
import List1 from './components/list1.vue';

const props = defineProps<{ mode: string }>();

const { usePageQuery } = useFramework();

const pq = usePageQuery();
</script>
