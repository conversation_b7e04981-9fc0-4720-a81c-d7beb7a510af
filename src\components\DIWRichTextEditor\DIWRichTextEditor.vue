<template>
	<rich-text :nodes="richDoc" />
</template>

<script setup lang="ts">
import { appconfig } from '@/config/appconfig';

const props = withDefaults(defineProps<{ readonly?: boolean }>(), { readonly: false });
const model = defineModel<Array<Record<string, any>>>();

const richDoc = computed(() => {
	return formatRichText(model.value);
});

function convertImageUrl(url: string) {
	if (url.startsWith('/')) {
		return appconfig.apiBaseUrl + url.substring(1);
	}
	return url;
}

function visit(parent: any[], level: number, n: any) {
	if (typeof n.type === 'string') {
		const k: any = { type: 'node', name: 'div' };
		switch (n.type) {
			case 'paragraph':
				k.name = 'p';
				break;

			case 'image':
				k.name = 'img';
				console.log('image', n.src);
				k.attrs = { src: convertImageUrl(n.src), class: 're-image' };
				break;

			case 'header1':
				k.name = 'h1';
				break;

			case 'header2':
				k.name = 'h2';
				break;

			case 'header3':
				k.name = 'h3';
				break;

			case 'header4':
				k.name = 'h4';
				break;

			case 'header5':
				k.name = 'h5';
				break;

			case 'header6':
				k.name = 'h6';
				break;

			case 'link':
				k.name = 'a';
				break;

			case 'list-item':
				k.name = 'li';
				break;

			case 'blockquote':
				k.name = 'blockquote';
				break;

			default:
				console.warn('tt', n);
				break;
		}

		const cc: any[] = [];
		if (Array.isArray(n.children)) {
			n.children.forEach((c: any) => visit(cc, level + 1, c));
		}

		const dd = cc.filter((e) => !!e);
		if (dd.length > 0) {
			k.children = cc;
		}

		parent.push(k);
	} else if (typeof n.text === 'string') {
		parent.push({ type: 'text', text: n.text });
	}
}

function formatRichText(content: any) {
	const result: any[] = [];
	if (Array.isArray(content)) {
		content.forEach((n) => visit(result, 0, n));
	}

	return result;
}
</script>
