@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(checkbox) {
		@include e(shape) {
			background: transparent;
			border-color: $wot-checkbox-border-color;
			color: $wot-checkbox-check-color;
		}

		@include e(label) {
			color: $wot-dark-color;
		}

		@include when(disabled) {
			.wd-checkbox__shape {
				border-color: $wot-dark-color-gray;
				background: $wot-dark-background4;
			}

			.wd-checkbox__label {
				color: $wot-dark-color-gray;
			}

			:deep(.wd-checkbox__check) {
				color: $wot-dark-color-gray;
			}

			@include when(checked) {
				.wd-checkbox__shape {
					color: $wot-dark-color-gray;
				}

				.wd-checkbox__label {
					color: $wot-dark-color-gray;
				}
			}

			@include when(button) {
				.wd-checkbox__label {
					border-color: #c8c9cc;
					background: #3a3a3c;
					color: $wot-dark-color-gray;
				}

				@include when(checked) {
					.wd-checkbox__label {
						border-color: #c8c9cc;
						background: #3a3a3c;
						color: #c8c9cc;
					}
				}
			}
		}

		@include when(button) {
			.wd-checkbox__label {
				background-color: $wot-dark-background;
			}

			@include when(checked) {
				.wd-checkbox__label {
					background-color: $wot-dark-background2;
				}
			}
		}
	}
}

@include b(checkbox) {
	display: block;
	margin-bottom: $wot-checkbox-margin;
	font-size: 0;
	-webkit-tap-highlight-color: transparent;
	line-height: 1.2;

	@include when(last-child) {
		margin-bottom: 0;
	}

	@include e(shape) {
		position: relative;
		display: inline-block;
		width: $wot-checkbox-size;
		height: $wot-checkbox-size;
		border: 2px solid $wot-checkbox-border-color;
		border-radius: 50%;
		color: $wot-checkbox-check-color;
		background: $wot-checkbox-bg;
		vertical-align: middle;
		transition: background 0.2s;
		box-sizing: border-box;

		@include when(square) {
			border-radius: $wot-checkbox-square-radius;
		}
	}

	@include e(input) {
		position: absolute;
		width: 0;
		height: 0;
		margin: 0;
		opacity: 0;
	}

	@include edeep(btn-check) {
		display: inline-block;
		font-size: $wot-checkbox-icon-size;
		margin-right: 4px;
		vertical-align: middle;
	}

	@include e(txt) {
		display: inline-block;
		vertical-align: middle;
		line-height: 20px;
		@include lineEllipsis;
	}

	@include e(label) {
		position: relative;
		display: inline-block;
		margin-left: $wot-checkbox-label-margin;
		vertical-align: middle;
		font-size: $wot-checkbox-label-fs;
		color: $wot-checkbox-label-color;
	}

	@include edeep(check) {
		color: $wot-checkbox-check-color;
		font-size: $wot-checkbox-icon-size;
		opacity: 0;
		transition: opacity 0.2s;
	}

	@include when(checked) {
		.wd-checkbox__shape {
			color: $wot-checkbox-checked-color;
			background: currentColor;
			border-color: currentColor;
		}

		:deep(.wd-checkbox__check) {
			opacity: 1;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}
	}

	@include when(button) {
		display: inline-block;
		margin-bottom: 0;
		margin-right: $wot-checkbox-margin;
		vertical-align: top;
		font-size: $wot-checkbox-button-font-size;

		@include when(last-child) {
			margin-right: 0;
		}

		.wd-checkbox__shape {
			width: 0;
			height: 0;
			overflow: hidden;
			opacity: 0;
			border: none;
		}

		.wd-checkbox__label {
			display: inline-flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			min-width: $wot-checkbox-button-min-width;
			height: $wot-checkbox-button-height;
			font-size: $wot-checkbox-button-font-size;
			margin-left: 0;
			padding: 5px 15px;
			border: 1px solid $wot-checkbox-button-border;
			background-color: $wot-checkbox-button-bg;
			border-radius: $wot-checkbox-button-radius;
			transition:
				color 0.2s,
				border 0.2s;
			box-sizing: border-box;
		}

		@include when(checked) {
			.wd-checkbox__label {
				color: $wot-checkbox-checked-color;
				background-color: $wot-checkbox-bg;
				border-color: $wot-checkbox-checked-color;
				border-color: currentColor;
			}
		}
	}

	@include when(inline) {
		display: inline-block;
		margin-bottom: 0;
		margin-right: $wot-checkbox-margin;

		@include when(last-child) {
			margin-right: 0;
		}
	}

	@include when(disabled) {
		.wd-checkbox__shape {
			border-color: $wot-checkbox-border-color;
			background: $wot-checkbox-disabled-check-bg;
		}

		.wd-checkbox__label {
			color: $wot-checkbox-disabled-label-color;
		}

		@include when(checked) {
			.wd-checkbox__shape {
				color: $wot-checkbox-disabled-check-color;
			}

			.wd-checkbox__label {
				color: $wot-checkbox-disabled-label-color;
			}
		}

		@include when(button) {
			.wd-checkbox__label {
				background: $wot-checkbox-disabled-color;
				border-color: $wot-checkbox-button-border;
				color: $wot-checkbox-disabled-label-color;
			}

			@include when(checked) {
				.wd-checkbox__label {
					border-color: $wot-checkbox-button-disabled-border;
				}
			}
		}
	}

	// 以下内容用于解决父子组件样式隔离的问题 —— START
	@include when(cell-box) {
		padding: 13px 15px;
		margin: 0;

		@include when(large) {
			padding: 14px 15px;
		}
	}

	@include when(button-box) {
		display: inline-flex;
		width: 33.3333%;
		padding: 12px 12px 0 0;
		box-sizing: border-box;

		.wd-checkbox__label {
			width: 100%;
		}

		&:last-child::after {
			content: '';
			display: table;
			clear: both;
		}
	}

	@include when(large) {
		.wd-checkbox__shape {
			width: $wot-checkbox-large-size;
			height: $wot-checkbox-large-size;
			font-size: $wot-checkbox-large-size;
		}

		.wd-checkbox__label {
			font-size: $wot-checkbox-large-label-fs;
		}
	}
}
