@use '../common/abstracts/_mixin.scss' as *;
@use '../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(search) {
		background: $wot-dark-background4;

		@include e(block) {
			background-color: $wot-dark-background;
		}

		@include e(input) {
			color: $wot-dark-color;
		}

		@include e(cover) {
			background-color: $wot-dark-background;
		}

		@include e(search-icon) {
			color: $wot-dark-color;
		}
		@include e(search-left-icon) {
			color: $wot-dark-color;
		}
		@include e(clear) {
			color: $wot-dark-color;
		}
		@include e(cancel) {
			color: $wot-dark-color;
		}

		@include when(light) {
			background: $wot-dark-background4;

			.wd-search__block {
				background: $wot-dark-background7;
			}

			.wd-search__cover {
				background: $wot-dark-background7;
			}
		}
	}
}

@include b(search) {
	display: flex;
	padding: $wot-search-padding;
	align-items: center;
	background: #fff;

	@include e(block) {
		flex: 1;
		background-color: $wot-search-input-bg;
		border-radius: $wot-search-input-radius;
		display: flex;
		flex-direction: row;
		align-items: center;
		position: relative;
	}
	@include e(field) {
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		position: relative;
	}
	@include e(input) {
		flex: 1;
		height: $wot-search-input-height;
		box-sizing: border-box;
		padding: $wot-search-input-padding;
		border: none;
		background: transparent;
		font-size: $wot-search-input-fs;
		-webkit-appearance: none;
		outline: none;
		color: $wot-search-input-color;
		z-index: 0;

		@include lineEllipsis;

		&::-webkit-search-cancel-button {
			-webkit-appearance: none;
		}
	}
	@include e(cover) {
		width: 100%;
		height: $wot-search-input-height;
		background-color: $wot-search-input-bg;
		line-height: $wot-search-input-height;
		font-size: $wot-search-input-fs;
		border-radius: $wot-search-input-radius;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}
	@include edeep(search-icon) {
		margin-right: 8px;
		color: $wot-search-icon-color;
		font-size: $wot-search-icon-size;
	}
	@include edeep(search-left-icon) {
		position: absolute;
		font-size: $wot-search-icon-size;
		top: 50%;
		left: 16px;
		transform: translateY(-50%);
		color: $wot-search-icon-color;
	}
	@include e(placeholder-txt) {
		color: $wot-search-placeholder-color;
		font-size: $wot-search-input-fs;
	}
	@include edeep(clear) {
		position: absolute;
		right: 0;
		padding: 6px 9px 6px 7px;
		color: $wot-search-cancel-color;
	}
	@include edeep(clear-icon) {
		vertical-align: middle;
		font-size: $wot-search-clear-icon-size;
	}
	@include e(cancel) {
		padding: $wot-search-cancel-padding;
		height: $wot-search-input-height;
		line-height: $wot-search-input-height;
		font-size: $wot-search-cancel-fs;
		color: $wot-search-cancel-color;
		-webkit-tap-highlight-color: transparent;
	}
	@include when(light) {
		background: $wot-search-light-bg;

		.wd-search__block {
			background: #fff;
		}

		.wd-search__cover {
			background: #fff;
		}
	}
	@include when(without-cancel) {
		padding-right: $wot-search-side-padding;
	}
}
