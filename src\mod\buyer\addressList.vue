<template>
	<DIWAppPage mode="1" :title="title">
		<DIWScrollView>
			<DIWListView ref="listView" :meta="listViewMeta">
				<template #default="{ data }">
					<DIWListItem @click="openDetail(data)">
						<template #header>
							<view class="flex flex-row justify-between items-center">
								<view class="flex flex-col">
									<text>{{ data.addressDetail }}</text>
									<!--
									<text class="text-sm color-gray">邮编：{{ data.zipCode }}</text>
									-->
									<text class="text-sm color-gray">收货人：{{ data.name }}</text>
								</view>
								<wd-tag v-if="data.defaultFlag" type="primary" round custom-class="h-fit whitespace-nowrap">默认</wd-tag>
							</view>
						</template>
						<view>
							<text>📞 {{ data.phone }}</text>
						</view>
						<view>
							<text>🏭 {{ data.tel }}</text>
						</view>
					</DIWListItem>
				</template>
			</DIWListView>
		</DIWScrollView>
		<template #bottomBar>
			<DIWAuth v-if="!isSelectMode" auth="admin_address_info_add">
				<DIWBottomBar>
					<view class="flex flex-row items-center gap-4 ml-auto">
						<wd-button @click="openDetail()">添加收货地址</wd-button>
					</view>
				</DIWBottomBar>
			</DIWAuth>
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { apiGet, protect, invokeTo, usePageQuery, usePageResult } = useFramework();

const pq = usePageQuery();
const { finishPage } = usePageResult();

const isSelectMode = ref(false);

watchEffect(() => {
	isSelectMode.value = pq.value.mode === 'select';
});

const title = computed(() => {
	if (isSelectMode.value) {
		if (pq.value.ct) {
			return pq.value.ct;
		}
	}

	return '收货地址';
});

const listView = ref();

const searchModel = ref({});

const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(trackInfo) {
			const size1 = 20;
			const size2 = 5;

			const isReload = trackInfo === undefined;

			let current = isReload ? 1 : trackInfo;
			let size = isReload ? size1 : size2;

			const d = await apiGet({
				url: 'admin/address/info/page',
				params: {
					current,
					size,
					...searchModel.value,
				},
			});

			const items = d.records;

			if (isReload) {
				return { hasMore: d.total > size, items, trackInfo: 1 + size1 / size2 };
			}

			return { hasMore: d.total > current * size, items, trackInfo: current + 1 };
		},
	});
});

function openDetail(data?: Record<string, any>) {
	if (isSelectMode.value) {
		finishPage(data!);
		return;
	}

	protect(async () => {
		await invokeTo({ url: '/mod/buyer/addressDetail', params: { id: data?.id } });
		listView.value.reload();
	});
}
</script>
