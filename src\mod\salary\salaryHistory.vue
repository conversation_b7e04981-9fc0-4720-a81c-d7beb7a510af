<template>
	<DIWAppPage title="%salaryHistory%">
		<DIWScrollView>
			<view class="salary-history-container">
				<!-- 顶部月份和工资信息 -->
				<view class="month-salary-header">
					<view class="month-info">
						<text class="month-icon">📅</text>
						<text class="month-text">{{ getCurrentMonthSimple() }}</text>
					</view>
					<view class="salary-info">
						<text class="salary-label">{{ t('currentMonthEstimatedSalary') }}：</text>
						<text class="salary-amount">{{ formatNumber(currentMonthSalary, 2) }}</text>
						<text class="salary-unit">{{ t('yuan') }}</text>
					</view>
				</view>

				<!-- 日工资记录列表 -->
				<view class="daily-records">
					<view class="daily-records__header">
						<view class="header-item">{{ t('date') }}</view>
						<view class="header-item">{{ t('shift') }}</view>
						<view class="header-item">{{ t('currentShiftEstimatedIncome') }}</view>
						<view class="header-item">{{ t('standCount') }}</view>
					</view>

					<view class="daily-records__list">
						<view v-for="(item, index) in dailyRecords" :key="index" class="daily-record-item">
							<view class="record-field">{{ item.date }}</view>
							<view class="record-field">
								<text class="shift-badge" :class="item.shiftType === 'morning' ? 'shift-morning' : 'shift-evening'">
									{{ item.shiftType === 'morning' ? t('morningShift') : t('eveningShift') }}
								</text>
							</view>
							<view class="record-field amount-field">{{ formatNumber(item.income, 2) }}</view>
							<view class="record-field">{{ item.standCount }}</view>
						</view>
					</view>
				</view>

				<!-- 历史工资记录 -->
				<view class="history-section">
					<view class="history-section__header">
						<text class="history-section__title">{{ t('historySalaryRecord') }}</text>
						<view class="year-selector">
							<text class="year-text">{{ selectedYear }}{{ t('year') }}</text>
							<text class="year-arrow">▼</text>
						</view>
					</view>

					<!-- 月份工资列表 -->
					<view class="monthly-list">
						<view v-for="(item, index) in monthlyRecords" :key="index" class="monthly-item" @click="handleMonthClick(item)">
							<view class="monthly-item__left">
								<text class="monthly-item__month">{{ item.month }}{{ t('month') }}</text>
								<text class="monthly-item__income">{{ t('monthlyIncome') }}: {{ formatNumber(item.income, 2) }}{{ t('yuan') }}</text>
							</view>
							<view class="monthly-item__right">
								<view class="monthly-item__details">
									<text class="detail-item">{{ t('monthlyReward') }}: {{ formatNumber(item.reward, 2) }}{{ t('yuan') }}</text>
									<text class="detail-item">{{ t('monthlyPunishment') }}: {{ formatNumber(item.punishment, 2) }}{{ t('yuan') }}</text>
								</view>
								<view class="monthly-item__action">
									<text class="action-text">{{ t('viewDetails') }}</text>
									<text class="action-arrow">▶</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 收入趋势图 -->
					<view class="income-chart">
						<text class="income-chart__title">{{ t('incomeTrend') }}</text>
						<view class="chart-container">
							<canvas canvas-id="incomeChart" class="chart-canvas" :style="{ width: chartWidth + 'rpx', height: chartHeight + 'rpx' }"></canvas>
						</view>
						<view class="chart-legend">
							<view v-for="(month, index) in chartMonths" :key="index" class="legend-item">
								<text class="legend-text">{{ month }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 确定按钮 -->
				<view class="confirm-section">
					<wd-button type="primary" custom-class="confirm-button" size="large" @click="handleConfirm">{{ t('confirm') }}</wd-button>
				</view>
			</view>
		</DIWScrollView>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { formatNumber } from '@/util/number';

const { protect, navigateTo, navigateBack } = useFramework();

// 使用国际化
const { t } = useI18n();

// 当月预估工资
const currentMonthSalary = ref(5428.65);
const selectedYear = ref(2025);

// 日工资记录数据
const dailyRecords = ref([
	{ date: '7-01', shiftType: 'morning', income: 298.65, standCount: 5 },
	{ date: '7-02', shiftType: 'morning', income: 298.65, standCount: 6 },
	{ date: '7-04', shiftType: 'evening', income: 298.65, standCount: 5 },
	{ date: '7-05', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '7-06', shiftType: 'evening', income: 298.65, standCount: 5 },
	{ date: '7-08', shiftType: 'morning', income: 298.65, standCount: 6 },
	{ date: '7-09', shiftType: 'morning', income: 298.65, standCount: 5 },
	{ date: '7-10', shiftType: 'evening', income: 298.65, standCount: 3 },
	{ date: '7-12', shiftType: 'morning', income: 298.65, standCount: 5 },
	{ date: '7-13', shiftType: 'morning', income: 298.65, standCount: 6 },
	{ date: '7-14', shiftType: 'evening', income: 298.65, standCount: 5 },
	{ date: '7-15', shiftType: 'morning', income: 298.65, standCount: 4 },
]);

// 月度工资记录数据
const monthlyRecords = ref([
	{ month: '06', income: 6985.34, reward: 260.5, punishment: 30 },
	{ month: '05', income: 7245.15, reward: 185, punishment: 20 },
	{ month: '04', income: 7018.26, reward: 260.5, punishment: 10 },
	{ month: '03', income: 6925.34, reward: 155, punishment: 50 },
	{ month: '02', income: 6985.34, reward: 165.6, punishment: 30 },
	{ month: '01', income: 7375.26, reward: 215.8, punishment: 40 },
]);

// 图表相关数据
const chartWidth = ref(702);
const chartHeight = ref(300);
const chartMonths = ref(['1月', '2月', '3月', '4月', '5月', '6月', '7月']);

// 获取当前月份显示
function getCurrentMonthSimple() {
	const date = new Date();
	return `${date.getFullYear()}年${date.getMonth() + 1}月`;
}

// 处理月份点击
function handleMonthClick(item: any) {
	protect(async () => {
		await navigateTo({
			url: `/mod/salary/monthlySalary?year=${selectedYear.value}&month=${item.month}`,
		});
	});
}

// 处理确定按钮点击
function handleConfirm() {
	navigateBack();
}

// 页面加载时初始化图表
onMounted(() => {
	nextTick(() => {
		initChart();
	});
});

// 初始化图表
function initChart() {
	const ctx = uni.createCanvasContext('incomeChart');

	// 设置图表基本属性
	const padding = 60;
	const chartArea = {
		x: padding,
		y: padding,
		width: chartWidth.value - padding * 2,
		height: chartHeight.value - padding * 2,
	};

	// 收入数据
	const incomeData = [7375.26, 6985.34, 6925.34, 7018.26, 7245.15, 6985.34, 5428.65];
	const maxIncome = Math.max(...incomeData);
	const minIncome = Math.min(...incomeData);
	const range = maxIncome - minIncome;

	// 绘制背景
	ctx.setFillStyle('#f8f9fa');
	ctx.fillRect(0, 0, chartWidth.value, chartHeight.value);

	// 绘制网格线
	ctx.setStrokeStyle('#e5e6eb');
	ctx.setLineWidth(1);

	// 水平网格线
	for (let i = 0; i <= 5; i++) {
		const y = chartArea.y + (chartArea.height / 5) * i;
		ctx.beginPath();
		ctx.moveTo(chartArea.x, y);
		ctx.lineTo(chartArea.x + chartArea.width, y);
		ctx.stroke();
	}

	// 垂直网格线
	for (let i = 0; i <= 6; i++) {
		const x = chartArea.x + (chartArea.width / 6) * i;
		ctx.beginPath();
		ctx.moveTo(x, chartArea.y);
		ctx.lineTo(x, chartArea.y + chartArea.height);
		ctx.stroke();
	}

	// 绘制折线图
	ctx.setStrokeStyle('#2b5aed');
	ctx.setLineWidth(2);
	ctx.beginPath();

	incomeData.forEach((income, index) => {
		const x = chartArea.x + (chartArea.width / 6) * index;
		const y = chartArea.y + chartArea.height - ((income - minIncome) / range) * chartArea.height;

		if (index === 0) {
			ctx.moveTo(x, y);
		} else {
			ctx.lineTo(x, y);
		}
	});

	ctx.stroke();

	// 绘制数据点
	ctx.setFillStyle('#2b5aed');
	incomeData.forEach((income, index) => {
		const x = chartArea.x + (chartArea.width / 6) * index;
		const y = chartArea.y + chartArea.height - ((income - minIncome) / range) * chartArea.height;

		ctx.beginPath();
		ctx.arc(x, y, 3, 0, 2 * Math.PI);
		ctx.fill();
	});

	ctx.draw();
}
</script>

<style lang="scss" scoped>
.salary-history-container {
	padding: 24rpx;
	background-color: #f5f7fa;
	min-height: 100vh;
}

/* 顶部月份和工资信息样式 */
.month-salary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

	.month-info {
		display: flex;
		align-items: center;

		.month-icon {
			font-size: 32rpx;
			margin-right: 12rpx;
		}

		.month-text {
			font-size: 32rpx;
			font-weight: 600;
			color: #1d2129;
		}
	}

	.salary-info {
		display: flex;
		align-items: baseline;

		.salary-label {
			font-size: 28rpx;
			color: #86909c;
		}

		.salary-amount {
			font-size: 32rpx;
			font-weight: 700;
			color: #1d2129;
			margin-left: 8rpx;
		}

		.salary-unit {
			font-size: 28rpx;
			font-weight: 500;
			color: #1d2129;
			margin-left: 4rpx;
		}
	}
}

/* 日工资记录样式 */
.daily-records {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;

	&__header {
		display: flex;
		background-color: #f8f9fa;
		padding: 20rpx 24rpx;
		border-bottom: 1rpx solid #e5e7eb;

		.header-item {
			flex: 1;
			text-align: center;
			font-size: 26rpx;
			font-weight: 600;
			color: #374151;
		}
	}
}

.daily-record-item {
	display: flex;
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #f3f4f6;

	&:last-child {
		border-bottom: none;
	}

	.record-field {
		flex: 1;
		text-align: center;
		font-size: 24rpx;
		color: #374151;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 60rpx;
	}

	.amount-field {
		font-weight: 600;
		color: #1f2937;
	}
}

.shift-badge {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.shift-morning {
	background-color: #dbeafe;
	color: #1d4ed8;
}

.shift-evening {
	background-color: #fef3c7;
	color: #d97706;
}

/* 历史记录部分样式 */
.history-section {
	background-color: #ffffff;
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);

	&__header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 32rpx;
	}

	&__title {
		font-size: 32rpx;
		font-weight: 600;
		color: #1d2129;
	}
}

.year-selector {
	display: flex;
	align-items: center;
	padding: 8rpx 24rpx;
	background-color: #f2f3f5;
	border-radius: 32rpx;

	.year-text {
		font-size: 28rpx;
		color: #1d2129;
		margin-right: 8rpx;
	}

	.year-arrow {
		font-size: 20rpx;
		color: #86909c;
	}
}

/* 月度列表样式 */
.monthly-list {
	margin-bottom: 32rpx;
}

.monthly-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 2rpx solid #f2f3f5;

	&:last-child {
		border-bottom: none;
	}

	&__left {
		flex: 1;
	}

	&__month {
		font-size: 30rpx;
		font-weight: 600;
		color: #1d2129;
		margin-bottom: 8rpx;
		display: block;
	}

	&__income {
		font-size: 26rpx;
		color: #86909c;
		display: block;
	}

	&__right {
		display: flex;
		align-items: center;
	}

	&__details {
		margin-right: 24rpx;
		text-align: right;

		.detail-item {
			font-size: 24rpx;
			color: #86909c;
			margin-bottom: 4rpx;
			display: block;
		}
	}

	&__action {
		display: flex;
		align-items: center;

		.action-text {
			font-size: 26rpx;
			color: #2b5aed;
			margin-right: 8rpx;
		}

		.action-arrow {
			font-size: 20rpx;
			color: #2b5aed;
		}
	}
}

/* 收入趋势图样式 */
.income-chart {
	&__title {
		font-size: 30rpx;
		font-weight: 600;
		color: #1d2129;
		margin-bottom: 24rpx;
		display: block;
	}
}

.chart-container {
	display: flex;
	justify-content: center;
	margin-bottom: 16rpx;
}

.chart-canvas {
	background-color: #ffffff;
	border-radius: 8rpx;
}

.chart-legend {
	display: flex;
	justify-content: space-around;
	padding: 0 60rpx;

	.legend-item {
		text-align: center;
	}

	.legend-text {
		font-size: 24rpx;
		color: #86909c;
	}
}

/* 确定按钮样式 */
.confirm-section {
	padding: 90rpx 0 24rpx 0;
}

.confirm-button {
	width: 100%;
	background: linear-gradient(90deg, #2b6fea 0%, #4397fe 100%);
	border-radius: 42rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 32rpx;
	letter-spacing: 0em;
	/* 纯白 */
	color: #ffffff;
}
</style>
