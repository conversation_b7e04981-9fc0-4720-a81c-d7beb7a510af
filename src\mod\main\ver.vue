<template>
	<view class="absolute w-full h-full flex flex-col">
		<text class="mt-80px text-xl self-center">数智云平台</text>
		<text class="mt-4 text-gray self-center">{{ versionText }}</text>
		<view class="flex-1" />
		<view v-if="sessionInfo">
			<view class="flex flex-row flex-wrap gap-2 px-4 pb-8">
				<text class="text-xs text-blue-500" @click="viewDoc(5, '隐私协议')">《隐私协议》</text>
				<text class="text-xs text-blue-500" @click="viewDoc(6, '隐私协议')">《隐私协议》</text>

				<text class="text-xs text-blue-500" @click="viewDoc(7, '服务协议')">《服务协议》</text>
				<text class="text-xs text-blue-500" @click="viewDoc(4, '平台商家入驻服务协议')">《平台商家入驻服务协议》</text>

				<text class="text-xs text-blue-500" @click="viewDoc(8, '青少年个人信息保护规则')">《青少年个人信息保护规则》</text>
				<text class="text-xs text-blue-500" @click="viewDoc(2, '监督申明')">《监督申明》</text>

				<text class="text-xs text-blue-500" @click="viewLic()">《证照信息》</text>
			</view>
		</view>
		<wd-gap safe-area-bottom height="0" />
	</view>
</template>

<script setup lang="ts">
const { sessionInfo, protect, getAgreementByType, invokeTo, navigateTo } = useFramework();

const versionText = computed(() => {
	return uni.getAppBaseInfo().appVersion;
});

function viewDoc(type: number, title: string) {
	protect(async () => {
		const d = await getAgreementByType(`${type}`);
		await invokeTo({ url: '/mod/common/richTextViewer', data: { content: d, title: title } });
	});
}

function viewLic() {
	protect(async () => {
		await navigateTo('/mod/main/lic');
	});
}
</script>
