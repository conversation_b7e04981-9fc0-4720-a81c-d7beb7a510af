@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

@include b(toast) {
	display: inline-block;
	max-width: $wot-toast-max-width;
	padding: $wot-toast-padding;
	background-color: $wot-toast-bg;
	border-radius: $wot-toast-radius;
	color: #fff;
	transition: all 0.2s;
	font-size: $wot-toast-fs;
	box-sizing: border-box;
	box-shadow: $wot-toast-box-shadow;

	@include when(vertical) {
		flex-direction: column;
	}

	@include e(msg) {
		font-size: $wot-toast-fs;
		word-break: break-all;
		line-height: $wot-toast-line-height;
		text-align: left;
		font-family: 'San Francisco', Rotobo, arial, 'PingFang SC', 'Noto SansCJK', 'Microsoft Yahei', sans-serif;
	}
	@include edeep(icon) {
		display: inline-block;
		margin-right: $wot-toast-icon-margin-right;
		font-size: $wot-toast-icon-size;

		@include when(vertical) {
			margin-right: 0;
			margin-bottom: $wot-toast-icon-margin-bottom;
		}
	}
	@include e(iconWrap) {
		font-size: 0;
		line-height: 0;
		vertical-align: middle;
	}
	@include e(iconBox) {
		display: block;
		width: 100%;
		height: 100%;
	}
	@include e(iconSvg) {
		width: $wot-toast-icon-size;
		height: $wot-toast-icon-size;
		background-size: cover;
		background-repeat: no-repeat;
	}
	@include e(loading) {
		margin-bottom: $wot-toast-loading-margin-bottom;
		display: inline-block;
	}
	@include m(top) {
		transform: translate3d(0, -40vh, 0);
	}
	@include m(middle-top) {
		transform: translate3d(0%, -18.8vh, 0);
	}
	@include m(bottom) {
		transform: translate3d(0, 40vh, 0);
	}
	@include m(with-icon) {
		min-width: $wot-toast-with-icon-min-width;
		display: inline-flex;
		align-items: center;
	}
	@include m(loading) {
		min-width: auto;
		padding: $wot-toast-loading-padding;
	}
}
