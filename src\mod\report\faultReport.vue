<!-- 故障报修页面 -->
<template>
	<DIWAppPage mode="1" title="故障报修">
		<template #appBar>
			<wd-navbar
				:title="t('faultReportTitle')"
				left-arrow
				@click-left="navigateBack()"
				:border="false"
				:fixed="true"
				:custom-class="'wd-navbar-custom'"
				placeholder
			>
				<template #right>
					<view @click="goToHistory">
						{{ t('historyRecord') }}
					</view>
				</template>
			</wd-navbar>
		</template>

		<view class="fault-report-container mt-108rpx">
			<view class="form-wrapper">
				<wd-form ref="formRef" class="pt-28rpx mx-24rpx" :model="formData" :rules="rules">
					<!-- 机台号 -->
					<view class="wd-form-item">
						<view class="wd-form-item__title">
							<text>机台号</text>
							<text class="required-star">*</text>
						</view>
						<view class="wd-form-item__content h-80rpx">
							<wd-input border="none" v-model="formData.machineNumber" :placeholder="t('pleaseInputMachineNumber')" clearable />
						</view>
					</view>

					<!-- 故障类型 -->
					<view class="wd-form-item">
						<view class="wd-form-item__title">
							<text>故障类型</text>
							<text class="required-star">*</text>
						</view>
						<view class="wd-form-item__content h-80rpx">
							<view class="picker-input" @click="showFaultTypePicker = true">
								<wd-picker
									v-model="selectedFaultType"
									:placeholder="formData.faultType || t('pleaseSelectFaultType')"
									:columns="faultTypeOptions"
									:show="showFaultTypePicker"
									@confirm="onFaultTypeConfirm"
									@cancel="showFaultTypePicker = false"
								/>
							</view>
						</view>
					</view>

					<!-- 故障描述 -->
					<view class="wd-form-item">
						<view class="wd-form-item__title">
							<text>故障描述</text>
							<text class="required-star">*</text>
						</view>
						<view class="wd-form-item__content">
							<wd-textarea
								v-model="formData.faultDescription"
								:placeholder="t('faultDescriptionPlaceholder')"
								:rows="2"
								:maxlength="500"
								show-word-limit
								clearable
							/>
						</view>
					</view>

					<!-- 故障图片 -->
					<view class="wd-form-item">
						<view class="wd-form-item__title">
							<text>故障图片</text>
						</view>
						<view class="wd-form-item__content">
							<view class="upload-container">
								<view v-for="(image, index) in formData.images" :key="index" class="image-item">
									<image :src="image" class="fault-image" mode="aspectFill" />
									<view class="delete-icon" @click="removeImage(index)">
										<wd-icon name="close" size="16rpx" color="#fff" />
									</view>
								</view>
								<view v-if="formData.images.length < 6" class="upload-btn" @click="chooseImage">
									<wd-icon name="add" size="48rpx" color="#ddd" />
								</view>
							</view>
						</view>
					</view>
				</wd-form>
			</view>

			<!-- 语音输入区域 -->
			<view class="voice-section">
				<view class="voice-container">
					<view class="voice-btn" :class="{ recording: isRecording }" @touchstart="startRecord" @touchend="stopRecord" @touchcancel="stopRecord">
						<wd-icon name="mic" size="48rpx" color="#fff" />
					</view>
					<view class="voice-tip">
						{{ isRecording ? t('recording') : t('clickToSpeakFaultDescription') }}
					</view>
				</view>
				<view v-if="recordDuration > 0" class="voice-duration">
					<wd-icon name="volume" size="28rpx" color="#1890ff" />
					<text class="duration-text">{{ formatDuration(recordDuration) }}</text>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<wd-button type="primary" size="large" :loading="submitting" @click="handleSubmit" block>
					{{ t('submit') }}
				</wd-button>
			</view>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { navigateTo, navigateBack, usePageQuery } = useFramework();
const pq = usePageQuery();

// 表单数据
const formData = reactive({
	machineNumber: '',
	faultType: '',
	faultDescription: '',
	images: [] as string[],
	voiceFile: '' as string,
});

// 表单验证规则
const rules = {
	machineNumber: [
		{
			required: true,
			message: t('pleaseInputMachineNumber'),
		},
	],
	faultType: [
		{
			required: true,
			message: t('pleaseSelectFaultType'),
		},
	],
	faultDescription: [
		{
			required: true,
			message: t('pleaseInputFaultDescription'),
		},
	],
};

// 故障类型选项
const faultTypeOptions = [
	{
		value: 'stopMachine',
		label: t('stopMachine'),
	},
	{
		value: 'yarnBreak',
		label: t('yarnBreak'),
	},
	{
		value: 'mechanicalFault',
		label: t('mechanicalFault'),
	},
	{
		value: 'electricalFault',
		label: t('electricalFault'),
	},
	{
		value: 'qualityIssue',
		label: t('qualityIssue'),
	},
	{
		value: 'other',
		label: t('other'),
	},
];

// 故障类型选择器状态
const showFaultTypePicker = ref(false);
const selectedFaultType = ref('');

// 语音录制状态
const isRecording = ref(false);
const recordDuration = ref(0);
const recordTimer = ref<number | null>(null);

// 提交状态
const submitting = ref(false);

// 表单引用
const formRef = ref();

// 选择故障类型
const onFaultTypeConfirm = (event: any) => {
	const selectedItem = faultTypeOptions.find((item) => item.value === event.value);
	if (selectedItem) {
		formData.faultType = selectedItem.label;
		selectedFaultType.value = event.value;
	}
	showFaultTypePicker.value = false;
};

// 选择图片
const chooseImage = () => {
	uni.chooseImage({
		count: 6 - formData.images.length,
		sizeType: ['compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			formData.images.push(...res.tempFilePaths);
		},
		fail: (err) => {
			console.error('选择图片失败:', err);
		},
	});
};

// 删除图片
const removeImage = (index: number) => {
	formData.images.splice(index, 1);
};

// 开始录音
const startRecord = () => {
	isRecording.value = true;
	recordDuration.value = 0;

	// 开始录音
	uni.startRecord({
		success: (res) => {
			formData.voiceFile = res.tempFilePath;
		},
		fail: (err) => {
			console.error('录音失败:', err);
			isRecording.value = false;
		},
	});

	// 开始计时
	recordTimer.value = setInterval(() => {
		if (isRecording.value) {
			recordDuration.value++;
		} else {
			if (recordTimer.value) {
				clearInterval(recordTimer.value);
				recordTimer.value = null;
			}
		}
	}, 1000);
};

// 停止录音
const stopRecord = () => {
	if (isRecording.value) {
		isRecording.value = false;
		uni.stopRecord();

		// 清除计时器
		if (recordTimer.value) {
			clearInterval(recordTimer.value);
			recordTimer.value = null;
		}
	}
};

// 格式化录制时长
const formatDuration = (seconds: number) => {
	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = seconds % 60;
	return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 提交表单
const handleSubmit = async () => {
	try {
		await formRef.value.validate();
		submitting.value = true;

		// 这里添加提交逻辑
		await submitFaultReport();

		uni.showToast({
			title: '提交成功',
			icon: 'success',
		});

		// 提交成功后返回
		setTimeout(() => {
			navigateBack();
		}, 1500);
	} catch (error) {
		console.error('表单验证失败:', error);
	} finally {
		submitting.value = false;
	}
};

// 提交故障报告（模拟API调用）
const submitFaultReport = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			console.log('故障报告提交:', formData);
			resolve(true);
		}, 2000);
	});
};

// 跳转到历史记录
const goToHistory = () => {
	navigateTo('/mod/report/faultHistory');
};

// 页面初始化
onMounted(() => {
	// 如果有传入的机台号，自动填入
	if (pq.value.machineNumber) {
		formData.machineNumber = pq.value.machineNumber;
	}
});
</script>

<style lang="scss" scoped>
.fault-report-container {
	padding: 0;
	background: #f1f5fb;
}

.form-wrapper {
	background: #fff;
	margin: 0rpx 24rpx;
	border-radius: 20rpx;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;

	&.placeholder {
		color: #c0c4cc;
	}
}

.upload-container {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.image-item {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	border-radius: 12rpx;
	overflow: hidden;
}

.fault-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.delete-icon {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 32rpx;
	height: 32rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	border: 2rpx dashed #ddd;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fafafa;
}

.voice-section {
	margin: 48rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.voice-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.voice-btn {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #1c64fd 0%, #4b8bff 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(28, 100, 253, 0.3);
	transition: all 0.3s ease;

	&:active,
	&.recording {
		transform: scale(1.1);
		box-shadow: 0 12rpx 32rpx rgba(28, 100, 253, 0.5);
	}

	&.recording {
		background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
		animation: pulse 1s infinite;
	}
}

@keyframes pulse {
	0% {
		transform: scale(1.1);
	}
	50% {
		transform: scale(1.2);
	}
	100% {
		transform: scale(1.1);
	}
}

.voice-tip {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

.voice-duration {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 32rpx;
	background: #e6f7ff;
	border-radius: 32rpx;
	border: 2rpx solid #1890ff;
	margin-top: 16rpx;
}

.duration-text {
	font-size: 28rpx;
	color: #1890ff;
	font-weight: 500;
}

.submit-section {
	margin-top: 32rpx;
	padding: 32rpx;
}
:deep(.wd-navbar-custom) {
	background: linear-gradient(90deg, #2b6fea 0%, #459aff 100%);
	box-shadow: 0px 4px 4px 1px rgba(230, 230, 230, 0.4);
	.wd-navbar__title {
		font-size: 36rpx;
		font-weight: normal;
		line-height: 36rpx;
		letter-spacing: 0em;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.wd-navbar__arrow {
		color: #fff;
	}
	.wd-navbar__left {
		color: #fff;
	}
	.wd-navbar__right {
		font-size: 32rpx;
		font-weight: normal;
		line-height: 36rpx;
		letter-spacing: 0em;
		color: #ffffff;
	}
}
.wd-form-item {
	margin-top: 32rpx;
}
.wd-form-item__title {
	font-size: 28rpx;
	font-weight: normal;
	line-height: 40rpx;
	letter-spacing: 0px;
	color: #4e5969;
}
.required-star {
	color: #e11d48;
	margin-left: 4rpx;
}
.wd-form-item__content {
	margin-top: 16rpx;
	border-radius: 8rpx;
	padding: 0rpx 24rpx;
	gap: 0rpx 20rpx;
	background: #ffffff;
	box-sizing: border-box;
	border: 1rpx solid #e5e7eb;
}
</style>
