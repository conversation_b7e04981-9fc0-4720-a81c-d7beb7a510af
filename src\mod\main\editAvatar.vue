<template>
	<DIWAppPage>
		<DIWProtect>
			<view class="flex flex-col items-center mt-60px">
				<view class="img-frame" @click="chooseAvatar()">
					<wd-img v-if="avatarImageUrl" width="200px" height="200px" :src="avatarImageUrl" mode="aspectFill" />
					<wd-icon v-else name="fill-camera" custom-class="img-icon" />
				</view>

				<view class="mt-8"><text class="text-gray" @click="chooseAvatar()">点击上传头像</text></view>
			</view>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { sessionInfo, protect, protectOp, apiPut, reloadSessionInfo, mapFileIdToUrl, invokeTo, platformUploadFile } = useFramework();

const avatarImageUrl = computed(() => {
	let avatar = sessionInfo.value?.avatarUrl;
	if (typeof avatar === 'string') {
		return mapFileIdToUrl(avatar);
	}

	return '';
});

function chooseAvatar() {
	// #ifdef MP-WEIXIN || APP-PLUS
	uni.chooseMedia({
		count: 1,
		mediaType: ['image'],
		success(res) {
			cropImage(res.tempFiles[0].tempFilePath);
		},
	});
	// #endif
	// #ifdef H5
	uni.chooseFile({
		count: 1,
		type: 'image',
		success(res) {
			cropImage(res.tempFilePaths[0]);
		},
	});
	// #endif
}

function cropImage(url: string) {
	protect(async () => {
		const d = await invokeTo({ url: '/mod/main/cropAvatar', params: { url } });
		if (d.result && (d.result as any).tempFilePath) {
			protectOp(async () => {
				let d1;
				// #ifdef H5
				d1 = await platformUploadFile((d.result as any).tempFilePath as File, '10');
				// #endif
				// #ifdef MP-WEIXIN || APP-PLUS
				d1 = await platformUploadFile({ path: (d.result as any).tempFilePath }, '10');
				// #endif

				await apiPut({
					url: 'admin/user/updateCurrentUserInfo',
					data: { username: sessionInfo.value!.username, avatar: d1.fileId },
				});
				await reloadSessionInfo();
			});
		}
	});
}
</script>

<style scoped lang="scss">
.img-frame {
	width: 200px;
	height: 200px;
	position: relative;
	background-color: rgba(0, 0, 0, 0.04);
}

.img-icon {
	font-size: 60px;
	color: #fff;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}
</style>
