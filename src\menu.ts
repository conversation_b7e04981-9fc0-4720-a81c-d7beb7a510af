export function useAppMenuGroups() {
	const appMenuGroups: AppMenuGroup[] = [
		{
			title: '合同管理',
			icon: 'iconfont icon-tubiao-daishenhehetong-moren text-blue-500',
			items: [
				{
					title: '我的合同',
					icon: 'iconfont icon-kehudingdan text-blue-500',
					path: 'order/buyerOrderList',
					auth: 'order_order_sign',
				},
				{
					title: '销售合同',
					icon: 'iconfont icon-kehudingdan text-blue-500',
					path: 'order/sellerOrderList',
					auth: 'order_order_audit',
				},
				{
					title: '合同复核',
					icon: 'iconfont icon-daifuhe text-purple-500',
					path: 'order/orderReviewList',
					auth: 'order_order_review',
				},
				{
					title: '合同封存',
					icon: 'iconfont icon-zhongzhimima text-orange-800',
					path: 'order/orderSealList',
					auth: 'order_seal_unseal',
				},
			],
		},
		{
			title: '调拨管理',
			icon: 'iconfont icon-tiaobodan text-indigo-500',
			items: [
				{
					title: '调拨单',
					icon: 'iconfont icon-tiaobodan text-blue',
					path: 'allocation/allocation',
					auth: 'warehouseAllocation_view',
				},
				{
					title: '调拨单入库',
					icon: 'iconfont icon-tiaobochuku text-red-500',
					path: 'allocation/allocationAuditList',
					auth: 'warehouseAllocation_view2',
				},
			],
		},
		{
			title: '财务管理',
			icon: 'iconfont icon-yinhangkaihu text-yellow-500',
			items: [
				{
					title: '资金账户',
					icon: 'iconfont icon-kehushoukuan text-blue',
					path: 'pay/home',
					auth: 'pay_virtual_account_view',
				},
				{
					title: '发票申请',
					icon: 'iconfont icon-fapiao text-orange',
					path: 'invoice/invoiceApplyList',
					auth: ['order_deliverInvoiceApplication_view', 'order_deliverInvoiceApplication_edit'],
				},
				{
					title: '增票资质',
					icon: 'iconfont icon-fapiaokaiju text-green',
					path: 'invoice/invoiceInfo',
					auth: 'admin_invoice_view',
				},
			],
		},
		{
			title: '地址管理',
			icon: 'iconfont icon-dizhi text-blue-500',
			items: [
				{
					title: '收货地址',
					icon: 'iconfont icon-dizhi text-lime',
					path: 'buyer/addressList',
					auth: 'admin_address_info_view',
				},
				{
					title: '自提信息',
					icon: 'iconfont icon-ziti text-blue',
					path: 'buyer/pickupInfoList',
					auth: 'admin_pickup_info_view',
				},
			],
		},
	];

	return appMenuGroups;
}
