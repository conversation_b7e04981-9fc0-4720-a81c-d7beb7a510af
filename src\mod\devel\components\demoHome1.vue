<template>
	<DIWRoot>
		<swiper class="w-full h-200px bg-red" indicator-dots>
			<swiper-item v-for="color in demoColors" :key="color">
				<view :class="color" class="w-full h-full" />
			</swiper-item>
		</swiper>
		<wd-cell-group border>
			<DIWListView ref="listView" :meta="listViewMeta">
				<template #default="{ data, index }">
					<wd-cell vertical :title="data.id">
						<swiper v-if="index === 0" class="w-full h-100px bg-red" indicator-dots>
							<swiper-item v-for="color in demoColors" :key="color">
								<view :class="color" class="w-full h-full" />
							</swiper-item>
						</swiper>
						<!--
						<DIWFile v-if="data.brandImageFileIds" :model-value="data.brandImageFileIds" readonly />
						<DIWFile v-if="data.imageFileIds" :model-value="data.imageFileIds" readonly />
						-->
						<text class="text-xs whitespace-pre-wrap break-all">{{ JSON.stringify(data, null, 4) }}</text>
					</wd-cell>
				</template>
			</DIWListView>
		</wd-cell-group>
	</DIWRoot>
</template>

<script setup lang="ts">
const { apiGet, mapDict, sessionInfo } = useFramework();

const { ACC_REPR_GLOBAL_TYPE, sms_supplier, seal_status } = mapDict('ACC_REPR_GLOBAL_TYPE', 'sms_supplier', 'seal_status');

const listView = ref();

watchEffect(() => {
	console.log(ACC_REPR_GLOBAL_TYPE.value, sms_supplier.value, seal_status.value);
});

watch(
	[sessionInfo, listView],
	() => {
		if (listView.value) {
			nextTick(() => {
				listView.value.reload();
			});
		}
	},
	{ immediate: true }
);

// 用少量数据虚构出大量数据，用于演示分页

const { loadData: loadProducts } = useFakePageList({
	rowKey: 'id',
	total: 40,
	pageSize: 10,
	async loadData(pageIndex) {
		const size1 = 20;
		const size2 = 5;

		const isReload = pageIndex === undefined;

		let current = isReload ? 1 : pageIndex;
		let size = isReload ? size1 : size2;

		const d = await apiGet({
			url: 'market/home/<USER>/page',
			params: {
				current,
				size,
			},
		});

		if (isReload) {
			return { hasMore: d.total > size, items: d.records, trackInfo: 1 + size1 / size2 };
		}

		return { hasMore: d.total > current * size, items: [], trackInfo: current + 1 };
	},
});

let errorFlag = false;

const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(pageIndex) {
			if (errorFlag) {
				errorFlag = false;
				console.log('errorFlag', errorFlag);
				throw new Error('模拟网络错误');
			}

			// 下拉刷新的时候可以同时加载其他的数据

			const isReload = pageIndex === undefined;

			const da = await Promise.all([
				loadProducts(pageIndex),
				isReload
					? apiGet({
							url: 'market/product/brand/page',
							params: { current: 1, size: 100 },
						})
					: undefined,
			]);

			const d = da[0];
			return d;
		},
	});
});

const demoColors = ['bg-purple', 'bg-red', 'bg-lime', 'bg-orange'];
</script>
