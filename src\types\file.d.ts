type DIWFileType = 'image' | 'video' | 'file' | 'pdf' | 'word' | 'excel' | 'other';

interface DIWFileItem_New {
	type: 0;
	id: string;
	previewUrl: string;
	raw: any;
	fileType: DIWFileType;
	displayName: string;
	fileSize?: string | number;
}

interface DIWFileItem_Uploaded {
	type: 1;
	id: string;
	url: string;
	fileType: DIWFileType;
	displayName: string;
	fileSize?: string | number;
}

interface DIWFileItem_Resolving {
	type: 2;
	id: string;
	displayName?: string;
	fileType?: DIWFileType;
	url?: string;
	previewUrl?: string;
}

type DIWFileItem = DIWFileItem_New | DIWFileItem_Uploaded | DIWFileItem_Resolving;
