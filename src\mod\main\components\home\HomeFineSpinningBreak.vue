<template>
	<view class="fine-spinning-break-section">
		<view class="break-card">
			<!-- 头部标题区域 -->
			<view class="break-header">
				<view class="title-wrapper">
					<view class="title-wrapper-inner">
						<view class="title-indicator"></view>
						<text class="title">{{ t('fineSpinningBreakHead') }}</text>
					</view>
				</view>
				<wd-picker :columns="shiftScheduleColumns" v-model="shiftSchedule" @confirm="onChangeTime" />
			</view>

			<!-- 统计数据区域 -->
			<view class="stats-section">
				<!-- 当班断头总数 -->
				<view class="stat-item">
					<view class="stat-value-wrapper">
						<text class="stat-value stat-value-red">{{ breakData.currentShiftBreaks }}</text>
						<text class="stat-label">{{ t('currentShiftBreakTotal') }}</text>
					</view>
					<view class="rank-badge rank-badge-orange">{{ t('ranking') }}:{{ breakData.currentShiftRank }}</view>
				</view>

				<!-- 当班接头数 -->
				<view class="stat-item">
					<view class="stat-value-wrapper">
						<text class="stat-value stat-value-green">{{ breakData.currentShiftConnections }}</text>
						<text class="stat-label">{{ t('currentShiftConnections') }}</text>
					</view>
					<view class="rank-badge rank-badge-blue">{{ t('ranking') }}:{{ breakData.connectionRank }}</view>
				</view>

				<!-- 平均接头时间 -->
				<view class="stat-item">
					<view class="stat-value-wrapper">
						<view class="stat-value stat-value-blue">
							<text class="stat-value-text">{{ breakData.averageConnectionTime }}</text>
							<text class="unit">s</text>
						</view>
						<text class="stat-label">{{ t('averageConnectionTime') }}</text>
					</view>
					<view class="rank-badge rank-badge-green">{{ t('ranking') }}:{{ breakData.timeRank }}</view>
				</view>
			</view>

			<!-- 机台详情区域 -->
			<view class="machines-detail-section">
				<view v-for="machine in breakData.machines" :key="machine.id" class="machine-detail-card">
					<view class="machine-number">{{ machine.number }}</view>
					<view class="machine-divider"></view>
					<view class="machine-info">
						<view class="machine-status">
							<text class="status-text">{{ machine.statusText }}</text>
							<wd-icon name="arrow-right" size="24rpx" color="#86909C" />
						</view>
						<text class="machine-details">{{ machine.details }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
const props = defineProps<{
	shiftScheduleList: {
		planStartTime: string;
		shiftCodeName: string;
	}[];
	needRefresh: boolean;
}>();
const { protect } = useFramework();

const shiftSchedule = ref('');

watch(
	() => props.needRefresh,
	async (newVal) => {
		if (newVal) {
			await loadData();
		}
	}
);

async function loadData() {
	await protect(async () => {
		// const d = await apiGet('mesmultidata/DiwFineSpinningBreak/list');
		// breakData.value = d || [];
	});
}

onMounted(async () => {
	await loadData();
});

const shiftScheduleColumns = computed(() => {
	if (!props.shiftScheduleList || props.shiftScheduleList.length === 0) {
		return [];
	}
	return props.shiftScheduleList.map((item) => ({
		label: item.planStartTime + ' ' + item.shiftCodeName,
	}));
});

// 监听 shiftScheduleList 的变化，设置默认值
watch(
	() => props.shiftScheduleList,
	(newList) => {
		if (newList && newList.length > 0 && !shiftSchedule.value) {
			shiftSchedule.value = newList[0].planStartTime;
		}
	},
	{ immediate: true }
);

// 使用国际化
const { t } = useI18n();

// 细纱断头数据接口
interface FineSpinningBreakData {
	date: string;
	shift: string;
	currentShiftBreaks: number;
	currentShiftRank: number;
	currentShiftConnections: number;
	connectionRank: number;
	averageConnectionTime: string;
	timeRank: number;
	machines: MachineBreakDetail[];
}

// 机台断头详情接口
interface MachineBreakDetail {
	id: string;
	number: string;
	statusText: string;
	details: string;
}

// 模拟细纱断头数据
const breakData = ref<FineSpinningBreakData>({
	date: '7月09日',
	shift: '晚',
	currentShiftBreaks: 528,
	currentShiftRank: 79,
	currentShiftConnections: 512,
	connectionRank: 19,
	averageConnectionTime: '35',
	timeRank: 5,
	machines: [
		{
			id: '001',
			number: '001',
			statusText: t('backwardSpindle'),
			details: 'L101、L102、R112',
		},
		{
			id: '002',
			number: '002',
			statusText: t('backwardSpindle'),
			details: 'L101、L105、R052',
		},
		{
			id: '003',
			number: '003',
			statusText: t('backwardSpindle'),
			details: 'L021、L034、R066',
		},
		{
			id: '004',
			number: '004',
			statusText: t('backwardSpindle'),
			details: 'L125、L057、R026',
		},
		{
			id: '005',
			number: '005',
			statusText: t('backwardSpindle'),
			details: 'L089、L106、R115',
		},
		{
			id: '006',
			number: '006',
			statusText: t('backwardSpindle'),
			details: 'L124、L024、R079',
		},
	],
});

const onChangeTime = (value: any) => {
	console.log(`Selected item: ${value}`);
};
</script>

<style lang="scss" scoped>
.fine-spinning-break-section {
	margin-top: 26rpx;
	width: 702rpx;
	min-height: 474rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 702rpx 100%;
	background-repeat: no-repeat;
	background-position: top center;
	border-radius: 24rpx;
	background-color: transparent;
}

.break-card {
	width: calc(100% - 48rpx);
	display: flex;
	flex-direction: column;
	margin: 34rpx 36rpx 32rpx 36rpx;
}

/* 头部样式 */
.break-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	.title-wrapper {
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}

	.title-wrapper-inner {
		display: flex;
		align-items: center;

		.title-indicator {
			width: 8rpx;
			height: 32rpx;
			background: #0082f0;
			border-radius: 4rpx;
			margin-right: 16rpx;
		}

		.title {
			font-size: 30rpx;
			font-weight: normal;
			letter-spacing: 0rpx;
			color: #1d2129;
		}
	}

	.date-selector {
		display: flex;
		gap: 24rpx;
		align-items: center;

		.date-selector-inner,
		.evening-selector-inner {
			display: flex;
			align-items: center;
			gap: 8rpx;
			.date-text {
				font-size: 24rpx;
				font-weight: normal;
				text-align: center;
				letter-spacing: 0rpx;
				color: #4b5563;
			}
			.evening-text {
				font-size: 24rpx;
				font-weight: normal;
				text-align: center;
				letter-spacing: 0rpx;
				color: #4b5563;
			}
		}
	}
}

/* 统计数据区域样式 */
.stats-section {
	margin-top: 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	// gap: 20rpx;

	.stat-item {
		width: 200rpx;
		height: 92rpx;
		background: #f6f7ff;
		border-radius: 8rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;

		.stat-value-wrapper {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 4rpx;

			.stat-value {
				margin-top: 6rpx;
				font-size: 30rpx;
				font-weight: normal;
				letter-spacing: 0rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&.stat-value-red {
					color: #ef4444;
				}

				&.stat-value-green {
					color: #0ecb12;
				}

				&.stat-value-yellow {
					color: #f59e0b;
				}
				&.stat-value-blue {
					color: #4086ff;
				}
				.unit {
					font-size: 24rpx;
					font-weight: normal;
					letter-spacing: 0rpx;
				}
			}
		}

		.rank-badge {
			position: absolute;
			right: 0;
			top: 0;
			font-size: 16rpx;
			font-weight: normal;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0rpx;
			padding: 4rpx 8rpx;
			border-radius: 0rpx 8rpx 0rpx 8rpx;

			&.rank-badge-orange {
				background: rgba(255, 154, 26, 0.102);
				color: #ff9a1a;
			}

			&.rank-badge-blue {
				background: rgba(0, 130, 240, 0.102);
				color: #0082f0;
			}

			&.rank-badge-green {
				background: rgba(14, 203, 18, 0.102);
				color: #0ecb12;
			}
		}
		.stat-label {
			font-size: 24rpx;
			font-weight: normal;
			letter-spacing: 0rpx;
			color: #4b5563;
		}
	}

	.stats-divider {
		width: 2rpx;
		height: 68rpx;
		background: #e5e7eb;
		margin: 0 16rpx;
	}
}

/* 机台详情区域样式 */
.machines-detail-section {
	margin-top: 20rpx;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;

	.machine-detail-card {
		padding: 4rpx 11rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 24rpx;
		background: #ffffff;
		box-shadow: 0px 4px 10px 0px rgba(34, 115, 184, 0.1);
		gap: 20rpx;

		.machine-number {
			font-size: 24rpx;
			font-weight: normal;
			line-height: 28rpx;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0rpx;
			color: #091f44;
		}

		.machine-divider {
			display: flex;
			width: 100%;
			border: 1rpx solid #cfd3d7;
		}

		.machine-info {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;

			.machine-status {
				display: flex;
				align-items: center;
				justify-content: center;

				.status-text {
					font-size: 20rpx;
					font-weight: normal;
					line-height: 28rpx;
					text-align: center;
					display: flex;
					align-items: center;
					letter-spacing: 0rpx;
					color: #1d2129;
				}
			}

			.machine-details {
				font-size: 20rpx;
				font-weight: normal;
				line-height: 28rpx;
				text-align: center;
				display: flex;
				align-items: center;
				letter-spacing: 0rpx;
				color: #86909c;
			}
		}
	}
}
:deep(.wd-picker) {
	.wd-cell {
		padding: 0;
		background-color: transparent;
		.wd-cell__wrapper {
			padding: 0;
		}
	}
}
</style>
