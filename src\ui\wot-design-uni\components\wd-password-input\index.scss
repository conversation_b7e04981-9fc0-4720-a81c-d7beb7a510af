@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(password-input) {
		@include e(item) {
			color: $wot-dark-color;
			background: $wot-dark-background2;

			@include when(border) {
				border-color: $wot-dark-border-color;
			}
		}

		@include e(mask) {
			background-color: $wot-dark-color;
		}

		@include e(cursor) {
			background-color: $wot-dark-color;
		}

		@include e(info) {
			color: $wot-dark-color;

			@include when(border) {
				color: $wot-dark-color2;
			}
		}
	}
}

@include b(password-input) {
	position: relative;
	margin: 0 $wot-password-input-margin;
	user-select: none;

	@include e(security) {
		display: flex;
		width: 100%;
		height: $wot-password-input-height;
		cursor: pointer;
	}

	@include e(item) {
		position: relative;
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: center;
		height: 100%;
		color: $wot-password-input-text-color;
		font-size: $wot-password-input-font-size;
		line-height: 1.2;
		background: $wot-password-input-background;

		@include when(border) {
			border: 1px solid $wot-password-input-border-color;

			&:not(:last-child) {
				border-right: none;
			}

			&:first-child {
				border-top-left-radius: $wot-password-input-radius;
				border-bottom-left-radius: $wot-password-input-radius;
			}

			&:last-child {
				border-top-right-radius: $wot-password-input-radius;
				border-bottom-right-radius: $wot-password-input-radius;
			}
		}
	}

	@include e(mask) {
		position: absolute;
		top: 50%;
		left: 50%;
		width: $wot-password-input-dot-size;
		height: $wot-password-input-dot-size;
		background: $wot-password-input-dot-color;
		border-radius: 100%;
		transform: translate(-50%, -50%);
		visibility: hidden;
	}

	@include e(cursor) {
		position: absolute;
		top: 50%;
		left: 50%;
		width: $wot-password-input-cursor-width;
		height: $wot-password-input-cursor-height;
		background: $wot-password-input-cursor-color;
		transform: translate(-50%, -50%);
		animation: $wot-password-input-cursor-duration cursor-flicker infinite;
	}

	@include e(info) {
		margin-top: $wot-password-input-margin;
		font-size: $wot-password-input-info-font-size;
		text-align: center;
		color: $wot-password-input-info-color;

		@include when(error) {
			color: $wot-password-input-error-info-color;
		}
	}
}

@keyframes cursor-flicker {
	from {
		opacity: 0;
	}

	50% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}
