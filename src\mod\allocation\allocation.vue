<template>
	<DIWAppPage mode="1">
		<view class="diw-theme absolute inset-0 flex flex-col">
			<view class="relative flex-1">
				<DIWScrollView>
					<DIWListView ref="listView" :meta="listViewMeta">
						<template #default="{ data }">
							<view class="allocation-card" @click="handleView(data)">
								<view class="allocation-card__header">
									<view class="allocation-card__header-left">
										<text class="allocation-card__code">{{ data.allocationNo }}</text>
										<view class="allocation-card__type-tag">
											{{ data.allocationTypeStr }}
										</view>
									</view>
									<view class="allocation-card__status" :class="getStatusClass(data.status)">
										{{ data.statusStr }}
									</view>
								</view>

								<view class="allocation-card__body">
									<view class="allocation-card__info-section">
										<view class="allocation-card__info-item">
											<text class="allocation-card__info-label">调拨名称</text>
											<text class="allocation-card__info-value">{{ data.allocationName }}</text>
										</view>
										<view class="allocation-card__info-item">
											<text class="allocation-card__info-label">商家名称</text>
											<text class="allocation-card__info-value">{{ data.sellerName }}</text>
										</view>
										<view class="allocation-card__info-item">
											<text class="allocation-card__info-label">商品编码</text>
											<text class="allocation-card__info-value">{{ data.productNameArray }}</text>
										</view>
									</view>

									<view class="allocation-card__warehouse-section">
										<view class="allocation-card__warehouse-item">
											<view class="allocation-card__warehouse-header">
												<text class="allocation-card__warehouse-icon">📤</text>
												<text class="allocation-card__warehouse-label">出库仓库</text>
											</view>
											<text class="allocation-card__warehouse-name">{{ data.outWarehouseName }}</text>
										</view>

										<view class="allocation-card__divider">
											<view class="allocation-card__arrow-icon">
												<text class="allocation-card__arrow">→</text>
											</view>
										</view>

										<view class="allocation-card__warehouse-item">
											<view class="allocation-card__warehouse-header">
												<text class="allocation-card__warehouse-icon">📥</text>
												<text class="allocation-card__warehouse-label">入库仓库</text>
											</view>
											<text class="allocation-card__warehouse-name">{{ data.inWarehouseName }}</text>
										</view>
									</view>

									<view class="allocation-card__quantity-section">
										<text class="allocation-card__quantity-label">移库数量</text>
										<text class="allocation-card__quantity-value">{{ data.quantity }} 吨</text>
									</view>
								</view>

								<view class="allocation-card__footer">
									<view class="allocation-card__action-btn" @click.stop="handleView(data)">
										<text class="allocation-card__action-icon i-ep-view"></text>
										<text class="allocation-card__action-text">查看详情</text>
									</view>
									<view class="allocation-card__action-btn" @click.stop="handleLogistics(data)">
										<text class="allocation-card__action-icon i-ep-van"></text>
										<text class="allocation-card__action-text">维护物流</text>
									</view>
								</view>
							</view>
						</template>
					</DIWListView>
				</DIWScrollView>
			</view>
			<!-- <DIWBottomBar>
				<wd-button type="primary" class="allocation-bottom-btn" @click="handleAddTransfer">
					<text class="i-ep-plus mr-1"></text>添加调拨单
				</wd-button>
				<wd-button type="success" class="allocation-bottom-btn" @click="handleAddWarehouse">
					<text class="i-ep-plus mr-1"></text>添加移库单
				</wd-button>
			</DIWBottomBar> -->
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';

const { apiGet, navigateTo, protect } = useFramework();

const listView = ref();
const searchParams = reactive({
	code: '',
	createBy: '',
});

// 列表视图元数据 - 使用computed属性
const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(pageIndex) {
			const size1 = 20;
			const size2 = 5;

			const isReload = pageIndex === undefined;

			let current = isReload ? 1 : pageIndex;
			let size = isReload ? size1 : size2;
			const params = {
				current: pageIndex,
				size,
				ascs: '',
				descs: 'create_time',
				code: searchParams.code,
				createBy: searchParams.createBy,
			};

			const d = await apiGet({ url: 'stock/warehouseAllocation/page', params });
			if (isReload) {
				return {
					hasMore: d && d.total ? d.total > size : false,
					items: d && d.records ? d.records : [],
					trackInfo: 1 + size1 / size2,
				};
			}

			return {
				hasMore: d && d.total ? d.total > current * size : false,
				items: d && d.records ? d.records : [],
				trackInfo: current + 1,
			};
		},
	});
});

// 根据状态获取样式类
function getStatusClass(status: number) {
	switch (status) {
		case 1:
			return 'bg-blue-50 text-blue-500';
		case 2:
			return 'bg-orange-50 text-orange-500';
		case 3:
			return 'bg-yellow-50 text-yellow-500';
		case 4:
			return 'bg-green-50 text-green-500';
		default:
			return 'bg-gray-50 text-gray-500';
	}
}

// 查看详情
function handleView(item: Record<string, any>) {
	protect(async () => {
		await navigateTo({
			url: `/mod/allocation/allocationDetail?id=${item.id}`,
		});
	});
}

// 维护物流
function handleLogistics(item: Record<string, any>) {
	protect(async () => {
		await navigateTo({
			url: `/mod/allocation/logisticsInfo?id=${item.id}`,
		});
	});
}

// 添加调拨单
function handleAddTransfer() {
	protect(async () => {
		await navigateTo({
			url: '/mod/allocation/addAllocation?type=1',
		});
	});
}

// 添加移库单
function handleAddWarehouse() {
	protect(async () => {
		await navigateTo({
			url: '/mod/allocation/addWarehouse?type=2',
		});
	});
}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f7fa;
}

.allocation-card {
	margin: 24rpx 32rpx;
	border-radius: 24rpx;
	background-color: #ffffff;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	position: relative;

	&__header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 28rpx 32rpx;
		border-bottom: 2rpx solid #e5e6eb;
	}

	&__header-left {
		display: flex;
		align-items: center;
	}

	&__code {
		font-size: 34rpx;
		font-weight: 600;
		color: #1d2129;
		margin-right: 16rpx;
	}

	&__type-tag {
		font-size: 24rpx;
		color: #2b5aed;
		background-color: #eef2ff;
		padding: 4rpx 16rpx;
		border-radius: 16rpx;
	}

	&__status {
		font-size: 24rpx;
		font-weight: 500;
		padding: 8rpx 24rpx;
		border-radius: 32rpx;
	}

	&__body {
		padding: 28rpx 32rpx;
	}

	&__info-section {
		margin-bottom: 28rpx;
		padding-bottom: 24rpx;
		border-bottom: 2rpx solid #e5e6eb;
	}

	&__info-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 16rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	&__info-label {
		font-size: 28rpx;
		color: #86909c;
	}

	&__info-value {
		font-size: 28rpx;
		color: #1d2129;
		font-weight: 500;
		max-width: 60%;
		text-align: right;
		word-break: break-all;
	}

	&__warehouse-section {
		display: flex;
		align-items: center;
		margin-bottom: 28rpx;
		padding-bottom: 24rpx;
		border-bottom: 2rpx solid #e5e6eb;
	}

	&__warehouse-item {
		flex: 1;
	}

	&__warehouse-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}

	&__warehouse-icon {
		font-size: 32rpx;
		margin-right: 8rpx;
	}

	&__warehouse-label {
		font-size: 28rpx;
		color: #86909c;
	}

	&__warehouse-name {
		font-size: 28rpx;
		color: #1d2129;
		font-weight: 500;
	}

	&__divider {
		width: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	&__arrow-icon {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		background-color: #eef2ff;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	&__arrow {
		color: #2b5aed;
		font-size: 24rpx;
		font-weight: bold;
	}

	&__quantity-section {
		display: flex;
		justify-content: space-between;
	}

	&__quantity-label {
		font-size: 28rpx;
		color: #86909c;
	}

	&__quantity-value {
		font-size: 32rpx;
		color: #1d2129;
		font-weight: 600;
	}

	&__footer {
		display: flex;
		justify-content: flex-end;
		padding: 20rpx 32rpx;
		border-top: 2rpx solid #e5e6eb;
		background-color: #fafbfc;
	}

	&__action-btn {
		display: flex;
		align-items: center;
		margin-left: 32rpx;
		color: #2b5aed;
	}

	&__action-icon {
		font-size: 28rpx;
		margin-right: 8rpx;
	}

	&__action-text {
		font-size: 26rpx;
		font-weight: 500;
	}
}

/* 状态样式 */
.bg-blue-50.text-blue-500 {
	background-color: #eef2ff;
	color: #2b5aed;
}

.bg-orange-50.text-orange-500 {
	background-color: #fff8e6;
	color: #ff7d00;
}

.bg-yellow-50.text-yellow-500 {
	background-color: #fffbe6;
	color: #faad14;
}

.bg-green-50.text-green-500 {
	background-color: #e8ffea;
	color: #00b42a;
}

.bg-gray-50.text-gray-500 {
	background-color: #f2f3f5;
	color: #86909c;
}

/* 底部按钮栏 */

.allocation-bottom-btn {
	flex: 1;
	margin: 0 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
