@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

@mixin tag-type-style($normalColor, $normalBg) {
	background: $normalBg;

	@include when(plain) {
		background: transparent;
		color: $normalColor;
		border: 1px solid $normalColor;
		padding: 0 4px;
	}
	@include when(round) {
		line-height: 1.2;
		font-size: $wot-tag-fs;
		padding: 4px 11px;
		background: transparent;
		color: if($normalColor != $wot-tag-info-color, $normalColor, $wot-tag-round-color);
		border: 1px solid if($normalColor != $wot-tag-info-color, $normalColor, $wot-tag-round-border-color);
		border-radius: $wot-tag-round-radius;
	}
	@include when(mark) {
		padding: 1px 6px;
		border-radius: $wot-tag-mark-radius;

		@include when(plain) {
			padding: 0 6px;
		}
	}
	@include when(active) {
		color: $wot-tag-primary-color;
		border-color: $wot-tag-primary-color;
	}
}
@include b(tag) {
	font-size: $wot-tag-small-fs;
	display: inline-block;
	color: $wot-tag-color;
	padding: 0 3px;
	border-radius: 2px;
	transition: opacity 0.3s;
	vertical-align: middle;
	line-height: initial;

	@include when(default) {
		@include tag-type-style($wot-tag-info-color, $wot-tag-info-bg);
	}
	@include when(primary) {
		@include tag-type-style($wot-tag-primary-color, $wot-tag-primary-bg);
	}
	@include when(danger) {
		@include tag-type-style($wot-tag-danger-color, $wot-tag-danger-bg);
	}
	@include when(warning) {
		@include tag-type-style($wot-tag-warning-color, $wot-tag-warning-bg);
	}
	@include when(success) {
		@include tag-type-style($wot-tag-success-color, $wot-tag-success-bg);
	}
	@include when(icon) {
		font-size: $wot-tag-fs;
		line-height: 1.2;
		padding: 2px 5px;
	}
	@include when(dynamic) {
		box-sizing: border-box;
		width: 88px;
		transition: 0.3s;

		&:active {
			color: $wot-tag-primary-color;
			border-color: $wot-tag-primary-color;
		}
	}
	@include when(dynamic-input) {
		border-color: $wot-tag-primary-color;
	}
	@include edeep(icon) {
		display: inline-block;
		margin-right: 4px;
		font-size: $wot-tag-fs;
		line-height: 1.2;
		vertical-align: baseline;
	}
	@include e(text) {
		display: inline-block;
		vertical-align: text-top;
	}
	@include e(add-text) {
		width: 60px;
		height: 14px;
		min-height: 14px;
		display: inline-block;
		font-size: $wot-tag-fs;
		vertical-align: middle;
		padding: 0;
	}
	@include e(close) {
		display: inline-block;
		margin-left: 24px;
		margin-right: -4px;
		font-size: $wot-tag-close-size;
		height: 14px;
		line-height: 1.1;
		vertical-align: text-bottom;
		color: $wot-tag-close-color;

		&:active {
			color: $wot-tag-close-active-color;
		}
	}
	@include edeep(add) {
		vertical-align: bottom;
	}
}
