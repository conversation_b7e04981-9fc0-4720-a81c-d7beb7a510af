@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(sidebar-item) {
		background: $wot-dark-background2;
		color: $wot-dark-color;

		&:active {
			background-color: $wot-dark-background4;
		}

		@include m(active) {
			background: $wot-dark-background;
			color: $wot-sidebar-active-color;
		}

		@include m(disabled) {
			color: $wot-dark-color-gray;

			&:active {
				background-color: $wot-dark-background2;
			}
		}
	}
}

@include b(sidebar-item) {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	padding: 32rpx;
	font-size: $wot-sidebar-font-size;
	color: $wot-sidebar-color;
	background: $wot-sidebar-bg;
	min-height: $wot-sidebar-item-height;
	box-sizing: border-box;
	white-space: wrap;
	line-height: $wot-sidebar-item-line-height;

	&:active {
		background-color: $wot-sidebar-hover-bg;
	}

	@include m(active) {
		font-weight: 600;
		background: $wot-sidebar-active-bg;
		color: $wot-sidebar-active-color;

		&::before {
			position: absolute;
			top: 50%;
			left: 0;
			width: $wot-sidebar-active-border-width;
			height: $wot-sidebar-active-border-height;
			background: $wot-sidebar-active-color;
			transform: translateY(-50%);
			content: '';
			border-radius: $wot-sidebar-active-border-width;
		}

		&:active {
			background-color: transparent;
		}
	}

	@include m(prefix) {
		border-bottom-right-radius: $wot-sidebar-border-radius;
	}

	@include m(suffix) {
		border-top-right-radius: $wot-sidebar-border-radius;
	}

	@include m(disabled) {
		color: $wot-sidebar-disabled-color;
		cursor: not-allowed;

		&:active {
			background-color: $wot-sidebar-bg;
		}
	}

	@include edeep(badge) {
		z-index: 2;
		word-break: break-all;
	}

	@include edeep(icon) {
		font-size: $wot-sidebar-icon-size;
		margin-right: 2px;
	}
}
