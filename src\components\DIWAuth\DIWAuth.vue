<template>
	<template v-if="authenticated">
		<slot />
	</template>
	<template v-else>
		<slot name="else" />
	</template>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{ auth?: string | string[]; all?: boolean }>(), { all: false });

// #ifdef H5 || APP-PLUS
defineOptions({ inheritAttrs: false });
// #endif

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		virtualHost: true,
		styleIsolation: 'shared',
	},
});

// #endif

const { checkAuth } = useFramework();

const authenticated = computed(() => {
	return checkAuth(props.auth, props.all);
});
</script>
