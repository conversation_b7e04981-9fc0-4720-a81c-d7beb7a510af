<template>
	<view class="flex flex-col items-center">
		<view class="p-4">
			<text>请在电脑浏览器端依次完成企业认证、契约锁认证、银行认证后再使用手机版。</text>
		</view>
		<view class="mt-8">
			<wd-button type="info" @click="doLogout">退出登录</wd-button>
		</view>
	</view>
</template>

<script setup lang="ts">
const { protect, logout } = useFramework();

function doLogout() {
	protect(async () => {
		await logout();
		await uni.reLaunch({ url: '/mod/mall/home' });
	});
}
</script>
