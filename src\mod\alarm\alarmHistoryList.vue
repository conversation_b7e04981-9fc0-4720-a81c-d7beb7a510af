<template>
	<view class="alarm-history-list">
		<view class="alarm-history-item" v-for="(alarm, index) in alarmList" :key="index">
			<text class="alarm-history-item-time">{{ alarm.startTime.split(' ')[1] }}</text>
			<text class="alarm-history-item-machine">{{ alarm.machineName }}</text>
			<text class="alarm-history-item-alarm">{{ alarm.alarmName }}</text>
			<text class="alarm-history-item-duration">{{ alarm.durationTime }}</text>
			<text class="alarm-history-item-status">{{ alarm.isRestore === 1 ? t('yes') : t('no') }}</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const { apiGet } = useFramework();

const alarmList = ref<any[]>([]);

onMounted(async () => {
	const d = await apiGet('mesmultidata/diwalarmdata/Current/page');
	alarmList.value = d || [];
});
</script>

<style lang="scss" scoped></style>
