<template>
	<DIWAppPage mode="1" title="维护物流信息">
		<DIWScrollView>
			<view v-if="loading" class="loading-container">
				<wd-loading color="#4169e1" size="36px" />
				<text class="loading-text">加载中...</text>
			</view>

			<template v-else>
				<!-- 基本信息卡片 -->
				<view class="info-card">
					<view class="info-card__header">
						<text class="info-card__title">调拨单信息</text>
					</view>
					<view class="info-card__body">
						<view class="info-item">
							<text class="info-item__label">调拨单号</text>
							<text class="info-item__value">{{ info.allocationNo }}</text>
						</view>
						<view class="info-item">
							<text class="info-item__label">调拨类型</text>
							<text class="info-item__value">{{ info.allocationTypeStr }}</text>
						</view>
						<view class="info-item">
							<text class="info-item__label">状态</text>
							<text class="info-item__value" :class="getStatusClass(info.status)">{{ info.statusStr }}</text>
						</view>
					</view>
				</view>

				<!-- 物流信息表单 -->
				<view class="form-card">
					<view class="form-card__header">
						<text class="form-card__title">物流信息</text>
					</view>
					<view class="form-card__body">
						<view class="form-item">
							<!-- <text class="form-item__label">司机</text> -->
							<wd-input label="司机" v-model="formData.driverName" placeholder="请输入司机姓名" clearable />
						</view>
						<view class="form-item">
							<!-- <text class="form-item__label">电话</text> -->
							<wd-input label="电话" v-model="formData.phone" placeholder="请输入司机电话" type="number" clearable />
						</view>
						<view class="form-item">
							<!-- <text class="form-item__label">司机身份证</text> -->
							<wd-input
								label="司机身份证"
								prop="driverIdCard"
								required
								clearable
								v-model="formData.driverIdCard"
								@focus="handleInputFocus('certId')"
								@keyboardheightchange="handleKeyboardHeightChange"
							/>
							<!-- <wd-input v-model="formData.driverIdCard" placeholder="请输入司机身份证号" clearable /> -->
						</view>
						<view class="form-item">
							<!-- <text class="form-item__label">物流单号</text> -->
							<wd-input label="物流单号" v-model="formData.logisticsNo" placeholder="请输入物流单号" clearable />
						</view>
						<view class="form-item">
							<!-- <text class="form-item__label">车牌号</text> -->
							<wd-input
								label="车牌号"
								prop="plateNumber"
								required
								clearable
								v-model="formData.licenseNumber"
								@focus="handleInputFocus('plateNumber')"
								@keyboardheightchange="handleKeyboardHeightChange"
							/>
							<!-- <wd-input v-model="formData.licenseNumber" placeholder="请输入车牌号" clearable /> -->
						</view>
					</view>
				</view>

				<!-- 提交按钮 -->
				<view class="action-buttons">
					<wd-button type="primary" size="large" @click="handleSubmit">保存</wd-button>
				</view>
			</template>
		</DIWScrollView>
		<template #keyboard>
			<wd-keyboard v-model:visible="certIdFocused" extra-key="X" v-model="formData.driverIdCard" />
			<wd-keyboard v-model:visible="plateNumberFocused" mode="car" v-model="formData.licenseNumber" />
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';

const { apiGet, apiPost, navigateTo, protect, usePageQuery, showMessage } = useFramework();

const pq = usePageQuery();
const loading = ref(true);
const info = ref<Record<string, any>>({});
const certIdFocused = ref(false);
const plateNumberFocused = ref(false);

// 表单数据
const formData = reactive({
	driverName: '',
	phone: '',
	driverIdCard: '',
	logisticsNo: '',
	licenseNumber: '',
});

onMounted(() => {
	loadData();
});

// 加载数据
async function loadData() {
	if (!pq.value || !pq.value.id) {
		uni.showToast({ title: '参数错误', icon: 'none' });
		return;
	}

	try {
		loading.value = true;
		const res = await apiGet({ url: `stock/warehouseAllocation/${pq.value.id}` });
		console.log('获取详情数据:', res);
		if (res) {
			info.value = res;

			// 如果已有物流信息，填充表单
			if (res.details && res.details.length > 0) {
				const detail = res.details[0];
				formData.driverName = detail.driverName || '';
				formData.phone = detail.phone || '';
				formData.driverIdCard = detail.driverIdCard || '';
				formData.logisticsNo = detail.logisticsNo || '';
				formData.licenseNumber = detail.licenseNumber || '';
			}
		} else {
			uni.showToast({ title: '数据格式错误', icon: 'none' });
		}
	} catch (error) {
		console.error('加载调拨单详情失败', error);
		uni.showToast({ title: '加载失败', icon: 'none' });
	} finally {
		loading.value = false;
	}
}

// 获取状态样式类
function getStatusClass(status: number) {
	const statusMap: Record<number, string> = {
		1: 'status-blue',
		2: 'status-orange',
		3: 'status-yellow',
		4: 'status-green',
		5: 'status-gray',
	};

	return statusMap[status] || 'status-gray';
}

// 提交表单
function handleSubmit() {
	protect(async () => {
		// 表单验证
		if (!formData.driverName) {
			showMessage('请输入司机姓名');
			return;
		}
		if (!formData.phone) {
			showMessage('请输入司机电话');
			return;
		}
		if (!formData.driverIdCard) {
			showMessage('请输入司机身份证号');
			return;
		}
		if (!formData.logisticsNo) {
			showMessage('请输入物流单号');
			return;
		}
		if (!formData.licenseNumber) {
			showMessage('请输入车牌号');
			return;
		}

		try {
			const params = {
				id: pq.value.id,
				detailExpressList: {
					id: pq.value.id,
					driverName: formData.driverName,
					phone: formData.phone,
					driverIdCard: formData.driverIdCard,
					logisticsNo: formData.logisticsNo,
					licenseNumber: formData.licenseNumber,
				},
			};

			const res = await apiPost({
				url: 'stock/warehouseAllocation/updateExpress',
				data: params,
			});

			if (res) {
				uni.showToast({ title: '保存成功', icon: 'success' });
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} else {
				uni.showToast({ title: '保存失败', icon: 'none' });
			}
		} catch (error) {
			console.error('保存物流信息失败', error);
			uni.showToast({ title: '保存失败', icon: 'none' });
		}
	});
}
function handleInputFocus(field: string) {
	plateNumberFocused.value = field === 'plateNumber';

	// #ifdef APP-PLUS
	certIdFocused.value = field === 'certId';
	// #endif
}

function handleKeyboardHeightChange(ev: { height: number }) {
	if (ev.height > 0) {
		if (plateNumberFocused.value || certIdFocused.value) {
			uni.hideKeyboard();
		}
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #f5f7fa;
}

.loading-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #86909c;
}

/* 信息卡片样式 */
.info-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;

	&__header {
		padding: 24rpx 32rpx;
		border-bottom: 2rpx solid #e5e6eb;
	}

	&__title {
		font-size: 32rpx;
		font-weight: 600;
		color: #1d2129;
	}

	&__body {
		padding: 24rpx 32rpx;
	}
}

/* 信息项样式 */
.info-item {
	display: flex;
	justify-content: space-between;
	padding: 16rpx 0;
	border-bottom: 2rpx solid #f2f3f5;

	&:last-child {
		border-bottom: none;
	}

	&__label {
		font-size: 28rpx;
		color: #86909c;
		width: 180rpx;
	}

	&__value {
		font-size: 28rpx;
		color: #1d2129;
		flex: 1;
		text-align: right;
		word-break: break-all;
	}
}

/* 表单卡片样式 */
.form-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;

	&__header {
		padding: 24rpx 32rpx;
		border-bottom: 2rpx solid #e5e6eb;
	}

	&__title {
		font-size: 32rpx;
		font-weight: 600;
		color: #1d2129;
	}

	&__body {
		padding: 24rpx 32rpx;
	}
}

/* 表单项样式 */
.form-item {
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}

	&__label {
		font-size: 28rpx;
		color: #1d2129;
		margin-bottom: 12rpx;
		display: block;
	}
}

/* 操作按钮样式 */
.action-buttons {
	padding: 32rpx;
	margin-bottom: 32rpx;
}

/* 状态颜色 */
.status-blue {
	color: #2b5aed;
}

.status-orange {
	color: #ff7d00;
}

.status-yellow {
	color: #faad14;
}

.status-green {
	color: #00b42a;
}

.status-gray {
	color: #86909c;
}
</style>
