<template>
	<DIWAppPage>
		<DIWProtect>
			<wd-form ref="form" :model="model">
				<wd-gap />

				<wd-cell-group border>
					<wd-input label="名字" prop="name" v-model="model.name" required :maxlength="20" :rules="[{ required: true, message: '请输入名字' }]" />
				</wd-cell-group>

				<wd-gap />

				<view class="px-4 text-right">
					<wd-button :loading="saving" @click="save">保存</wd-button>
				</view>
			</wd-form>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { sessionInfo, protect, protectOp, validateForm, navigateBack, apiPut, reloadSessionInfo } = useFramework();

const form = ref();

const model = ref({ name: sessionInfo.value ? sessionInfo.value.name : '' });

const saving = ref(false);

function save() {
	protect(async () => {
		await validateForm(form);

		protectOp(async () => {
			try {
				saving.value = true;
				await apiPut({
					url: 'admin/user/updateCurrentUserInfo',
					data: { username: sessionInfo.value!.username, name: model.value.name },
				});
				await reloadSessionInfo();

				navigateBack();
			} finally {
				saving.value = false;
			}
		});
	});
}
</script>
