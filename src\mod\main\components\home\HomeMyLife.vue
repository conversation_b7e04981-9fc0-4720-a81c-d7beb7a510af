<template>
	<view class="my-life-section">
		<view class="life-card">
			<!-- 头部标题区域 -->
			<view class="life-header">
				<view class="title-wrapper">
					<view class="title-wrapper-inner">
						<view class="title-indicator"></view>
						<text class="title">{{ t('homeMyLife') }}</text>
					</view>

					<wd-icon name="arrow-right" size="24rpx" color="#1D2129" />
				</view>
				<view class="room-info">
					{{ lifeData.roomInfo }}
				</view>
			</view>

			<!-- 余额卡片区域 -->
			<view class="balance-section">
				<!-- 水电余额 -->
				<view class="balance-card water-electric">
					<view class="balance-amount">{{ lifeData.waterElectricBalance || 0 }} <view class="balance-amount-unit">元</view></view>
					<text class="balance-label">{{ t('homeWaterElectricBalance') }}</text>
				</view>
				<!-- 饭卡余额 -->
				<view class="balance-card meal-card">
					<view class="balance-amount">{{ lifeData.mealCardBalance || 0 }} <view class="balance-amount-unit">元</view></view>
					<text class="balance-label">{{ t('homeMealCardBalance') }}</text>
				</view>
			</view>

			<!-- 功能按钮区域 -->
			<view class="function-section">
				<view
					v-for="(item, index) in lifeData.functions"
					:key="index"
					class="function-item"
					:class="item.className"
					@click="handleFunctionClick(item)"
				>
					<view class="function-item-content">
						<text class="function-label">{{ item.label }}</text>
						<wd-icon name="arrow-right" size="20rpx" color="#666" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps<{
	needRefresh: boolean;
}>();
const { apiGet, protect } = useFramework();

// 生活服务数据接口
interface LifeFunction {
	id: string;
	label: string;
	className: string;
	route?: string;
}

interface MyLifeData {
	roomInfo: string;
	waterElectricBalance: string;
	mealCardBalance: string;
	functions: LifeFunction[];
}

// 模拟生活服务数据
const lifeData = ref<MyLifeData>({
	roomInfo: '5号楼3层5325室',
	waterElectricBalance: '58.8',
	mealCardBalance: '112.5',
	functions: [
		{
			id: 'recharge',
			label: t('homeRecharge'),
			className: 'recharge',
			route: '/pages/life/recharge',
		},
		{
			id: 'accommodation',
			label: t('homeAccommodation'),
			className: 'accommodation',
			route: '/pages/life/accommodation',
		},
		{
			id: 'repair',
			label: t('homeRepair'),
			className: 'repair',
			route: '/pages/life/repair',
		},
		{
			id: 'remote-door',
			label: t('homeRemoteDoor'),
			className: 'remote-door',
			route: '/pages/life/remote-door',
		},
	],
});

watch(
	() => props.needRefresh,
	async (newVal) => {
		if (newVal) {
			await loadData();
		}
	}
);

async function loadData() {
	await protect(async () => {
		const d = await apiGet('mesmultidata/DiwShiftSchedule/hisToTodaylist');
		lifeData.value = d || [];
	});
}

// 处理功能点击事件
const handleFunctionClick = (item: LifeFunction) => {
	console.log('Function clicked:', item.label);
	// 这里可以添加路由跳转逻辑
	if (item.route) {
		// TODO: 实现具体的页面跳转逻辑
		console.log('Navigate to:', item.route);
	}
};
</script>

<style lang="scss" scoped>
.my-life-section {
	margin-top: 26rpx;
	width: 702rpx;
	min-height: 334rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 702rpx 334rpx;
	background-repeat: no-repeat;
	background-position: top center;
	background-color: #feffff;
	border-radius: 24rpx;
	border: 1px solid #ffffff;
}

.life-card {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	margin: 32rpx 24rpx 0rpx 24rpx;
}

/* 头部标题样式 */
.life-header {
	margin: 0 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title-wrapper {
		display: flex;
		align-items: center;
		gap: 18rpx;

		.title-wrapper-inner {
			display: flex;
			align-items: center;

			.title-indicator {
				width: 8rpx;
				height: 32rpx;
				background: #0082f0;
				border-radius: 4rpx;
				margin-right: 16rpx;
			}

			.title {
				font-size: 30rpx;
				font-weight: normal;
				line-height: 24rpx;
				color: #1d2129;
			}
		}
	}

	.room-info {
		font-size: 24rpx;
		font-weight: normal;
		line-height: 24rpx;
		text-align: center;
		letter-spacing: 0rpx;
		display: flex;
		align-items: center;
		color: #4b5563;
	}
}

/* 余额卡片样式 */
.balance-section {
	margin: 37.36rpx 24rpx 0rpx 24rpx;
	display: flex;
	gap: 20rpx;
	border-radius: 20rpx;

	.balance-card {
		flex: 1;
		height: 120rpx;
		border-radius: 12rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: relative;

		.balance-amount {
			color: #091f44;
			font-size: 32rpx;
			font-weight: 400;
			line-height: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-wrap: nowrap;

			.balance-amount-unit {
				font-size: 24rpx;
				font-weight: 400;
			}
		}

		.balance-label {
			margin-top: 12rpx;
			font-size: 20rpx;
			font-weight: normal;
			line-height: 28rpx;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0px;
			color: #1d2129;
		}

		&.water-electric {
			background-color: #f8fafc;
			background-image: url('/static/icons/<EMAIL>');
			background-size: cover;
			background-repeat: no-repeat;
			background-position: center;
			border: 1rpx solid rgba(148, 163, 184, 0.1);
		}

		&.meal-card {
			background-color: #f8fafc;
			background-image: url('/static/icons/<EMAIL>');
			background-size: cover;
			background-repeat: no-repeat;
			background-position: center;
			border: 1rpx solid rgba(148, 163, 184, 0.1);
		}
	}
}

/* 功能按钮样式 */
.function-section {
	margin: 16rpx 24rpx 20rpx 24rpx;
	display: flex;
	flex-wrap: no-wrap;
	justify-content: space-between;
	align-items: center;
	gap: 20rpx;

	.function-item {
		width: 150rpx;
		height: 92rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: flex-start;
		justify-content: center;

		.function-item-content {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			margin-top: 24rpx;
		}

		.function-label {
			font-size: 24rpx;
			font-weight: normal;
			line-height: 28rpx;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0px;
			color: #091f44;
		}

		// 不同功能项的背景图片样式，匹配设计图
		&.recharge {
			background-color: #fdf2f8;
			background-image: url('/static/icons/<EMAIL>');
			background-size: 150rpx 92rpx;
			background-repeat: no-repeat;
			background-position: center;
		}

		&.accommodation {
			background-color: #fff7ed;
			background-image: url('/static/icons/<EMAIL>');
			background-size: 150rpx 92rpx;
			background-repeat: no-repeat;
			background-position: center;
		}

		&.repair {
			background-color: #f8fafc;
			background-image: url('/static/icons/<EMAIL>');
			background-size: 150rpx 92rpx;
			background-repeat: no-repeat;
			background-position: center;
		}

		&.remote-door {
			background-color: #f0fdf4;
			background-image: url('/static/icons/<EMAIL>');
			background-size: 150rpx 92rpx;
			background-repeat: no-repeat;
			background-position: center;
		}
	}
}
</style>
