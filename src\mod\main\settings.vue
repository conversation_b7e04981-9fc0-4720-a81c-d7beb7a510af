<template>
	<DIWAppPage>
		<DIWProtect>
			<view class="absolute w-full h-full inset-0 flex flex-col">
				<wd-gap />
				<wd-cell-group border>
					<wd-cell is-link title="头像" clickable @click="editAvatar()">
						<template #default>
							<wd-img v-if="sessionInfo && sessionInfo.avatarUrl" :src="sessionInfo!.avatarUrl" :width="20" :height="20" mode="aspectFill" />
						</template>
					</wd-cell>
					<wd-cell is-link title="名字" :value="sessionInfo!.name" clickable @click="editName()" />
					<wd-cell is-link title="邮箱" :value="sessionInfo!.email" clickable @click="editEmail()" />
					<wd-cell is-link title="手机号码" :value="sessionInfo!.phone" clickable @click="editPhone()" />
				</wd-cell-group>

				<wd-gap />
				<wd-cell-group border>
					<wd-cell is-link title="修改密码" clickable @click="modifyPassword()"></wd-cell>
					<wd-cell is-link title="意见反馈" clickable @click="gotoFeedback()"></wd-cell>
					<wd-cell v-if="versionText" title="版本号" is-link :value="versionText" clickable @click="gotoVer()" />
				</wd-cell-group>

				<template v-if="false">
					<wd-gap />
					<wd-cell-group border>
						<wd-cell title-width="100%" clickable @click="switchCompany">
							<template #title>
								<text class="block text-center">切换企业</text>
							</template>
						</wd-cell>
					</wd-cell-group>
				</template>

				<wd-gap />
				<wd-cell-group border>
					<wd-cell title-width="100%" clickable @click="doLogout">
						<template #title>
							<text class="block text-center">退出登录</text>
						</template>
					</wd-cell>
				</wd-cell-group>

				<view class="flex-1" />

				<view class="mt-16 text-center">
					<wd-button type="error" size="large" custom-class="min-w-260px" @click="deleteAccount">注销账号</wd-button>
				</view>

				<wd-gap />
				<wd-gap />
				<wd-gap safe-area-bottom />
			</view>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { sessionInfo, protect, logout, navigateTo } = useFramework();

const versionText = computed(() => {
	return uni.getAppBaseInfo().appVersion;
});

function editAvatar() {
	protect(async () => {
		await navigateTo('/mod/main/editAvatar');
	});
}

function editName() {
	protect(async () => {
		await navigateTo('/mod/main/editName');
	});
}

function editEmail() {
	protect(async () => {
		await navigateTo('/mod/main/editEmail');
	});
}

function editPhone() {
	protect(async () => {
		await navigateTo('/mod/main/editPhone');
	});
}

function modifyPassword() {
	protect(async () => {
		await navigateTo('/mod/main/modifyPassword');
	});
}

function switchCompany() {
	// TODO: 需要重新实现企业切换功能
	// 当前API返回值中没有companyList，需要调研是否有单独的接口获取企业列表
	uni.showToast({
		title: '企业切换功能暂时不可用',
		icon: 'none',
	});
}

function doLogout() {
	protect(async () => {
		await logout();
		await uni.reLaunch({ url: '/mod/mall/home' });
	});
}

function gotoFeedback() {
	protect(async () => {
		await navigateTo('/mod/mall/feedback');
	});
}

function gotoVer() {
	protect(async () => {
		await navigateTo('/mod/main/ver');
	});
}

function deleteAccount() {
	protect(async () => {
		await navigateTo('/mod/main/deleteAccount');
	});
}
</script>
