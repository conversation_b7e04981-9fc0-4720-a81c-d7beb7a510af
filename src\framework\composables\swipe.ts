import { useTouch } from '@/ui/wot-design-uni/components/composables/useTouch';

export function useSwipe(callback: (deltaX: number) => void, minSwipeDistance: number = 50) {
	const { touchStart, touchMove, direction, deltaX, offsetX } = useTouch();

	function onTouchStart(ev: any) {
		touchStart(ev);
	}

	function onTouchMove(ev: any) {
		touchMove(ev);
	}

	function onTouchEnd() {
		if (direction.value === 'horizontal' && offsetX.value >= minSwipeDistance) {
			callback(deltaX.value);
		}
	}

	return { onTouchStart, onTouchMove, onTouchEnd };
}
