<template>
	<DIWRoot v-if="mode === 0">
		<template v-if="error && reloadFailed">
			<slot name="error">
				<view @click="reload(false)" class="loading-width">
					<wd-status-tip image="network" tip="数据加载失败" />
				</view>
			</slot>
		</template>
		<template v-else>
			<!-- #ifdef H5 -->
			<view :class="[props.layout === 'grid' ? `diw-list-grid diw-grid-columns-${props.columns}` : '', props.useFlex ? 'diw-use-flex' : '']">
				<template v-for="(item, itemIndex) in itemList" :key="item.key">
					<slot :data="item.data" :index="itemIndex" />
				</template>
			</view>
			<!-- #endif -->

			<!-- #ifdef MP-WEIXIN -->
			<view :class="props.layout === 'grid' ? 'diw-list-flex' : ''">
				<template v-for="(item, itemIndex) in itemList" :key="item.key">
					<view :class="props.layout === 'grid' ? `diw-flex-item diw-flex-columns-${props.columns}` : ''">
						<slot :data="item.data" :index="itemIndex" />
					</view>
				</template>
			</view>
			<!-- #endif -->

			<!-- #ifndef H5 || MP-WEIXIN -->
			<view :class="[props.layout === 'grid' ? `diw-list-grid diw-grid-columns-${props.columns}` : '', props.useFlex ? 'diw-use-flex' : '']">
				<template v-for="(item, itemIndex) in itemList" :key="item.key">
					<slot :data="item.data" :index="itemIndex" />
				</template>
			</view>
			<!-- #endif -->

			<template v-if="props.showMore">
				<template v-if="itemList.length > 0">
					<slot name="more">
						<view class="loading-width">
							<wd-loadmore :state="error && !loadingMore ? 'error' : hasMore ? 'loading' : 'finished'" @reload="loadMore" />
						</view>
					</slot>
				</template>
				<template v-else-if="initLoaded">
					<slot name="empty">
						<view class="loading-width">
							<wd-status-tip image="content" tip="暂无数据" />
						</view>
					</slot>
				</template>
			</template>
		</template>
	</DIWRoot>
	<view v-else :class="customClass" :style="customStyle">
		<!-- #ifdef H5 -->
		<view :class="[props.layout === 'grid' ? `diw-list-grid diw-grid-columns-${props.columns}` : '', props.useFlex ? 'diw-use-flex' : '']">
			<template v-for="(item, itemIndex) in itemList" :key="item.key">
				<slot :data="item.data" :index="itemIndex" />
			</template>
		</view>
		<!-- #endif -->

		<!-- #ifdef MP-WEIXIN -->
		<view :class="props.layout === 'grid' ? 'diw-list-flex' : ''">
			<template v-for="(item, itemIndex) in itemList" :key="item.key">
				<view :class="props.layout === 'grid' ? `diw-flex-item diw-flex-columns-${props.columns}` : ''">
					<slot :data="item.data" :index="itemIndex" />
				</view>
			</template>
		</view>
		<!-- #endif -->

		<!-- #ifndef H5 || MP-WEIXIN -->
		<view :class="[props.layout === 'grid' ? `diw-list-grid diw-grid-columns-${props.columns}` : '', props.useFlex ? 'diw-use-flex' : '']">
			<template v-for="(item, itemIndex) in itemList" :key="item.key">
				<slot :data="item.data" :index="itemIndex" />
			</template>
		</view>
		<!-- #endif -->
	</view>
</template>

<script setup lang="ts">
import { injectDIWScrollManager } from '@/framework';

interface DIWListViewProps {
	// 0 - 只具备基本的数据展示，需要嵌入到其他 DIWScrollView 或者直接放在 DIWAppPage 中，此时没有根结点，customClass 和 customStyle 无效
	// 1 - 此时 DIWListVIew 是一个自带 DIWScrollView 的完整组件，可以嵌入到其他组件中
	// 默认值为 0
	mode?: 0 | 1 | '0' | '1';

	meta: DIWListViewMeta<any>;

	// 根结点 class
	customClass?: DIWClassType;

	// 根结点 style
	customStyle?: DIWStyleType;

	// 布局类型：list-列表布局，grid-网格布局
	layout?: 'list' | 'grid';

	// 网格列数，仅在grid布局时有效
	columns?: number;

	// 是否使用flex布局代替grid布局
	useFlex?: boolean;

	// 是否显示底部加载更多信息
	showMore?: boolean;
}

const props = withDefaults(defineProps<DIWListViewProps>(), {
	mode: 0,
	customClass: 'diw-list-view',
	layout: 'list',
	columns: 2,
	useFlex: false,
	showMore: true,
});

const mode = computed(() => (props.mode === '0' ? 0 : props.mode === '1' ? 1 : props.mode));
const customClass = computed(() => parseClass(props.customClass));
const customStyle = computed(() => parseStyle(props.customStyle));

const { protect } = useFramework();

interface Item {
	key: string;
	data: Record<string, any>;
	meta: {};
}

const itemList = ref<Item[]>([]);
const rawItemList = ref<Record<string, any>[]>([]);
const hasMore = ref(true);
const error = ref(false);
const reloadFailed = ref(false);
const loadingMore = ref(false);
let trackInfo: any = undefined;
const initLoaded = ref(false);

let isVisible = false;
let pendingReload = false;
let pendingClear = false;

const scrollManager = injectDIWScrollManager();

if (scrollManager) {
	scrollManager.onPulldownRefresh(() => {
		loadImpl(true).finally(() => {
			initLoaded.value = true;
			scrollManager.stopPulldownRefresh();
		});
	});

	scrollManager.onReachBottom(() => {
		loadMore();
	});

	onMounted(() => {
		reload();
	});
}

function reload(clear?: boolean) {
	if (!isVisible) {
		pendingReload = true;
		pendingClear = clear === true;
		return;
	}
	if (scrollManager) {
		nextTick(() => {
			if (clear) {
				itemList.value = [];
			}
			scrollManager.startPulldownRefresh();
		});
	}
}

function reload2(clear?: boolean) {
	if (!isVisible) {
		pendingReload = true;
		pendingClear = clear === true;
		return;
	}
	if (scrollManager) {
		nextTick(() => {
			if (clear) {
				itemList.value = [];
			}
			loadImpl(true);
		});
	}
}

function loadMore() {
	if (hasMore.value) {
		if (!loadingMore.value) {
			loadingMore.value = true;
			loadImpl(false).finally(() => {
				loadingMore.value = false;
			});
		}
	}
}

async function loadImpl(isReload: boolean) {
	return protect(async () => {
		try {
			const rowKey = props.meta.rowKey || 'id';

			const k = isReload ? props.meta.loadData() : props.meta.loadData(trackInfo);
			const d = await Promise.resolve(k);

			const hasMore0 = d.hasMore;
			trackInfo = d.trackInfo;

			if (hasMore0) {
				trackInfo = d.trackInfo;
				hasMore.value = true;
			} else {
				trackInfo = undefined;
				hasMore.value = false;
			}

			const items = d.items.map((e) => {
				return {
					key: e[rowKey],
					data: e,
					meta: {},
				};
			});

			if (isReload) {
				itemList.value = items;
				rawItemList.value = items;
			} else {
				itemList.value.splice(itemList.value.length, 0, ...items);
				rawItemList.value.splice(itemList.value.length, 0, ...items);
			}
			error.value = false;
		} catch (err) {
			reloadFailed.value = isReload;
			error.value = true;
			throw err;
		} finally {
		}
	});
}

function visitItems(callback: (row: number, data: Record<string, any>) => any) {
	for (let i = 0; i < rawItemList.value.length; ++i) {
		callback(i, rawItemList.value[i].data);
	}
}

onHide(() => {
	isVisible = false;
});

onShow(() => {
	isVisible = true;
	if (pendingReload) {
		// 适当的延迟，确保在各个平台都有效
		setTimeout(() => {
			pendingReload = false;
			reload(pendingClear);
		}, 200);
	}
});

defineExpose({
	reload,
	reload2,
	visitItems,
});
</script>

<style lang="scss">
.diw-list-view {
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
}

.loading-width {
	width: 100%;
}

.diw-list-grid {
	display: grid;
	gap: 20rpx;
	width: 100%;
}

.diw-grid-columns-2 {
	grid-template-columns: repeat(2, 1fr);
}

.diw-grid-columns-3 {
	grid-template-columns: repeat(3, 1fr);
}

.diw-grid-columns-4 {
	grid-template-columns: repeat(4, 1fr);
}

/* 使用flex布局的网格 */
.diw-use-flex {
	display: flex !important;
	flex-wrap: wrap;
	gap: 0;
}

/* 微信小程序特有的布局样式 */
.diw-list-flex {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
}

.diw-flex-item {
	padding: 10rpx;
	box-sizing: border-box;
}

.diw-flex-columns-2 {
	width: 50%;
}

.diw-flex-columns-3 {
	width: 33.33%;
}

.diw-flex-columns-4 {
	width: 25%;
}
</style>
