<template>
	<DIWAppPage mode="1">
		<template #appBar>
			<DIWActionBar>
				<view class="flex flex-row w-full border-rounded border-solid border-gray-200 border-1px items-center mr-2">
					<picker :range="readFilters" range-key="label" :value="readFilterIndex" @change="handleReadFilterChange">
						<view class="h-28px flex flex-row items-center px-2 border-solid border-gray-200 border-0 border-r-1" hover-class="bg-gray-300">
							<text class="text-xs text-gray-7">{{ readFilterText }}</text>
						</view>
					</picker>
					<input class="flex-1 px-2 text-sm" v-model="searchModel.content" confirm-type="search" inputmode="text" />
					<view
						class="h-28px w-36px text-center flex flex-row items-center justify-center border-solid border-gray-200 border-0 border-l-1"
						hover-class="bg-gray-300"
						@click="reload()"
					>
						<text class="i-ep-search block" />
					</view>
				</view>
				<wd-button size="small" @click="markAll">全部已读</wd-button>
			</DIWActionBar>
		</template>
		<DIWScrollView>
			<view class="px-3 pb-4 mt-3">
				<DIWListView ref="listView" :meta="listViewMeta">
					<template #default="{ data, index }">
						<view class="message-card bg-white rounded-lg shadow mb-3 overflow-hidden" @click="handleMessageClick(data)">
							<view class="p-4">
								<view class="flex flex-col gap-2">
									<!-- 消息头部 -->
									<view class="flex flex-row justify-between items-center">
										<text class="text-lg font-medium text-blue-700">{{ data.title }}</text>
										<view>
											<wd-tag v-if="data.readFlag == 1" type="success" size="small">已读</wd-tag>
											<wd-tag v-else type="warning" size="small">未读</wd-tag>
										</view>
									</view>

									<!-- 消息时间 -->
									<view class="text-sm text-gray-500">{{ data.createTime }}</view>

									<!-- 消息内容 -->
									<text class="text-sm break-all py-2">{{ data.content }}</text>
								</view>
							</view>
						</view>
					</template>
				</DIWListView>
			</view>
		</DIWScrollView>
	</DIWAppPage>
</template>

<script setup lang="ts">
import DIWActionBar from '@/components/DIWActionBar/DIWActionBar.vue';

const { apiGet, protect, protectOp, markMessageAsRead, navigateTo, markAllMessagesAsRead } = useFramework();

const listView = ref();

const searchModel = ref({ category: 1, descs: [['create_time']], content: '', readFlag: '' });

const readFilters = ref([
	{ label: '全部', value: '' },
	{ label: '未读', value: '0' },
	{ label: '已读', value: '1' },
]);

const readFilterIndex = ref(0);

const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(trackInfo) {
			const size1 = 20;
			const size2 = 5;

			const isReload = trackInfo === undefined;

			let current = isReload ? 1 : trackInfo;
			let size = isReload ? size1 : size2;

			// TODO: 应该从 store 读取
			const d = await apiGet({
				url: 'msg/sysMessage/user/page',
				params: {
					current,
					size,
					...searchModel.value,
				},
			});

			const items = d.records;

			if (isReload) {
				return { hasMore: d.total > size, items, trackInfo: 1 + size1 / size2 };
			}

			return { hasMore: d.total > current * size, items, trackInfo: current + 1 };
		},
	});
});

const readFilterText = computed(() => readFilters.value[readFilterIndex.value].label);

function handleReadFilterChange(ev: { detail: { value: number } }) {
	readFilterIndex.value = ev.detail.value;
	searchModel.value.readFlag = readFilters.value[ev.detail.value].value;
	reload();
}

function reload() {
	listView.value.reload();
}

function markAll() {
	protectOp('全部标记为已读，确定吗？', async () => {
		await markAllMessagesAsRead();
		reload();
	});
}

function handleMessageClick(data: Record<string, any>) {
	if (!data.readFlag) {
		protect(async () => {
			// 标记为已读
			await markMessageAsRead(data.id);
			data.readFlag = 1;
		});
	}

	const mp = parseJSON(data.param);

	console.log('handleMessageClick', data.gotoUrl, mp);

	switch (data.gotoUrl) {
		case 'order_buyer_list':
		case 'order_buyer_detail':
			navigateTo({ url: '/mod/order/buyerOrderList', params: { orderNo: mp.orderNo } });
			break;

		case 'order_seller_list':
		case 'order_seller_detail':
			navigateTo({ url: '/mod/order/sellerOrderList', params: { orderNo: mp.orderNo } });
			break;

		case 'ahead_lock_page':
		case 'ahead_lock_detail':
			navigateTo({ url: '/mod/deliver/lockBatchDetail', params: { id: mp.orderLockId } });
			break;

		case 'order_out_bill_complete_user':
			navigateTo({ url: '/mod/deliver/confirm' });
			break;

		case 'deliver_detail':
		case 'deliver_page':
			navigateTo({ url: '/mod/deliver/deliverDetail', params: { id: mp.deliverId } });
			break;

		case 'extracting_page':
		case 'extracting_detail':
			navigateTo({ url: '/mod/deliver/pickupInfoAuditDetail', params: { id: mp.recordId } });
			break;

		case 'shipping_page':
		case 'shipping_detail':
			navigateTo({ url: '/mod/deliver/addressAuditDetail', params: { id: mp.recordId } });
			break;

		case 'invoice_page':
			break;

		case 'order_out_bill_complete_admin':
			break;

		case 'order_out_bill_pass':
			break;

		case 'order_out_bill_reject':
			break;

		case 'order_out_notice_create_bill':
			break;

		case 'account_confirms_receipt':
			break;

		default:
			console.warn('UNKNOWN', data.gotoURL, JSON.stringify(mp));
			break;
	}
}
</script>

<style>
.message-card {
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
</style>
