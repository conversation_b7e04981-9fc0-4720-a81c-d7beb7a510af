@use '../../common/abstracts/variable' as *;
@use '../../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(month-panel) {
		@include e(title) {
			color: $wot-dark-color;
		}

		@include e(weeks) {
			box-shadow: 0px 4px 8px 0 rgba(255, 255, 255, 0.02);
			color: $wot-dark-color;
		}

		@include e(time-label) {
			color: $wot-dark-color;
			&::after {
				background: $wot-dark-background4;
			}
		}
	}
}

@include b(month-panel) {
	font-size: $wot-calendar-fs;

	@include e(title) {
		padding: 5px 0;
		text-align: center;
		font-size: $wot-calendar-panel-title-fs;
		color: $wot-calendar-panel-title-color;
		padding: $wot-calendar-panel-padding;
	}

	@include e(weeks) {
		display: flex;
		height: $wot-calendar-week-height;
		line-height: $wot-calendar-week-height;
		box-shadow: 0px 4px 8px 0 rgba(0, 0, 0, 0.02);
		color: $wot-calendar-week-color;
		font-size: $wot-calendar-week-fs;
		padding: $wot-calendar-panel-padding;
	}

	@include e(week) {
		flex: 1;
		text-align: center;
	}

	@include e(container) {
		padding: $wot-calendar-panel-padding;
		box-sizing: border-box;
	}

	@include e(time) {
		display: flex;
		box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.02);
	}

	@include e(time-label) {
		position: relative;
		flex: 1;
		font-size: $wot-picker-column-fs;
		text-align: center;
		line-height: 125px;
		color: $wot-picker-column-color;

		&::after {
			position: absolute;
			content: '';
			height: 35px;
			top: 50%;
			left: 0;
			right: 0;
			transform: translateY(-50%);
			background: $wot-picker-column-select-bg;
			z-index: 0;
		}
	}

	@include e(time-text) {
		position: relative;
		z-index: 1;
	}

	@include e(time-picker) {
		flex: 3;
	}
}
