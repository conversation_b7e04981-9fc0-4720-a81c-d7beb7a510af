@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(navbar) {
		background-color: $wot-dark-background;

		@include e(title) {
			color: $wot-dark-color;
		}

		@include e(text) {
			color: $wot-dark-color;
		}

		:deep(.wd-navbar__arrow) {
			color: $wot-dark-color;
		}
	}
}

@include b(navbar) {
	position: relative;
	text-align: center;
	user-select: none;
	height: $wot-navbar-height;
	line-height: $wot-navbar-height;
	background-color: $wot-navbar-background;
	box-sizing: content-box;

	@include e(content) {
		position: relative;
		height: 100%;
		width: 100%;
	}

	@include e(title) {
		max-width: 60%;
		height: 100%;
		margin: 0 auto;
		color: $wot-navbar-color;
		font-weight: $wot-navbar-title-font-weight;
		font-size: $wot-navbar-title-font-size;
		@include lineEllipsis();
	}

	@include e(text) {
		display: inline-block;
		vertical-align: middle;
		color: $wot-navbar-desc-font-color;
	}

	@include e(left, right, capsule) {
		position: absolute;
		top: 0;
		bottom: 0;
		font-size: $wot-navbar-desc-font-size;
		display: flex;
		align-items: center;
		padding: 0 12px;

		@include when(disabled) {
			opacity: $wot-navbar-disabled-opacity;
		}
	}

	@include e(left, capsule) {
		left: 0;
	}

	@include e(right) {
		right: 0;
	}

	@include edeep(arrow) {
		font-size: $wot-navbar-arrow-size;
		color: $wot-navbar-color;
	}

	@include when(border) {
		@include halfPixelBorder('bottom');
	}

	@include when(fixed) {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 500;
	}
}
