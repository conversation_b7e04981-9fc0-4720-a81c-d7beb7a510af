@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(input) {
		background: $wot-dark-background2;

		&::after {
			background: $wot-dark-color-gray;
		}

		@include when(not-empty) {
			&:not(.is-disabled) {
				&::after {
					background-color: $wot-dark-color;
				}
			}
		}

		@include e(inner) {
			color: $wot-dark-color;

			&::-webkit-input-placeholder {
				color: $wot-dark-color3;
			}
		}

		@include e(count) {
			color: $wot-dark-color3;
			background: transparent;
		}

		@include e(count-current) {
			color: $wot-dark-color;
		}

		:deep(.wd-input__icon),
		:deep(.wd-input__clear) {
			color: $wot-dark-color;
			background: transparent;
		}

		@include when(cell) {
			background-color: $wot-dark-background2;
			line-height: $wot-cell-line-height;

			@include when(border) {
				@include halfPixelBorder('top', $wot-input-cell-padding, $wot-dark-border-color);
			}
		}

		@include when(disabled) {
			.wd-input__inner {
				color: $wot-dark-color-gray;
				background: transparent;
			}
		}

		@include e(label) {
			color: $wot-dark-color;
		}
	}
}

@include b(input) {
	position: relative;
	-webkit-tap-highlight-color: transparent;
	text-align: left;
	background: $wot-input-bg;

	&::after {
		position: absolute;
		content: '';
		bottom: 0;
		left: 0;
		right: 0;
		height: 1px;
		background: $wot-input-border-color;
		transform: scaleY(0.5);
		transition: background-color 0.2s ease-in-out;
	}

	@include when(not-empty) {
		&:not(.is-disabled) {
			&::after {
				background-color: $wot-input-not-empty-border-color;
			}
		}
	}

	@include e(label) {
		position: relative;
		display: flex;
		width: $wot-input-cell-label-width;
		color: $wot-cell-title-color;
		margin-right: $wot-cell-padding;
		box-sizing: border-box;
		font-size: $wot-input-fs;
		flex-shrink: 0;

		@include when(required) {
			padding-left: 12px;

			&::after {
				position: absolute;
				left: 0;
				top: 2px;
				content: '*';
				font-size: $wot-cell-required-size;
				line-height: 1.1;
				color: $wot-cell-required-color;
			}
		}
	}

	@include e(label-inner) {
		display: inline-block;
		font-size: $wot-input-fs;
		line-height: $wot-cell-line-height;
	}

	@include e(body) {
		flex: 1;
	}

	@include e(value) {
		position: relative;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	@include e(prefix) {
		margin-right: $wot-input-icon-margin;
		font-size: $wot-input-fs;
		line-height: initial;

		:deep(.wd-input__icon),
		:deep(.wd-input__clear) {
			margin-left: 0;
		}
	}

	@include e(suffix) {
		flex-shrink: 0;
		line-height: initial;
	}

	@include e(error-message) {
		color: $wot-form-item-error-message-color;
		font-size: $wot-form-item-error-message-font-size;
		line-height: $wot-form-item-error-message-line-height;
		text-align: left;
		vertical-align: middle;
	}

	@include when(disabled) {
		.wd-input__inner {
			color: $wot-input-disabled-color;
			background: transparent;
		}
	}

	@include when(error) {
		.wd-input__inner {
			color: $wot-input-error-color;
			background: transparent;
		}
	}

	@include when(no-border) {
		&::after {
			display: none;
		}

		.wd-input__inner {
			height: $wot-input-inner-height-no-border;
			padding-top: 0;
			padding-bottom: 0;
		}
	}

	@include when(cell) {
		display: flex;
		align-items: flex-start;
		padding: $wot-input-cell-padding $wot-input-padding;
		background-color: $wot-input-cell-bg;

		&.is-error::after {
			background: $wot-input-cell-border-color;
		}

		:deep(.wd-input__icon),
		:deep(.wd-input__clear) {
			display: inline-flex;
			align-items: center;
			height: $wot-input-cell-height;
			line-height: $wot-input-cell-height;
		}

		.wd-input__prefix {
			display: inline-block;
			margin-right: $wot-cell-icon-right;
		}

		.wd-input__inner {
			height: $wot-input-cell-height;
		}

		&.wd-input::after {
			display: none;
		}

		@include when(center) {
			align-items: center;
		}

		@include when(border) {
			@include halfPixelBorder('top', $wot-input-cell-padding);
		}
	}

	@include when(large) {
		padding: $wot-input-cell-padding-large;

		.wd-input__prefix {
			font-size: $wot-input-fs-large;
		}

		.wd-input__label-inner {
			font-size: $wot-input-fs-large;
		}

		.wd-input__inner {
			font-size: $wot-input-fs-large;
		}

		.wd-input__count {
			font-size: $wot-input-count-fs-large;
		}

		:deep(.wd-input__icon),
		:deep(.wd-input__clear) {
			font-size: $wot-input-icon-size-large;
		}
	}

	@include e(inner) {
		flex: 1;
		height: $wot-input-inner-height;
		font-size: $wot-input-fs;
		color: $wot-input-color;
		outline: none;
		border: none;
		background: none;
		padding: 0;
		box-sizing: border-box;

		&::-webkit-input-placeholder {
			color: $wot-input-placeholder-color;
		}

		@include when(align-right) {
			text-align: right;
		}
	}

	@include e(readonly-mask) {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 2;
		width: 100%;
		height: 100%;
	}

	@include edeep(icon) {
		margin-left: $wot-input-icon-margin;
		font-size: $wot-input-icon-size;
		color: $wot-input-icon-color;
		vertical-align: middle;
		background: $wot-input-bg;
	}

	@include edeep(clear) {
		margin-left: $wot-input-icon-margin;
		font-size: $wot-input-icon-size;
		color: $wot-input-clear-color;
		vertical-align: middle;
		background: $wot-input-bg;
	}

	@include e(count) {
		margin-left: 15px;
		font-size: $wot-input-count-fs;
		color: $wot-input-count-color;
		vertical-align: middle;
		background: $wot-input-bg;
	}

	@include e(count-current) {
		color: $wot-input-count-current-color;

		@include when(error) {
			color: $wot-input-error-color;
		}
	}

	.wd-input__count,
	.wd-input__count-current {
		display: inline-flex;
	}
}
