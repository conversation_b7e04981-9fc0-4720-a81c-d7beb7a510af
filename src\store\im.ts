import { useSystemStore } from './system';
import { useUpload } from './upload';
import { connectWS } from '@/util/transport';

const PING_MESSAGE = JSON.stringify({ type: 'ping' });

// 多久发送一次 PING 包
const PING_INTERVAL = 10 * 1000;

// 多久没收到任何消息则认为是断线了
const MESSAGE_DEAD_INTERVAL = 60 * 1000;

// 检测到断线后，等待多久开始尝试重连
const RECONNECT_DELAY = 3 * 1000;

function convertToDate(s: any) {
	if (typeof s === 'string') {
		return new Date(s.replace(/-/g, '/'));
	}

	if (typeof s === 'number') {
		return new Date(s);
	}

	if (s instanceof Date) {
		return s;
	}

	return new Date();
}

function makePromiseArray<T>() {
	const ls: Array<{ resolve(value: T | PromiseLike<T>): void; reject(reason?: any): void }> = [];

	function add(): Promise<T> {
		return new Promise<T>((resolve, reject) => {
			ls.push({ resolve, reject });
		});
	}

	function resolve(value: T | PromiseLike<T>) {
		ls.forEach((e) => e.resolve(value));
	}

	function reject(reason?: any) {
		ls.forEach((e) => e.reject(reason));
	}

	return { add, resolve, reject };
}

export const useIMStore = defineStore('im', () => {
	const ss = useSystemStore();
	const { apiGet } = ss;
	const { sessionInfo } = storeToRefs(ss);

	const { mapFileIdToUrl } = useUpload();

	const conversations = ref<IMConversation[]>([]);

	const totalUnread = computed(() => {
		return conversations.value.reduce((a, b) => a + b.unread, 0);
	});

	let socketTask: IMTransport | null = null;

	let pendingConnection: ReturnType<typeof connectWS> | null = null;

	let pendingLoad: ReturnType<typeof loadConversations> | null = null;

	let keepAliveTimer: number | null = null;
	let reconnectTimer: number | null = null;
	let lastReceivedTime: number | null = null;

	let restartImpl: (() => void) | null = null;

	function checkConnection() {
		if (restartImpl && !socketTask) {
			restartImpl();
		}
	}

	async function loadConversationsImpl() {
		if (sessionInfo.value) {
			// 服务商查询商家和用户列表
			const d = await Promise.all([
				apiGet({ url: 'chat/imConversation/list', params: { userTenantType: 1 } }),
				apiGet({ url: 'chat/imConversation/list', params: { userTenantType: 3 } }),
			]);

			return d;
		}

		return [];
	}

	function loadConversations() {
		let abortFlag = false;

		const task = new Promise<any[]>((resolve, reject) => {
			loadConversationsImpl()
				.then((d) => {
					if (!abortFlag) {
						resolve(d);
					}
				})
				.catch((err) => {
					if (!abortFlag) {
						reject(err);
					}
				});
		});

		function abort() {
			abortFlag = true;
		}

		return {
			task,
			abort,
		};
	}

	function syncConversations(d: any[]) {
		const ls: IMConversation[] = [];
		d.forEach((f) => {
			f.forEach((ci: any) => {
				ci.imUserList.forEach((u: any) => {
					let conv = makeConversation(ci, u);
					conv.tenantType = ci.companyInfo.type;
					ls.push(conv);
				});
			});
		});

		return ls;
	}

	async function getMessageListImpl(userId: string, userTenantType: string, size: number, msgId?: string) {
		const d = await apiGet({
			url: 'chat/imMessage/list',
			params: { userId, userTenantType, size, msgId },
		});

		if (Array.isArray(d) && d.length > 0) {
			const ls: IMMessage[] = [];
			const myUserId = sessionInfo?.value?.userId;
			d.forEach((e: Record<string, any>) => {
				let m: IMMessage | null = null;

				switch (e.content.contentType) {
					case 1:
						m = {
							type: 0,
							id: e.msgId,
							isMine: e.fromUser === myUserId,
							time: convertToDate(e.sendTime),
							content: e.content.contentData,
							readFlag: !!e.readTime,
						};
						break;

					case 2:
						m = {
							type: 1,
							id: e.msgId,
							isMine: e.fromUser === myUserId,
							time: convertToDate(e.sendTime),
							fileId: e.content.contentData,
							readFlag: !!e.readTime,
						};
						break;

					case 3:
						m = {
							type: 2,
							id: e.msgId,
							isMine: e.fromUser === myUserId,
							time: convertToDate(e.sendTime),
							orderId: e.content.contentData,
							readFlag: !!e.readTime,
						};
						break;

					case 4:
						m = {
							type: 3,
							id: e.msgId,
							isMine: e.fromUser === myUserId,
							time: convertToDate(e.sendTime),
							productId: e.content.contentData,
							readFlag: !!e.readTime,
						};
						break;
				}

				if (m) {
					ls.push(m);
				}
			});

			return ls;
		}

		return [];
	}

	function makeConversation(ci: any, u: any): IMConversation {
		let loadFromHistoryInProgress: ReturnType<typeof makePromiseArray<boolean>> | null = null;
		let hasMore = true;

		// 检查用户是否有效，isInvalid=1表示无效
		const isInvalid = typeof u.isInvalid === 'number' ? u.isInvalid === 1 : false;

		async function getMessageList(userId: string, userTenantType: string, size: number, msgId?: string) {
			const d = await getMessageListImpl(userId, userTenantType, size, msgId);
			if (msgId === undefined && conv.messages.length > 0) {
				// 这里存在一个竞争关系：如果开始调用接口加载数据时，msgId === undefine 即 messages.length === 0
				// 而加载完数据后，messages.length > 0
				// 说明加载的数据已经不可靠，可能会与 messages 中已经记录的重叠
				// 这时需要指定位置重新加载一次
				msgId = conv.messages[conv.messages.length - 1].id;
				return await getMessageListImpl(userId, userTenantType, size, msgId);
			}

			return d;
		}

		async function loadFromHistoryImpl(): Promise<boolean> {
			if (!hasMore) {
				return Promise.resolve(false);
			}

			if (!loadFromHistoryInProgress) {
				loadFromHistoryInProgress = makePromiseArray<boolean>();
				const locked = loadFromHistoryInProgress;

				let msgId: string | undefined = undefined;
				if (conv.messages.length > 0) {
					msgId = conv.messages[conv.messages.length - 1].id;
				}

				getMessageList(u.userId, u.user.tenantType, 10, msgId)
					.then((ls) => {
						conv.messages.push(...ls);
						hasMore = ls.length > 0;
						locked.resolve(hasMore);
					})
					.catch((err) => {
						locked.reject(err);
					})
					.finally(() => {
						loadFromHistoryInProgress = null;
					});
			}

			return loadFromHistoryInProgress.add();
		}

		const conv: IMConversation = {
			type: 0,
			id: `${ci.companyInfo.id}_${u.userId}`,
			title: u.user.name || u.user.username,
			subtitle: ci.companyInfo.name,
			avatarUrl: u.user.avatar ? mapFileIdToUrl(u.user.avatar) : '',
			messages: [],
			unread: typeof u.unread === 'number' ? Math.floor(u.unread) : 0,
			online: typeof u.userOnline === 'number' ? Math.floor(u.userOnline) : 0,
			peerId: u.userId,
			last: parseLastMessage(u),
			tenantType: ci.companyInfo.type,
			isInvalid,

			loadAfter(id: string) {
				const endIndex = conv.messages.findIndex((e) => e.id === id);
				if (endIndex >= 0) {
					return conv.messages.slice(0, endIndex);
				}

				return [];
			},

			async loadMore(limit, id) {
				let index = 0;
				if (typeof id === 'string') {
					const index1 = conv.messages.findIndex((e) => e.id === id);
					if (index1 >= 0) {
						index = index1 + 1;
					}
				}

				const r = conv.messages.slice(index, index + limit);
				if (r.length === limit) {
					return r;
				}

				await loadFromHistoryImpl();
				if (typeof id === 'string') {
					const index1 = conv.messages.findIndex((e) => e.id === id);
					if (index1 >= 0) {
						index = index1 + 1;
					}
				}

				return conv.messages.slice(index, index + limit);
			},

			sendText(content) {
				if (socketTask) {
					socketTask.send(
						JSON.stringify({
							type: 'chat',
							toUser: u.userId,
							toTenantId: u.user.tenantId,
							toTenantType: u.user.tenantType,
							content: {
								contentData: content,
								contentType: 1,
							},
						})
					);
				}
			},

			sendImage(fileId) {
				if (socketTask) {
					socketTask.send(
						JSON.stringify({
							type: 'chat',
							toUser: u.userId,
							toTenantId: u.user.tenantId,
							toTenantType: u.user.tenantType,
							content: {
								contentData: fileId,
								contentType: 2,
							},
						})
					);
				}
			},

			sendOrder(orderId) {
				if (socketTask) {
					socketTask.send(
						JSON.stringify({
							type: 'chat',
							toUser: u.userId,
							toTenantId: u.user.tenantId,
							toTenantType: u.user.tenantType,
							content: {
								contentData: orderId,
								contentType: 3,
							},
						})
					);
				}
			},

			sendProduct(productId) {
				if (socketTask) {
					socketTask.send(
						JSON.stringify({
							type: 'chat',
							toUser: u.userId,
							toTenantId: u.user.tenantId,
							toTenantType: u.user.tenantType,
							content: {
								contentData: productId,
								contentType: 4,
							},
						})
					);
				}
			},

			mark(ids) {
				if (socketTask) {
					socketTask.send(
						JSON.stringify({
							type: 'mark',
							msgIdList: Array.isArray(ids) ? ids : [ids],
						})
					);
				}
			},
		};

		return conv;
	}

	function parseLastMessage(u: any): IMMessage | null {
		if (typeof u.lastContent === 'string' && typeof u.lastMsgId === 'string') {
			try {
				const m = JSON.parse(u.lastContent);
				switch (m.contentType) {
					case 1:
						if (typeof m.contentData === 'string') {
							return { type: 0, content: m.contentData, id: u.lastMsgId, isMine: false, readFlag: false, time: convertToDate(u.lastMsgTime) };
						}
						break;

					case 2:
						if (typeof m.contentData === 'string') {
							return { type: 1, fileId: m.contentData, id: u.lastMsgId, isMine: false, readFlag: false, time: convertToDate(u.lastMsgTime) };
						}
						break;

					case 3:
						if (typeof m.contentData === 'string') {
							return { type: 2, orderId: m.contentData, id: u.lastMsgId, isMine: false, readFlag: false, time: convertToDate(u.lastMsgTime) };
						}
						break;

					case 4:
						if (typeof m.contentData === 'string') {
							return { type: 3, productId: m.contentData, id: u.lastMsgId, isMine: false, readFlag: false, time: convertToDate(u.lastMsgTime) };
						}
						break;
				}
			} catch (err) {}
		}

		return null;
	}

	function updateConversation(ci: any) {
		ci.imUserList.forEach((u: any) => {
			const id = `${ci.companyInfo.id}_${u.userId}`;
			const convIndex = conversations.value.findIndex((e) => e.id === id);
			if (convIndex >= 0) {
				const conv = conversations.value[convIndex];
				conv.unread = typeof u.unread === 'number' ? Math.floor(u.unread) : 0;

				const last = parseLastMessage(u);
				if (last) {
					conv.last = last;
				}

				// 会话更新了，移动到最前面
				if (convIndex > 0) {
					conversations.value.unshift(conversations.value.splice(convIndex, 1)[0]);
				}
			} else {
				// 新的会话，排到最前面
				conversations.value.unshift(makeConversation(ci, u));
			}
		});
	}

	function handleMessage(msg: Record<string, any>) {
		lastReceivedTime = Date.now();
		switch (msg.type) {
			case 'chat': {
				if (sessionInfo.value) {
					let m: IMMessage | null = null;
					let conv: IMConversation | undefined = undefined;

					if (msg.fromTenantId === sessionInfo.value.tenantId && msg.fromUser === sessionInfo.value.userId) {
						const convId = `${msg.toTenantId}_${msg.toUser}`;
						conv = conversations.value.find((e) => e.id === convId);
						if (conv) {
							switch (msg.content.contentType) {
								case 1:
									m = {
										type: 0,
										id: msg.msgId,
										isMine: true,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										content: msg.content.contentData,
									};
									break;
								case 2:
									m = {
										type: 1,
										id: msg.msgId,
										isMine: true,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										fileId: msg.content.contentData,
									};
									break;
								case 3:
									m = {
										type: 2,
										id: msg.msgId,
										isMine: true,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										orderId: msg.content.contentData,
									};
									break;
								case 4:
									m = {
										type: 3,
										id: msg.msgId,
										isMine: true,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										productId: msg.content.contentData,
									};
									break;
							}
						}
					} else if (msg.toTenantId === sessionInfo.value.tenantId && msg.toUser === sessionInfo.value.userId) {
						const convId = `${msg.fromTenantId}_${msg.fromUser}`;
						conv = conversations.value.find((e) => e.id === convId);
						if (conv) {
							switch (msg.content.contentType) {
								case 1:
									m = {
										type: 0,
										id: msg.msgId,
										isMine: false,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										content: msg.content.contentData,
									};
									break;
								case 2:
									m = {
										type: 1,
										id: msg.msgId,
										isMine: false,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										fileId: msg.content.contentData,
									};
									break;
								case 3:
									m = {
										type: 2,
										id: msg.msgId,
										isMine: false,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										orderId: msg.content.contentData,
									};
									break;
								case 4:
									m = {
										type: 3,
										id: msg.msgId,
										isMine: false,
										readFlag: false,
										time: convertToDate(msg.sendTime),
										productId: msg.content.contentData,
									};
									break;
							}
						}
					}

					if (conv && m) {
						if (conv.messages.length === 0 || conv.messages[0].id !== m.id) {
							// 新消息放在顶部
							conv.messages.splice(0, 0, m);
							conv.last = m;
						}
					}
				}

				break;
			}

			case 'tag': {
				updateConversation(msg.connectionDTO);
				break;
			}

			case 'mark': {
				msg.msgIdList.forEach((id: string) => {
					for (const conv of conversations.value) {
						const msg = conv.messages.find((e) => e.id === id);
						if (msg) {
							msg.readFlag = true;
							break;
						}
					}
				});
				break;
			}

			case 'online':
				for (const conv of conversations.value) {
					if (conv.type === 0 && conv.peerId === msg.userId) {
						conv.online = msg.userOnline;
					}
				}
				break;
		}
	}

	async function restart(accessToken: string, tenantId: string) {
		await stop();

		pendingConnection = connectWS(accessToken, tenantId);
		pendingLoad = loadConversations();

		pendingConnection.task
			.then((ws) => {
				console.log('connected!', ws);

				keepAliveTimer = setInterval(() => {
					if (socketTask) {
						socketTask.send(PING_MESSAGE);
					}

					if (lastReceivedTime != null && Date.now() - lastReceivedTime > MESSAGE_DEAD_INTERVAL) {
						if (socketTask) {
							socketTask.close();
						}
					}

					restartImpl = () => {
						restart(accessToken, tenantId);
					};

					if (!socketTask) {
						reconnectTimer = setTimeout(() => {
							restart(accessToken, tenantId);
						}, RECONNECT_DELAY);
					}
				}, PING_INTERVAL);

				ws.onClose(() => {
					console.log('onClose');
					socketTask = null;
				});
				ws.onError(() => {
					console.error('onError');
					socketTask = null;
				});
				ws.onMessage((data) => {
					if (typeof data === 'string') {
						try {
							const msg = JSON.parse(data);
							handleMessage(msg);
						} catch (err) {
							console.error(err);
						}
					}
				});

				socketTask = ws;
			})
			.catch((err) => {
				// TODO: retry
				console.error(err);
			});

		pendingLoad.task
			.then((d) => {
				conversations.value = syncConversations(d);
			})
			.catch((err) => {
				// TODO: retry
				console.error(err);
			});
	}

	async function stop() {
		if (pendingConnection) {
			pendingConnection.abort();
			pendingConnection = null;
		}

		if (pendingLoad) {
			pendingLoad.abort();
			pendingLoad = null;
		}

		if (socketTask) {
			socketTask.close();
			socketTask = null;
		}

		if (reconnectTimer) {
			clearTimeout(reconnectTimer);
			reconnectTimer = null;
		}

		if (keepAliveTimer) {
			clearInterval(keepAliveTimer);
			keepAliveTimer = null;
		}

		restartImpl = null;

		conversations.value = [];
	}

	function close() {
		if (socketTask) {
			socketTask.close();
		}
	}

	return { restart, stop, conversations, totalUnread, close, checkConnection };
});
