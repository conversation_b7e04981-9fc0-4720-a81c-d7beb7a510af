import { useSystemStore } from './system';

export const useAgreementStore = defineStore('dw_agreement', () => {
	const { apiGet } = useSystemStore();

	async function getAgreementByType(type: string): Promise<Record<string, any> | null> {
		const d = await apiGet('admin/sysAgreement/type/' + encodeURI(type));
		if (d) {
			return JSON.parse(d.content);
		}

		return null;
	}

	return { getAgreementByType };
});
