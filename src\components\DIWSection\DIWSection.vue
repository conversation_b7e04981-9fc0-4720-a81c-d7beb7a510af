<template>
	<view v-if="props.title" class="diw-section">
		<view class="diw-section__title">
			<text class="diw-section__title-text">{{ props.title }}</text>
		</view>
		<view>
			<slot />
		</view>
	</view>
	<view v-else class="diw-section">
		<slot />
	</view>
</template>

<script setup lang="ts">
const props = defineProps<{ title?: string }>();
</script>

<style lang="scss">
.diw-section {
	padding-top: 16px;

	&__title {
		margin-left: 8px;
		margin-bottom: 8px;

		&-text {
			color: #808080;
		}
	}
}
</style>
