<template>
	<DIWListView ref="listView" :meta="listViewMeta" :show-more="false">
		<template #default="{ data }">
			<template v-if="sessionInfo">
				<view v-if="data.id === 'all'" class="menu-header">
					<view v-if="topMarginStyle" :style="topMarginStyle" />
					<view class="flex flex-col px-4 pt-4">
						<view class="flex flex-row items-center">
							<view v-if="sessionInfo.avatarUrl" class="rounded overflow-hidden line-height-0 border-white border-width-1px border-solid mr-3">
								<wd-img :width="64" :height="64" :src="sessionInfo.avatarUrl" mode="aspectFill" />
							</view>

							<view class="flex flex-col gap-2 flex-1">
								<view class="flex flex-row gap-3 mr-1">
									<text class="flex-1">{{ sessionInfo.name }}</text>
								</view>
								<view class="flex flex-row gap-3 mr-1 items-center">
									<view class="flex flex-col items-start flex-1">
										<text class="text-sm text-zinc-2">{{ sessionInfo.factoryName }}</text>
										<wd-tag type="primary" plain>{{ tenantName }}</wd-tag>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="flex flex-row items-center justify-end">
						<view class="p-2 flex items-center" @click="navigateTo('/mod/mall/collection')">
							<text class="mr-1">收藏</text>
							<text class="i-ep-star text-2xl text-white block" />
						</view>
						<view class="p-2 flex items-center" @click="navigateTo('/mod/mall/footerhistory')">
							<text class="mr-1">足迹</text>
							<text class="i-ep-clock text-2xl text-white block" />
						</view>
						<view class="p-2" @click="openMessageList()">
							<wd-badge :is-dot="hasUnreadMessage">
								<text class="i-ep-message text-2xl text-white block" />
							</wd-badge>
						</view>
						<DIWAuth auth="chat_imConversation_view">
							<view class="p-2" @click="openIM()">
								<wd-badge :model-value="totalUnread" :max="99">
									<text class="i-ep-chat-dot-round text-2xl text-white block" />
								</wd-badge>
							</view>
						</DIWAuth>
						<view class="p-2" @click="openSettings()">
							<text class="i-ep-setting text-2xl text-white block" />
						</view>
					</view>
				</view>

				<!-- 我的功能区 -->
				<view class="function-section"> </view>

				<!-- 资金账户卡片 - 提取到工作台外部 -->
				<AccountCard
					v-if="hasAccountData && checkAuth('pay_virtual_account_view')"
					:account-amount="workbenchStat?.accountStat?.accountAmount || 0"
					:balance="workbenchStat?.accountStat?.balance || 0"
					:frozen-amount="workbenchStat?.accountStat?.frozenAmount || 0"
					:on-more-click="openAccountInfo"
				/>

				<view class="menu">
					<view class="menu-content">
						<wd-collapse v-model="collapse">
							<wd-collapse-item title="工作台" name="w" custom-class="menu-collapse-item">
								<template #icon>
									<view class="text-xl mr-2" :class="`iconfont icon-gongzuotai text-blue-500`" />
								</template>
								<view class="workbench-wrapper">
									<WorkbenchCust />
									<WorkbenchPlat />
								</view>
							</wd-collapse-item>

							<wd-collapse-item v-for="mg in menuGroups" :key="mg.id" :title="mg.title" :name="mg.id" custom-class="menu-collapse-item">
								<template #icon>
									<view class="text-xl mr-2" :class="mg.icon" />
								</template>
								<view class="menu-grid-wrapper">
									<wd-grid :border="false" :column="4" clickable>
										<wd-grid-item
											v-for="mi in mg.items"
											:key="mi.id"
											use-text-slot
											icon="picture"
											:use-icon-slot="!!mi.icon"
											@itemclick="handleItemClick(mi)"
											:value="mi.badge?.value"
											:max="mi.badge?.max"
										>
											<template #icon>
												<view class="text-2xl" :class="mi.icon" />
											</template>
											<template #text>
												<view class="mt-4 h-40px px-2">
													<text>{{ mi.title }}</text>
												</view>
											</template>
										</wd-grid-item>
									</wd-grid>
								</view>
							</wd-collapse-item>
						</wd-collapse>
					</view>
				</view>
			</template>
		</template>
	</DIWListView>
</template>

<script setup lang="ts">
import WorkbenchCust from './workbenchCust.vue';
import WorkbenchPlat from './workbenchPlat.vue';
import { useStatStore } from '@/store/stat';
import { useAppMenuGroups } from '@/menu';
import AccountCard from './AccountCard.vue';

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		styleIsolation: 'shared',
	},
});

// #endif

const { sessionInfo, totalUnread, metrics, navigateTo, checkAuth, protect, hasUnreadMessage } = useFramework();
const { reloadWorkbenchStat } = useStatStore();
const { workbenchStat } = storeToRefs(useStatStore());

const listView = ref();

const appMenu = useAppMenuGroups();

const visible = ref(false);

const needReload = ref(true);

var lastReloadTime = 0;

var checkIntervalId = 0;

onShow(() => {
	visible.value = true;
	needReload.value = true;

	checkIntervalId = setInterval(() => {
		const dt = Date.now() - lastReloadTime;
		if (dt >= 20000) {
			needReload.value = true;
		}
	}, 3000);
});

onHide(() => {
	visible.value = false;
	clearInterval(checkIntervalId);
});

watch(
	[sessionInfo, listView, visible, needReload],
	() => {
		if (listView.value && visible.value && needReload.value) {
			needReload.value = false;
			setTimeout(() => {
				if (listView.value) {
					listView.value.reload2();
				}
			});
		}
	},
	{ immediate: true }
);

const topMarginStyle = computed(() => (metrics.value.safeArea.top > 0 ? `height: ${metrics.value.safeArea.top}px` : ''));

const tenantName = computed(() => {
	return '未知';
});

const collapse = ref(['w', ...appMenu.map((_e, i) => `MG_${i}`)]);

const menuGroups = computed(() => {
	return buildMenu();
});

function buildMenu() {
	// console.log(sessionInfo.value?.permissions);

	const menuGroups = [];

	if (sessionInfo.value) {
		for (let i = 0; i < appMenu.length; ++i) {
			const mg = appMenu[i];
			const groupId = `MG_${i}`;
			const items = [];
			for (let j = 0; j < mg.items.length; ++j) {
				const mi = mg.items[j];
				const menuId = `MI_${i}_${j}`;
				if (checkAuth(mi.auth, mi.authAll)) {
					if (!mi.avail || mi.avail()) {
						items.push({
							id: menuId,
							title: mi.title,
							path: mi.path,
							icon: mi.icon,
							action: mi.action,
							badge: mi.badgeFunc ? mi.badgeFunc() : undefined,
						});
					}
				}
			}

			if (items.length > 0) {
				menuGroups.push({ id: groupId, title: mg.title, items, icon: mg.icon });
			}
		}
	}

	console.log(menuGroups);

	return menuGroups;
}

// 判断是否有账户数据
const hasAccountData = computed(() => {
	return (
		workbenchStat.value?.accountStat !== null &&
		workbenchStat.value?.accountStat !== undefined &&
		(workbenchStat.value?.accountStat?.accountAmount !== null ||
			workbenchStat.value?.accountStat?.frozenAmount !== null ||
			workbenchStat.value?.accountStat?.balance !== null)
	);
});

const listViewMeta = defineDIWListViewMeta({
	async loadData() {
		reloadWorkbenchStat()
			.then(() => {
				lastReloadTime = Date.now();
			})
			.catch((err) => {
				console.error(err);
			});
		return { hasMore: false, items: [{ id: 'all' }], trackInfo: 0 };
	},
});

function handleItemClick(mi: { action?: () => void; path: string }) {
	if (mi.action) {
		mi.action();
		return;
	}

	navigateTo({ url: `/mod/${mi.path}` });
}

function openSettings() {
	protect(async () => {
		await navigateTo('/mod/main/settings');
	});
}

function openIM() {
	protect(async () => {
		await navigateTo('/mod/chat/conversationList');
	});
}

function openMessageList() {
	protect(async () => {
		await navigateTo('/mod/message/messageList');
	});
}

function openAccountInfo() {
	protect(async () => {
		await navigateTo('/mod/pay/home');
	});
}
</script>

<style lang="scss" scoped>
@use '/src/theme.scss' as *;

.menu {
	background-color: #f8f9fa;
	// min-height: calc(100vh - 200rpx);

	&-content {
		padding: 8rpx 0;
		background-color: #f8f9fa;
	}

	&-header {
		background-color: $theme-color-primary;
		color: white;
		// --wot-button-primary-bg-color: $theme-color-primary;
		--wot-tag-primary-color: #f0f0f0;
	}

	:deep() {
		.menu-collapse-item {
			margin: 12rpx 16rpx;
			background: #ffffff;
			border-radius: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
			overflow: hidden;
			border: 1rpx solid rgba(0, 0, 0, 0.04);

			.wd-collapse-item__header {
				--wot-color-border-light: transparent;
				padding: 28rpx 32rpx;
				font-weight: 600;
				font-size: 32rpx;
				background: #ffffff;
			}
		}

		.wd-collapse-item__body {
			--wot-collapse-body-padding: 0;
			background: #ffffff;
		}

		.wd-grid-item__content {
			padding-bottom: 0;
		}
	}

	.workbench-wrapper {
		background: #ffffff;
		padding: 0 8rpx 16rpx;
	}

	.menu-grid-wrapper {
		padding: 16rpx 20rpx 20rpx;
		background: #ffffff;
	}
}
</style>
