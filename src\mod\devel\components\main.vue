<template>
	<DIWRoot>
		<DIWSection>
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="下拉刷新列表自定义标题" is-link to="/mod/devel/sampleHostPullCustom?mode=demoHome1" />
				<wd-cell title="下拉刷新列表" is-link to="/mod/devel/sampleHostPull?mode=demoHome1" />
				<wd-cell title="滚动刷新列表自定义标题" is-link to="/mod/devel/sampleHostCustom?mode=demoHome1" />
				<wd-cell title="滚动刷新列表" is-link to="/mod/devel/sampleHost?mode=demoHome1" />
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection>
			<wd-cell-group title="表单校验" border>
				<wd-cell title="简单表单" is-link to="/mod/devel/sampleHost?mode=form1" />
				<wd-cell title="简单表单【DIWScrollView】" title-width="calc(100% - 40px)" is-link to="/mod/devel/sampleHost?mode=form1&sv=1" />
				<DIWAuth auth="order_deliverOrder_view">
					<!-- <DIWNavigator text="提货申请单-hmj" url="/mod/deliver/deliverList" /> -->
					<DIWNavigator text="提货申请单" url="/mod/deliver/deliverTabs" />
					<DIWNavigator text="锁批申请单" url="/mod/deliver/lockBatchList" />
					<DIWNavigator text="收货地址变更审核" url="/mod/deliver/addressAuditList" />
					<DIWNavigator text="自提信息变更审核" url="/mod/deliver/pickupInfoAuditList" />
					<DIWNavigator text="待确认收货" url="/mod/deliver/confirm" />
				</DIWAuth>
			</wd-cell-group>
		</DIWSection>

		<DIWSection>
			<wd-cell-group title="合同管理" border>
				<DIWAuth auth="order_order_sign">
					<DIWNavigator text="我的合同" url="/mod/order/buyerOrderList" />
				</DIWAuth>
				<DIWAuth auth="order_order_audit">
					<DIWNavigator text="销售合同" url="/mod/order/sellerOrderList" />
				</DIWAuth>
				<DIWAuth auth="order_order_review">
					<DIWNavigator text="合同复核" url="/mod/order/orderReviewList" />
				</DIWAuth>
			</wd-cell-group>
		</DIWSection>
		<DIWSection>
			<wd-cell-group title="销售出库" border>
				<DIWAuth auth="order_deliverOrder_view">
					<DIWNavigator text="出库单制单" url="/mod/sale/warehouseOutNotice" />
					<DIWNavigator text="拼车列表" url="/mod/sale/sharing" />
					<DIWNavigator text="出库单审核" url="/mod/sale/outbill" />
				</DIWAuth>
			</wd-cell-group>
		</DIWSection>

		<DIWSection>
			<wd-cell-group title="地址管理" border>
				<DIWAuth auth="admin_address_info_view">
					<DIWNavigator text="收货地址" url="/mod/buyer/addressList" />
				</DIWAuth>
				<DIWAuth auth="admin_pickup_info_view">
					<DIWNavigator text="自提信息" url="/mod/buyer/pickupInfoList" />
				</DIWAuth>
			</wd-cell-group>
		</DIWSection>

		<DIWAuth auth="order_deliverOrder_add">
			<DIWSection>
				<wd-cell-group title="申请发货测试区" border>
					<DIWNavigator text="test1" url="/mod/deliver/batchDeliveryApply?ids=957972229302255616%2C957932397658112000%2C957271025236574208" />
					<DIWNavigator text="test2" url="/mod/deliver/batchDeliveryApply?ids=957972229302255616" />
					<DIWNavigator text="test3" url="/mod/deliver/batchDeliveryApply?ids=957932397658112000" />
					<DIWNavigator text="test4" url="/mod/deliver/batchDeliveryApply?ids=957271025236574208" />
					<wd-button @click="deliverTest1()">滞纳金测试</wd-button>
					<wd-button @click="orderApplyTest1()">商城下单测试1</wd-button>
					<wd-button @click="orderSignTest1()">契约锁签合同测试1</wd-button>
				</wd-cell-group>
			</DIWSection>
		</DIWAuth>

		<DIWAuth>
			<DIWSection>
				<DIWNavigator text="IM" url="/mod/chat/conversationList" />
			</DIWSection>
		</DIWAuth>

		<template v-if="sessionInfo">
			<wd-gap />

			<template v-if="sessionInfo.avatarUrl">
				<view class="flex flex-row items-center gap-10 justify-center">
					<wd-img :width="100" :height="100" round :src="sessionInfo.avatarUrl" enable-preview custom-class="shadow-lg" />
					<wd-img :width="100" :height="100" :src="sessionInfo.avatarUrl" enable-preview custom-class="shadow-lg" />
				</view>
			</template>
		</template>

		<DIWSection>
			<wd-cell-group>
				<wd-collapse v-model="collapse">
					<template v-if="sessionInfo">
						<wd-collapse-item name="session" title="Session 信息">
							<text class="whitespace-pre-wrap break-all">{{ JSON.stringify(sessionInfo, null, 4) }}</text>
						</wd-collapse-item>
					</template>

					<wd-collapse-item name="device" title="设备信息">
						<text class="whitespace-pre-wrap break-all">{{ JSON.stringify(deviceInfo, null, 4) }}</text>
					</wd-collapse-item>

					<wd-collapse-item name="app" title="App 信息">
						<text class="whitespace-pre-wrap break-all">{{ JSON.stringify(appInfo, null, 4) }}</text>
					</wd-collapse-item>

					<wd-collapse-item name="window" title="窗口信息">
						<text class="whitespace-pre-wrap break-all">{{ JSON.stringify(windowInfo, null, 4) }}</text>
					</wd-collapse-item>
				</wd-collapse>
			</wd-cell-group>
		</DIWSection>

		<template v-if="sessionInfo">
			<wd-gap />

			<wd-button block type="error" @click="doLogout">注销</wd-button>
		</template>

		<wd-gap />
	</DIWRoot>
</template>

<script setup lang="ts">
const { sessionInfo, protect, protectOp, logout, invokeTo, apiPost, apiGet, invokeQYS } = useFramework();

const collapse = ref<string[]>([]);

const deviceInfo = uni.getDeviceInfo();

const appInfo = uni.getAppBaseInfo();

const windowInfo = ref(uni.getWindowInfo());

onResize(() => {
	console.log('onResize');
	windowInfo.value = uni.getWindowInfo();
});

function doLogout() {
	protect(async () => {
		await logout();
	});
}

function orderApplyTest1() {
	const d = {
		type: 0,
		list: [
			{ sellerId: '1902973077066207234', productId: '1907248738136027137', stockCity: '0', price: 23000, quantity: 0.025 },
			{ sellerId: '1902973077066207234', productId: '1902985091926732801', stockCity: '0', price: 23000, quantity: 0.025 },
		],
		cartIds: ['1913530403837816833', '1913530468727894018'],
	};

	protectOp(async () => {
		// 来源类型; 0:PC端 1:APP 2:微信商城 3:小程序
		let sourceType = 1;

		// #ifdef MP-WEIXIN
		sourceType = 3;
		// #endif

		const d1 = await apiPost({
			url: 'order/order/apply',
			data: {
				sourceType,
				applyDetails: d.list,
				orderSource: d.type,
			},
		});

		console.log(d1);

		const purchaseDetails = d1.applyDetails.map((e: Record<string, any>) => e.purchases).flat();
		console.log(purchaseDetails);

		async function submit(password?: string, smsCode?: string) {
			await apiPost({
				url: 'order/order/confirm',
				data: {
					applyId: d1.applyId,
					sourceType,
					details: d1.applyDetails.map((e: Record<string, any>) => ({ buyerMessage: 'message!', id: e.id })),
					cartIds: d.cartIds,
					payPassword: password,
					smsCode,
				},
			});
		}

		protect(async () => {
			await invokeTo({
				url: '/mod/payment/confirm',
				data: {
					submit,
					title: '提交合同',
					detail: {
						// 提交合同相关的细节信息
						mode: 'placeorder',
						details: purchaseDetails,
						deposit: d1.deposit,
					},
				},
			});
		});
	}, null);
}

function deliverTest1() {
	const d1 = [
		{
			id: '1913077279759085569',
			orderId: '957972229302255616',
			orderNo: 'SZSJ2025032800304',
			buyerId: '1902972415502831618',
			sellerId: '1902978646346944513',
			serverId: '1903012530962931713',
			groupId: null,
			deliverMethod: 4,
			deliverMethodStr: '快递',
			deliverBatchNo: null,
			deliverStatus: null,
			auditStates: 0,
			zdStatus: null,
			ckStatus: null,
			shStatus: null,
			cancelCkStatus: null,
			invoiceState: null,
			payState: 0,
			orderSource: 0,
			specifyBatch: null,
			deliverAmount: 125,
			storageFees: 6.62,
			finalStorageFees: null,
			finalDeliverAmount: null,
			totalNum: 0.025,
			totalLockNum: 0,
			auditUserId: null,
			auditTime: null,
			receiveTime: null,
			receiveUser: null,
			remark: null,
			invoiceRemark: null,
			documentNumber: null,
			isSign: null,
			overdueStartTime: '2025-03-29 00:00:00',
			overdueEndTime: '2025-04-18 00:00:00',
			outboundTime: null,
			defaultReceiptTime: 3,
			qysContractId: null,
			qysDocumentId: null,
			contractFileId: null,
			applyUserId: '1902972415406362625',
			applyTime: '2025-04-18 11:49:01',
			confirmReceiptTime: null,
			createBy: null,
			createTime: null,
			updateBy: null,
			updateTime: null,
			delFlag: null,
			tenantId: null,
			productNames: '精梳环锭纺32S针织纱',
			batchNos: '5004433202',
			warehouseId: '1902983249419407362',
			warehouseName: null,
			warehouseAddress: null,
			passedZdNum: null,
			canceledZdNum: null,
			effectiveQhNum: null,
			effectiveWqhNum: null,
			differenceNum: null,
			receivedNum: null,
			signDeadTime: null,
			kpDeadTime: null,
			relocationQuantity: null,
			buyerName: null,
			sellerName: null,
			productEntityList: [
				{
					id: '1913077279759085570',
					deliverId: '1913077279759085569',
					orderId: '957972229302255616',
					orderNo: 'SZSJ2025032800304',
					orderItemId: '1905469818390773761',
					productId: '1902985281916121089',
					num: 0.025,
					passedZdNum: null,
					canceledZdNum: null,
					effectiveQhNum: null,
					effectiveWqhNum: null,
					differenceNum: null,
					receivedNum: null,
					lockNum: 0,
					relocationQuantity: null,
					warehouseId: '1902983249419407362',
					batchNo: '5004433202',
					overdueStartTime: '2025-03-29 00:00:00',
					overdueEndTime: '2025-04-18 00:00:00',
					cancelStatus: null,
					createBy: null,
					createTime: null,
					updateBy: null,
					updateTime: null,
					delFlag: null,
					warehouseName: '中实兰溪仓（前）',
					warehouseProvince: '330000',
					productClassName: '棉纱',
					productName: '精梳环锭纺32S针织纱',
					productNo: 'PP2025032100002',
					productPrice: 5000,
					finalDeliverAmount: null,
				},
			],
			orderExtracting: null,
			orderShipping: {
				id: null,
				orderNo: 'SZSJ2025032800304',
				receiverName: '老韩',
				receiverPhone: '07314789789',
				receiverMobile: '13343434343',
				province: '410000',
				city: '410100',
				area: null,
				address: '2522',
				totalAddress: null,
				receiverZip: '466200',
				deliverId: '1913077279759085569',
				createBy: null,
				createTime: null,
				updateBy: null,
				updateTime: null,
				delFlag: null,
			},
			orderEnterprise: null,
			storageFeeRuleList: [
				{ id: '1905469818457882625', startDays: 1, endDays: 3, price: 5, afterPrice: null },
				{ id: '1905469818466271234', startDays: 4, endDays: 7, price: 10, afterPrice: null },
				{ id: '1905469818466271235', startDays: 8, endDays: null, price: 15, afterPrice: null },
			],
			storageFeesEntityList: [
				{
					id: null,
					feesId: '1905469818457882625',
					orderNo: 'SZSJ2025032800304',
					deliverId: '1913077279759085569',
					deliverBatchNo: null,
					sellerId: '1902978646346944513',
					sellerName: null,
					startDays: 1,
					endDays: 3,
					price: 5,
					overdueCost: 0.375,
					createBy: null,
					createTime: null,
					updateBy: null,
					updateTime: null,
					delFlag: null,
				},
				{
					id: null,
					feesId: '1905469818466271234',
					orderNo: 'SZSJ2025032800304',
					deliverId: '1913077279759085569',
					deliverBatchNo: null,
					sellerId: '1902978646346944513',
					sellerName: null,
					startDays: 4,
					endDays: 7,
					price: 10,
					overdueCost: 1,
					createBy: null,
					createTime: null,
					updateBy: null,
					updateTime: null,
					delFlag: null,
				},
				{
					id: null,
					feesId: '1905469818466271235',
					orderNo: 'SZSJ2025032800304',
					deliverId: '1913077279759085569',
					deliverBatchNo: null,
					sellerId: '1902978646346944513',
					sellerName: null,
					startDays: 8,
					endDays: null,
					price: 15,
					overdueCost: 5.25,
					createBy: null,
					createTime: null,
					updateBy: null,
					updateTime: null,
					delFlag: null,
				},
			],
			totalAheadLockNum: null,
		},
	];

	protect(async () => {
		async function submit(password?: string, code?: string) {
			console.log('go', password, code);
		}

		if (d1.length > 0) {
			await invokeTo({
				url: '/mod/payment/confirm',
				data: {
					submit,
					title: '滞纳金支付确认',
					detail: {
						// 申请发货相关的滞纳金细节信息
						mode: 'deliver',
						info: d1,
					},
				},
			});
		} else {
			await submit('');
		}
	});
}

function orderSignTest1() {
	const orderId = '966339985919705088';

	protect(async () => {
		await invokeQYS(
			'签电子合同',
			async (backPageUrl) => {
				return await apiGet({
					url: 'order/order/signPageUrl',
					params: { orderId: orderId, requestType: 'H5', backPageUrl },
				});
			},
			orderId,
			'0'
		);

		console.log('OK');
	});
}
</script>
