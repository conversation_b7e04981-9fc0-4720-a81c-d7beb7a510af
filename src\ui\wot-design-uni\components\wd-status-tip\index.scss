@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	background-color: $wot-dark-background2;
	@include b(status-tip) {
		@include e(text) {
			color: $wot-dark-color3;
		}
	}
}

@include b(status-tip) {
	padding: $wot-statustip-padding;
	width: 100%;
	margin: 0 auto;
	color: $wot-statustip-color;
	font-size: $wot-statustip-fs;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;

	@include edeep(image) {
		margin: 0 auto;
		width: 160px;
		height: 160px;
	}
	@include e(text) {
		margin: 20px auto 0;
		font-size: $wot-statustip-fs;
		line-height: $wot-statustip-line-height;
		color: $wot-statustip-color;
		text-align: center;
		overflow-wrap: break-word;
	}
}
