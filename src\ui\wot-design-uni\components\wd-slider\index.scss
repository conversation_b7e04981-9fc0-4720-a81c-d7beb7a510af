@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(slider) {
		@include e(label-min, label-max) {
			color: $wot-dark-color;
		}

		@include e(label) {
			color: $wot-dark-color;
			background-color: rgba($color: $wot-dark-background2, $alpha: 0.5);
		}

		@include m(disabled) {
			@include me(label-min, label-max) {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(slider) {
	display: flex;
	flex-flow: row nowrap;
	align-items: center;
	height: calc($wot-slider-handle-radius * 3);

	@include e(label-min, label-max) {
		font-size: $wot-slider-fs;
		color: $wot-slider-color;
	}
	@include e(label) {
		text-align: center;
		width: calc($wot-slider-handle-radius * 2);
		line-height: calc($wot-slider-handle-radius * 2);
		font-size: $wot-slider-fs;
		line-height: 1.2;
		color: $wot-slider-color;
		background-color: rgba($color: #fff, $alpha: 0.5);
		border-radius: 100%;
		position: absolute;
		bottom: calc($wot-slider-handle-radius * 2 + 8px);
	}
	@include e(bar-wrapper) {
		flex: 1;
		position: relative;
		border-radius: calc($wot-slider-axie-height / 2);
		background-color: #e5e5e5;
		margin: calc($wot-slider-handle-radius - $wot-slider-axie-height / 2) 0;
	}
	@include e(bar) {
		position: relative;
		border-radius: inherit;
		height: $wot-slider-axie-height;
		background: $wot-slider-line-color;
	}
	@include e(button-wrapper) {
		width: calc($wot-slider-handle-radius * 2);
		/* 右侧滑块按钮定位，向右偏移自身宽度的50% */
		transform: translate3d(50%, -50%, 0);
		position: absolute;
		right: 0;

		@include m(left) {
			left: 0;
			/* 左侧滑块按钮定位，向左偏移自身宽度的50% */
			transform: translate3d(-50%, -50%, 0);
		}
	}
	@include e(has-label) {
		padding-top: calc($wot-slider-fs * 1.2 + 8px);
	}
	@include e(button) {
		height: calc($wot-slider-handle-radius * 2);
		width: calc($wot-slider-handle-radius * 2);
		background: $wot-slider-handle-bg;
		border-radius: 100%;
		border: 1px solid $wot-slider-axie-bg;
		box-sizing: border-box;
		box-shadow: 0 2px 4px 0 rgba($color: #9b9b9b, $alpha: 0.5);
	}
	@include e(label-min) {
		margin-right: calc($wot-slider-handle-radius * 2);
	}
	@include e(label-max) {
		margin-left: calc($wot-slider-handle-radius * 2);
	}
	@include m(disabled) {
		@include me(bar) {
			opacity: 0.25;
		}
		@include me(label-min, label-max) {
			color: $wot-slider-disabled-color;
		}
	}
}
