@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(input-number) {
		@include e(action) {
			color: $wot-dark-color;
			@include when(disabled) {
				color: $wot-dark-color-gray;
			}
		}

		@include e(input) {
			color: $wot-dark-color;
		}

		@include when(disabled) {
			.wd-input-number__input {
				color: $wot-dark-color-gray;
			}
			.wd-input-number__sub,
			.wd-input-number__add {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(input-number) {
	display: inline-block;
	user-select: none;
	line-height: 1.15;

	@include e(action) {
		position: relative;
		display: inline-block;
		width: $wot-input-number-btn-width;
		height: $wot-input-number-height;
		vertical-align: middle;
		color: $wot-input-number-icon-color;
		-webkit-tap-highlight-color: transparent;
		box-sizing: border-box;

		// 左右加减号的边框
		&::after {
			position: absolute;
			content: '';
			width: calc(200% - 2px);
			height: calc(200% - 2px);
			left: 0;
			top: 0;
			border: 1px solid $wot-input-number-border-color;
			border-top-left-radius: calc($wot-input-number-radius * 2);
			border-bottom-left-radius: calc($wot-input-number-radius * 2);
			transform: scale(0.5);
			transform-origin: left top;
		}
		&:last-child::after {
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
			border-top-right-radius: calc($wot-input-number-radius * 2);
			border-bottom-right-radius: calc($wot-input-number-radius * 2);
		}
		@include when(disabled) {
			color: $wot-input-number-disabled-color;
		}
	}

	@include e(inner) {
		position: relative;
		display: inline-block;
		vertical-align: middle;
	}

	@include e(input) {
		position: relative;
		display: block;
		width: $wot-input-number-input-width;
		height: $wot-input-number-height;
		padding: 0 2px;
		box-sizing: border-box;
		z-index: 1;
		background: transparent;
		border: none;
		outline: none;
		text-align: center;
		color: $wot-input-number-color;
		font-size: $wot-input-number-fs;
		-webkit-appearance: none;
		-webkit-tap-highlight-color: transparent;
	}

	@include e(input-border) {
		position: absolute;
		width: 100%;
		height: calc(200% - 2px);
		left: 0;
		top: 0;
		border-top: 1px solid $wot-input-number-border-color;
		border-bottom: 1px solid $wot-input-number-border-color;
		transform: scaleY(0.5);
		transform-origin: left top;
		z-index: 0;
	}

	@include edeep(action-icon) {
		position: absolute;
		display: inline-block;
		font-size: $wot-input-number-icon-size;
		width: $wot-input-number-icon-size;
		height: $wot-input-number-icon-size;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	@include when(disabled) {
		.wd-input-number__input {
			color: $wot-input-number-disabled-color;
			z-index: inherit;
		}
		.wd-input-number__sub,
		.wd-input-number__add {
			color: $wot-input-number-disabled-color;
		}
	}
	@include when(without-input) {
		.wd-input-number__action:last-child::after {
			border-left: none;
		}
	}
}
