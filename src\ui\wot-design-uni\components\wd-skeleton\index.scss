@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(skeleton) {
		@include e(col) {
			background-color: $wot-dark-background4;
		}
	}
}
@include b(skeleton) {
	box-sizing: border-box;

	@include e(row) {
		justify-content: space-between;
		margin-bottom: $wot-skeleton-row-margin-bottom;
		display: flex;
		align-items: center;

		&:only-child,
		&:last-child {
			margin-bottom: 0;
		}
	}
	@include e(col) {
		border-radius: $wot-skeleton-border-radius-text;
		background-color: $wot-skeleton-background-color;
		display: flex;
		align-items: center;
		justify-content: center;

		&:first-child:last-child,
		&:last-child {
			margin-right: 0;
		}
	}
	@include m(type) {
		&-text {
			width: 100%;
			height: $wot-skeleton-text-height-default;
			border-radius: $wot-skeleton-border-radius-text;
		}

		&-rect {
			width: 100%;
			height: $wot-skeleton-rect-height-default;
			border-radius: $wot-skeleton-border-radius-rect;
		}

		&-circle {
			flex-shrink: 0;
			width: $wot-skeleton-circle-height-default;
			height: $wot-skeleton-circle-height-default;
			border-radius: $wot-skeleton-border-radius-circle;
		}
	}
	@include m(animation) {
		&-gradient {
			position: relative;
			overflow-x: hidden;

			&::after {
				content: ' ';
				position: absolute;
				animation: wd-skeleton-gradient 1.5s linear 2s infinite;
				background: linear-gradient(90deg, rgba(255, 255, 255, 0), $wot-skeleton-animation-gradient, rgba(255, 255, 255, 0));
				inset: 0;
			}
		}

		&-flashed {
			animation: wd-skeleton-flashed 2s linear 2s infinite;
		}
	}

	@keyframes wd-skeleton-gradient {
		0% {
			transform: translateX(-100%) skewX(-15deg);
		}
		100% {
			transform: translateX(100%) skewX(-15deg);
		}
	}
	@keyframes wd-skeleton-flashed {
		0% {
			opacity: 1;
		}
		50% {
			opacity: 0.3;
			background-color: $wot-skeleton-animation-flashed;
		}
		100% {
			opacity: 1;
		}
	}
}
