@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(badge) {
		@include e(content) {
			border-color: $wot-dark-background2;
		}
	}
}

@include b(badge) {
	position: relative;
	vertical-align: middle;
	display: inline-block;

	@include e(content) {
		display: inline-block;
		box-sizing: content-box;
		height: $wot-badge-height;
		line-height: $wot-badge-height;
		padding: $wot-badge-padding;
		background-color: $wot-badge-bg;
		border-radius: calc($wot-badge-height / 2 + 2px);
		color: $wot-badge-color;
		font-size: $wot-badge-fs;
		text-align: center;
		white-space: nowrap;
		border: $wot-badge-border;
		font-weight: 500;

		@include when(fixed) {
			position: absolute;
			top: 0px;
			right: 0px;
			transform: translateY(-50%) translateX(50%);
		}

		@include when(dot) {
			height: $wot-badge-dot-size;
			width: $wot-badge-dot-size;
			padding: 0;
			border-radius: 50%;
		}

		@each $type in (primary, success, warning, info, danger) {
			@include m($type) {
				@if $type == primary {
					background-color: $wot-badge-primary;
				} @else if $type == success {
					background-color: $wot-badge-success;
				} @else if $type == warning {
					background-color: $wot-badge-warning;
				} @else if $type == info {
					background-color: $wot-badge-info;
				} @else {
					background-color: $wot-badge-danger;
				}
			}
		}
	}
}
