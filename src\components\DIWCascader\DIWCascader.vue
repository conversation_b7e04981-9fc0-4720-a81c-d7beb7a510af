<template>
	<picker mode="multiSelector" :range="ranges" :range-key="props.labelProp" :value="index" @columnchange="handleColumnChange" @change="handleChange">
		<slot :displayText="displayText" />
	</picker>
</template>

<script setup lang="ts">
const props = withDefaults(
	defineProps<{
		options?: Array<Record<string, any>>;
		labelProp?: string;
		valueProp?: string;
		childrenProp?: string;
	}>(),
	{ labelProp: 'label', valueProp: 'value', childrenProp: 'children' }
);

const emit = defineEmits<{
	(e: 'change', value: string | number | undefined): void;
}>();

const model = defineModel<string | number>();

const ranges = ref<Array<Array<Record<string, any>>>>([]);
const index = ref<Array<number>>([]);

const displayText = ref('');

watch(
	[() => props.options, () => model.value],
	([options, mv]) => {
		if (Array.isArray(options)) {
			prepareOptions(options, mv);
		}
	},
	{ immediate: true }
);

function prepareOptions(options: Array<Record<string, any>>, modelValue: string | number | undefined) {
	const startDepth = 1;
	let maxDepth = startDepth;

	const index1: number[] = [];

	const indexList: number[] = [];
	let foundIndex = false;

	function visit(level: number, item: Record<string, any>, index: number) {
		indexList.push(index);
		maxDepth = Math.max(maxDepth, level);
		if (modelValue !== undefined && item[props.valueProp] === modelValue) {
			foundIndex = true;
			index1.splice(0, index1.length, ...indexList);
		}
		const children = item[props.childrenProp];
		if (Array.isArray(children)) {
			for (let i = 0; i < children.length; ++i) {
				visit(level + 1, children[i], i);
			}
		}

		indexList.pop();
	}

	for (let i = 0; i < options.length; ++i) {
		visit(startDepth, options[i], i);
	}

	const ranges1: Array<Array<Record<string, any>>> = [];

	if (!foundIndex) {
		for (let i = 0; i < maxDepth; ++i) {
			index1.push(0);
		}
	}

	for (let i = 0; i < maxDepth; ++i) {
		if (i === 0) {
			ranges1.push(options);
			continue;
		}

		const f = ranges1[i - 1][index1[i - 1]];
		if (f) {
			const children = f[props.childrenProp];
			if (Array.isArray(children)) {
				ranges1.push(children);
				continue;
			}
		}

		ranges1.push([]);
	}

	ranges.value = ranges1;
	index.value = index1;

	if (foundIndex) {
		const ls: string[] = [];
		for (let i = 0; i < index1.length; ++i) {
			ls.push(ranges.value[i][index1[i]][props.labelProp]);
		}

		displayText.value = ls.join('');
	}
}

function handleChange(ev: { detail: { value: number[] } }) {
	const v = ev.detail.value;
	const lastIndex = v[v.length - 1];
	const k = ranges.value[v.length - 1][lastIndex];
	const mv = k[props.valueProp];
	model.value = mv;
	const ls: string[] = [];
	for (let i = 0; i < v.length; ++i) {
		ls.push(ranges.value[i][v[i]][props.labelProp]);
	}

	displayText.value = ls.join('');
	emit('change', mv);
}

function handleColumnChange(ev: { detail: { column: number; value: number } }) {
	const ranges1: Array<Array<Record<string, any>>> = [];
	const index1: number[] = [];
	for (let i = 0; i < ranges.value.length; ++i) {
		if (i <= ev.detail.column) {
			ranges1.push(ranges.value[i]);
		} else if (i === ev.detail.column + 1) {
			const f = ranges.value[ev.detail.column][ev.detail.value];
			if (f) {
				const children = f[props.childrenProp];
				if (Array.isArray(children)) {
					ranges1.push(children);
				} else {
					ranges1.push([]);
				}
			} else {
				ranges1.push([]);
			}
		} else {
			ranges1.push([]);
		}

		if (i < ev.detail.column) {
			index1.push(index.value[i]);
		} else if (i === ev.detail.column) {
			index1.push(ev.detail.value);
		} else {
			index1.push(0);
		}
	}

	ranges.value = ranges1;
	index.value = index1;
}
</script>
