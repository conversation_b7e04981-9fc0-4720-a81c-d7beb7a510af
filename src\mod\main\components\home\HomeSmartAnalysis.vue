<template>
	<view class="smart-analysis-section">
		<view class="smart-analysis-card">
			<!-- 头部标题区域 -->
			<view class="smart-analysis-header">
				<view class="title-wrapper">
					<image src="/static/icons/<EMAIL>" mode="aspectFit" class="title-icon" />
					<text class="title">{{ t('smartAnalysisNotification') }}</text>
				</view>
			</view>

			<!-- 智能分析内容 -->
			<view class="smart-analysis-content">
				<!-- 今日低效分析 -->
				<view class="analysis-section">
					<view class="section-header">
						<image src="/static/icons/<EMAIL>" mode="aspectFit" class="section-icon" />
						<text class="section-title">{{ t('todayLowEfficiencyAnalysis') }}</text>
					</view>

					<view class="analysis-summary">
						{{ t('todayAnalysisSummary') }}
					</view>
				</view>

				<!-- 交互按钮区域 -->
				<view class="action-buttons">
					<view class="action-btn" @click="copyContent">
						<image src="/static/icons/<EMAIL>" mode="aspectFit" class="action-icon" />
					</view>
					<view class="action-btn" @click="refreshContent">
						<image src="/static/icons/<EMAIL>" mode="aspectFit" class="action-icon" />
					</view>
					<view class="action-btn" @click="likeContent">
						<image src="/static/icons/<EMAIL>" mode="aspectFit" class="action-icon" />
					</view>
					<view class="action-btn" @click="dislikeContent">
						<image src="/static/icons/<EMAIL>" mode="aspectFit" class="action-icon" />
					</view>
				</view>

				<!-- 分析请求按钮 -->
				<view class="analysis-request">
					<view class="request-btn" @click="requestAnalysis">
						{{ t('pleaseAnalyzeBreakage') }}
					</view>
				</view>

				<!-- 底部区域 -->
				<view class="bottom-section">
					<view class="diw-section">
						<view class="diw-logo">
							<image src="/static/icons/<EMAIL>" mode="aspectFit" class="logo-img" />
							<view class="logo-dots">
								<view class="dot active"></view>
								<view class="dot"></view>
								<view class="dot"></view>
							</view>
						</view>

						<view class="chat-btn-warp">
							<view class="new-chat-btn" @click="startNewChat">
								<image src="/static/icons/<EMAIL>" mode="aspectFit" class="add-icon" />
								<text class="new-chat-text">{{ t('newConversation') }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-box" @click="onSearchInput">
					<image src="/static/icons/<EMAIL>" mode="aspectFit" class="search-icon" />
					<input type="text" :placeholder="t('searchPlaceholder')" class="search-input" v-model="searchText" @input="onSearchInput" />
				</view>
				<!-- 深度思考按钮 -->
				<view class="deep-thinking" :class="{ 'deep-thinking-active': isDeepThinking }">
					<view class="thinking-btn" @click="deepThinking">
						<image :src="isDeepThinking ? '/static/icons/<EMAIL>' : '/static/icons/<EMAIL>'" mode="aspectFit" class="thinking-icon" />
						<text class="thinking-text">{{ t('deepThinking') }}</text>
					</view>
				</view>
				<!-- 提交按钮 -->
				<view class="submit-btn">
					<image src="/static/icons/<EMAIL>" mode="aspectFit" class="submit-icon" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const isDeepThinking = ref(false);
// 使用国际化
const { t } = useI18n();
const searchText = ref('');

// 交互功能函数
const copyContent = () => {
	// 复制内容到剪贴板
	console.log('Copy content');
};

const refreshContent = () => {
	// 刷新内容
	console.log('Refresh content');
};

const likeContent = () => {
	// 点赞
	console.log('Like');
};

const dislikeContent = () => {
	// 踩
	console.log('Dislike');
};

const requestAnalysis = () => {
	// 请求分析断头情况
	console.log('Request breakage analysis');
};

const startNewChat = () => {
	// 开始新对话
	console.log('Start new conversation');
};

const onSearchInput = () => {
	// 搜索输入处理
	console.log('Search input:', searchText.value);
};

const deepThinking = () => {
	// 深度思考
	isDeepThinking.value = !isDeepThinking.value;
	console.log('Deep thinking');
};
</script>

<style lang="scss" scoped>
.smart-analysis-section {
	margin-top: 26rpx;
	border-radius: 24rpx;
	width: 702rpx;
	min-height: 1184rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 702rpx 1184rpx;
	background-repeat: no-repeat;
	background-position: top center;
	background: linear-gradient(180deg, #c2e2fd 0%, #ffffff 15%, #ffffff 100%);
}

.smart-analysis-card {
	width: 702rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	margin: 0rpx 24rpx;
}

/* 头部标题样式 */
.smart-analysis-header {
	margin: 0 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0 0 0;

	.title-wrapper {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.title-icon {
			width: 64rpx;
			height: 64rpx;
		}

		.title {
			font-size: 32rpx;
			font-weight: normal;
			line-height: 32rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #1d2129;
		}
	}
}

/* 智能分析内容 */
.smart-analysis-content {
	margin: 20rpx 24rpx 0rpx 24rpx;
	background-color: #ffffff;
	border-radius: 30rpx 30rpx 30rpx 8rpx;
	flex: 1;
}

/* 分析区域 */
.analysis-section {
	border-radius: 30rpx 30rpx 30rpx 8rpx;
	background: #f3f4f6;
	padding: 20rpx;

	.section-header {
		display: flex;
		align-items: center;
		gap: 17rpx;
		margin-bottom: 24rpx;

		.section-icon {
			width: 48rpx;
			height: 48rpx;
		}

		.section-title {
			font-size: 28rpx;
			font-weight: normal;
			line-height: 32rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #1d2129;
		}
	}

	.analysis-summary {
		margin: 32rpx 27rpx 0 28rpx;
		color: #1d2129;
		font-weight: 400;
		font-size: 24rpx;
		line-height: 36rpx;
	}
}

/* 交互按钮区域 */
.action-buttons {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 40rpx;
	margin: 28rpx 0 60rpx 16rpx;

	.action-btn {
		width: 30rpx;
		height: 30rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.action-icon {
			width: 30rpx;
			height: 30rpx;
		}
	}
}

/* 分析请求按钮 */
.analysis-request {
	display: flex;
	justify-content: flex-end;
	.request-btn {
		max-width: 400rpx;
		padding: 16rpx 20rpx;
		border-radius: 8px 8px 2px 8px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		background: rgba(0, 130, 240, 0.15);
		font-size: 28rpx;
		font-weight: normal;
		line-height: 32rpx;
		text-align: center;
		letter-spacing: 0rpx;
		color: #1d2129;
	}
}

/* 底部区域 */
.bottom-section {
	margin-top: 40rpx;

	.diw-section {
		display: flex;
		flex-direction: column;
		margin-bottom: 32rpx;

		.diw-logo {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.logo-img {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				background: #4a90e2;
			}

			.logo-dots {
				display: flex;
				gap: 8rpx;

				.dot {
					width: 16rpx;
					height: 16rpx;
					border-radius: 50%;
					background: #c9cdd4;

					&.active {
						background: #4a90e2;
					}
				}
			}
		}

		.chat-btn-warp {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 32rpx;
		}

		.new-chat-btn {
			width: 158rpx;
			height: 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #ffffff;
			border: 0.5px solid #d8d8d8;
			border-radius: 28rpx;
			font-size: 24rpx;
			font-weight: normal;
			line-height: 36rpx;
			letter-spacing: 0rpx;
			color: #1d2129;
			gap: 12rpx;

			.add-icon {
				width: 24rpx;
				height: 24rpx;
				color: #1d2129;
			}
			&:active {
				background: #4a90e2;
				transition: all 0.3s ease;
			}
		}
	}
}
.search-section {
	width: 702rpx;
	min-height: 180rpx;
	border-radius: 40rpx 40rpx 0rpx 0rpx;
	position: relative;

	/* 自动布局 */
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-start;
	background: #ffffff;
	box-shadow: 0rpx -6rpx 10rpx 0rpx rgba(59, 134, 197, 0.1);

	.search-box {
		padding: 29.2rpx 40rpx 0 40rpx;
		width: 654rpx;
		display: flex;
		align-items: center;
		background: #ffffff;
		border-radius: 24rpx;
		padding: 20rpx 24rpx;
		gap: 12rpx;

		.search-icon {
			width: 32rpx;
			height: 33rpx;
		}

		.search-input {
			flex: 1;
			font-size: 28rpx;
			color: #1d2129;
			border: none;
			outline: none;
			background: transparent;
			font-weight: normal;
			line-height: 56rpx;
			letter-spacing: 0rpx;

			&::placeholder {
				color: #d8d8d8;
			}
		}
	}
}
.submit-btn {
	width: 64rpx;
	height: 64rpx;
	background: rgba(0, 130, 240, 0.4);
	position: absolute;
	right: 24rpx;
	bottom: 24rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	.submit-icon {
		width: 64rpx;
		height: 64rpx;
	}
}

.deep-thinking {
	position: absolute;
	bottom: 25.1rpx;
	left: 24rpx;
	border: 1rpx solid #d8d8d8;
	border-radius: 24rpx;

	.thinking-btn {
		width: 132rpx;
		height: 46rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 3.7rpx;

		border-radius: 24rpx;

		.thinking-icon {
			width: 30rpx;
			height: 30rpx;
		}

		.thinking-text {
			font-size: 20rpx;
			font-weight: normal;
			line-height: 36rpx;
			letter-spacing: 0rpx;
			color: #1d2129;
			opacity: 0.7;
		}
	}
	&.deep-thinking-active {
		border: 1rpx solid #0082f0;
		.thinking-btn {
			background: rgba(0, 130, 240, 0.2);
		}
		.thinking-text {
			color: #0082f0;
		}
	}
}
</style>
