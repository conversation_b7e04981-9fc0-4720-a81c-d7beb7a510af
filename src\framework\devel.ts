import { useSystemStore } from '@/store/system';

interface UseFakePageListParams {
	rowKey: string;
	total: number;
	pageSize: number;
	loadData: (pageIndex?: number) => DIWLoadDataResult<number> | Promise<DIWLoadDataResult<number>>;
}

export function useFakePageList(params: UseFakePageListParams) {
	let fullItems: Array<Record<string, any>> = [];
	let trackInfo: any = undefined;
	let sourceCompleted = false;
	let firstPage = true;

	const { hack } = useSystemStore();

	async function loadData(pageIndex?: number): Promise<DIWLoadDataResult<number>> {
		if (pageIndex === undefined) {
			pageIndex = 1;
		}

		let startIndex = (pageIndex - 1) * params.pageSize;
		const endIndex = Math.min(params.total, startIndex + params.pageSize);
		let remain = Math.max(0, endIndex - startIndex);

		if (pageIndex === 1) {
			fullItems = [];
			trackInfo = undefined;
			sourceCompleted = false;
			firstPage = true;
		}

		const items: Array<Record<string, any>> = [];

		if (remain > 0) {
			for (let i = startIndex; i < endIndex; ++i) {
				if (i < fullItems.length) {
					items.push(Object.assign({}, fullItems[i]));
					--remain;
				} else {
					break;
				}
			}
		}

		while (remain > 0 && !sourceCompleted) {
			const d = await (firstPage ? params.loadData() : params.loadData(trackInfo));
			firstPage = false;

			fullItems.push(...d.items);
			for (let i = 0; remain > 0 && i < d.items.length; ++i) {
				items.push(Object.assign({}, d.items[i]));
				--remain;
			}

			if (d.hasMore) {
				trackInfo = d.trackInfo;
			} else {
				sourceCompleted = true;
			}
		}

		if (remain > 0 && fullItems.length > 0) {
			startIndex = endIndex - remain;
			for (let i = startIndex; i < endIndex; ++i) {
				const item = fullItems[i % fullItems.length];
				items.push(Object.assign({}, item, { [params.rowKey]: `${item[params.rowKey]}_${i}` }));
			}
		}

		if (__CONFIG_IS_DEV_MODE__) {
			await hack(7);
		}

		const hasMore = endIndex < params.total && items.length > 0;

		return { hasMore, items, trackInfo: hasMore ? pageIndex + 1 : 0 };
	}

	return { loadData };
}
