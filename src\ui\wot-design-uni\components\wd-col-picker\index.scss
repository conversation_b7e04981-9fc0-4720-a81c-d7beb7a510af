@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(col-picker) {
		@include e(list-item) {
			@include when(disabled) {
				color: $wot-dark-color3;
			}
		}

		@include e(list-item-tip) {
			color: $wot-dark-color-gray;
		}

		:deep(.wd-col-picker__arrow) {
			color: $wot-dark-color;
		}

		@include e(list) {
			color: $wot-dark-color;
		}

		@include e(selected) {
			color: $wot-dark-color;
		}

		:deep(.wd-col-picker__cell--placeholder) {
			.wd-cell__value {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(col-picker) {
	@include edeep(cell) {
		@include when(disabled) {
			.wd-cell__value {
				color: $wot-input-disabled-color;
				cursor: not-allowed;
			}
		}
		@include when(error) {
			.wd-cell__value {
				color: $wot-input-error-color;
			}
			.wd-col-picker__arrow {
				color: $wot-input-error-color;
			}
		}
		@include when(large) {
			.wd-col-picker__arrow {
				font-size: $wot-cell-icon-size-large;
			}
		}

		@include m(placeholder) {
			.wd-cell__value {
				color: $wot-input-placeholder-color;
			}
		}
	}

	@include edeep(arrow) {
		display: block;
		font-size: $wot-cell-icon-size;
		color: $wot-cell-arrow-color;
		line-height: $wot-cell-line-height;
	}

	@include e(selected) {
		height: $wot-col-picker-selected-height;
		font-size: $wot-col-picker-selected-fs;
		color: $wot-col-picker-selected-color;
		overflow: hidden;
	}

	@include e(selected-container) {
		position: relative;
		display: flex;
		user-select: none;
	}

	@include e(selected-item) {
		flex: 0 0 auto;
		height: $wot-col-picker-selected-height;
		line-height: $wot-col-picker-selected-height;
		padding: $wot-col-picker-selected-padding;

		@include when(selected) {
			font-weight: $wot-col-picker-selected-fw;
		}
	}

	@include e(selected-line) {
		position: absolute;
		bottom: 5px;
		width: $wot-col-picker-line-width;
		left: 0;
		height: $wot-col-picker-line-height;
		background: $wot-col-picker-line-color;
		z-index: 1;
		border-radius: calc($wot-col-picker-line-height / 2);
		box-shadow: $wot-col-picker-line-box-shadow;
	}

	@include e(list-container) {
		position: relative;
	}

	@include e(list) {
		height: $wot-col-picker-list-height;
		padding-bottom: $wot-col-picker-list-padding-bottom;
		box-sizing: border-box;
		overflow: auto;
		color: $wot-col-picker-list-color;
		font-size: $wot-col-picker-list-fs;
		-webkit-overflow-scrolling: touch;
	}

	@include e(list-item) {
		display: flex;
		padding: $wot-col-picker-list-item-padding;
		align-items: flex-start;

		@include when(selected) {
			color: $wot-col-picker-list-color-checked;

			:deep(.wd-col-picker__checked) {
				opacity: 1;
			}
		}
		@include when(disabled) {
			color: $wot-col-picker-list-color-disabled;
		}
	}

	@include e(list-item-label) {
		line-height: 1.285;
	}

	@include e(list-item-tip) {
		margin-top: 2px;
		font-size: $wot-col-picker-list-fs-tip;
		color: $wot-col-picker-list-color-tip;
	}

	@include edeep(checked) {
		display: block;
		margin-left: 4px;
		font-size: $wot-col-picker-list-checked-icon-size;
		color: $wot-col-picker-list-color-checked;
		opacity: 0;
	}

	@include e(loading) {
		display: flex;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		align-items: center;
		justify-content: center;
	}
}
