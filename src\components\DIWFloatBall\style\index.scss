// style基础样式库

// 主题颜色
$primary-color: #ff5000 !default;
$success-color: #4cd964 !default;
$warning-color: #f0ad4e !default;
$error-color: #dd524d !default;
$info-color: #909399 !default;

// 文本颜色
$text-color-primary: #303133 !default;
$text-color-regular: #606266 !default;
$text-color-secondary: #909399 !default;
$text-color-disabled: #c0c4cc !default;

// 背景颜色
$bg-color-white: #ffffff !default;
$bg-color-gray: #f5f7fa !default;
$bg-color-disabled: #ebeef5 !default;

// 边框颜色
$border-color-light: #dcdfe6 !default;
$border-color-medium: #e4e7ed !default;
$border-color-dark: #d4d7de !default;

// 尺寸
$font-size-small: 12px !default;
$font-size-base: 14px !default;
$font-size-medium: 16px !default;
$font-size-large: 18px !default;

// 边距
$spacing-small: 8px !default;
$spacing-base: 12px !default;
$spacing-medium: 16px !default;
$spacing-large: 24px !default;

// CSS变量创建函数
@function create-var($name, $value) {
	@return var(--l-#{$name}, #{$value});
}
