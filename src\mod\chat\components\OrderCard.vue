<template>
	<view class="order-card">
		<view v-if="loading === 0" class="loading-container">
			<wd-loading size="small" />
			<text class="loading-text">合同详情加载中...</text>
		</view>
		<view v-else-if="loading === 1" class="order-content" @click="viewOrder(props.orderId)">
			<!-- 合同头部信息 -->
			<view class="order-header">
				<view class="header-left">
					<wd-icon name="shop" size="32rpx" color="#409eff" />
					<text class="order-no">{{ orderDetail!.orderNo }}</text>
				</view>
				<view class="status-container">
					<wd-tag :type="getStatusTagType(orderDetail!.status)" size="small">
						{{ getOrderStatusText(orderDetail!.status) }}
					</wd-tag>
				</view>
			</view>

			<!-- 合同金额 -->
			<view class="order-amount">
				<DIWCurrency class="amount-value" :value="orderDetail!.totalAmount" />
			</view>

			<!-- 合同主体信息 -->
			<view class="order-body">
				<view class="order-parties">
					<view class="party-item">
						<wd-icon name="user" size="24rpx" color="#999" />
						<text class="party-name">{{ orderDetail!.buyerName || '未知买家' }}</text>
					</view>
					<view class="party-item">
						<wd-icon name="shop" size="24rpx" color="#999" />
						<text class="party-name">{{ orderDetail!.sellerName || '未知卖家' }}</text>
					</view>
				</view>

				<view class="order-time">
					<wd-icon name="clock" size="24rpx" color="#ccc" />
					<text class="time-text">{{ formatOrderTime(orderDetail!.createTime) }}</text>
				</view>
			</view>
		</view>
		<view v-else class="error-state" @click="reloadOrderDetail()">
			<wd-icon name="warning" size="32rpx" color="#f56c6c" />
			<text class="error-text">合同加载失败，点击重试</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useOrderStore } from '@/store/order';

const props = defineProps<{
	orderId: string;
	orderStatus?: Array<{ label: string; value: string }>;
}>();

const { navigateTo, protect, sessionInfo } = useFramework();

const { getOrderDetail } = useOrderStore();

const orderDetail = ref<Record<string, any> | null>(null);
const loading = ref<0 | 1 | 2>(0);

watch(
	() => props.orderId,
	() => {
		reloadOrderDetail();
	},
	{ immediate: true }
);

function formatOrderTime(time: string) {
	if (!time) return '';
	try {
		return formatDateTime(new Date(time), 'yyyy-MM-dd HH:mm');
	} catch (e) {
		return time;
	}
}

function getOrderStatusText(status: number | string) {
	// 优先使用传入的状态字典
	if (props.orderStatus) {
		const statusOption = props.orderStatus.find((option) => option.value === String(status));
		return statusOption?.label || '未知状态';
	}

	// 如果没有传入状态字典，使用默认映射（保持向后兼容）
	const statusMap: Record<string, string> = {
		'0': '待审核',
		'1': '待复核',
		'2': '待签约',
		'3': '已签署',
		'4': '交易完成',
		'5': '交易关闭',
		'6': '待确认',
		'7': '已失效',
		'8': '已删除',
	};
	return statusMap[String(status)] || '未知状态';
}

function getStatusTagType(status: number | string): 'primary' | 'success' | 'warning' | 'danger' {
	const statusStr = String(status);
	// 根据实际订单状态定义类型
	switch (statusStr) {
		case '0': // 待审核
		case '1': // 待复核
		case '2': // 待签约
			return 'warning';
		case '3': // 已签署
		case '4': // 交易完成
			return 'success';
		case '5': // 交易关闭
		case '7': // 已失效
		case '8': // 已删除
			return 'danger';
		case '6': // 待确认
			return 'primary';
		default:
			return 'primary';
	}
}

function reloadOrderDetail() {
	protect(async () => {
		try {
			loading.value = 0;
			orderDetail.value = await getOrderDetail(props.orderId);
			loading.value = 1;
		} catch (err) {
			loading.value = 2;
			throw err;
		}
	});
}

function viewOrder(orderId: string) {
	protect(async () => {
		if (sessionInfo.value && loading.value === 1) {
			navigateTo({ url: '/mod/order/orderDetail', params: { id: orderId } });
		}
	});
}
</script>

<style scoped lang="scss">
.order-card {
	border: 1rpx solid #e0e0e0;
	border-radius: 16rpx;
	overflow: hidden;
	background-color: white;
	transition: all 0.2s;
	width: 100%;
	max-width: 500rpx;
	min-width: 400rpx;
	margin: 8rpx 0;
}

.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 48rpx 24rpx;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 16rpx;
}

.order-content {
	padding: 24rpx;
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
	gap: 16rpx;
}

.header-left {
	display: flex;
	align-items: flex-start;
	flex: 1;
	gap: 8rpx;
	min-width: 0;
}

.order-no {
	font-size: 28rpx;
	font-weight: bold;
	color: #409eff;
	line-height: 1.4;
	word-wrap: break-word;
	word-break: break-all;
	flex: 1;
	min-width: 0;
}

.status-container {
	flex-shrink: 0;
}

.order-amount {
	margin-bottom: 16rpx;
}

.amount-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #ff5000;
}

.order-body {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.order-parties {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.party-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.party-name {
	font-size: 24rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

.order-time {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-top: 8rpx;
}

.time-text {
	font-size: 24rpx;
	color: #999;
}

.error-state {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
}

.error-text {
	font-size: 28rpx;
	color: #f56c6c;
}
</style>
