interface IMPeer {
	name: string;
}

interface IMBaseMessage {
	id: string;
	from?: IMPeer;
	time: Date;
	isMine: boolean;
	readFlag: boolean;
}

interface IMTextMessage extends IMBaseMessage {
	type: 0;
	content: string;
}

interface IMImageMessage extends IMBaseMessage {
	type: 1;
	fileId: string;
}

interface IMOrderMessage extends IMBaseMessage {
	type: 2;
	orderId: string;
}

interface IMProductMessage extends IMBaseMessage {
	type: 3;
	productId: string;
}

type IMMessage = IMTextMessage | IMImageMessage | IMOrderMessage | IMProductMessage;

interface IMSimpleConversation {
	type: 0;
	id: string;
	title: string;
	subtitle: string;
	avatarUrl: string;
	messages: IMMessage[];
	last: IMMessage | null;
	unread: number;
	online: number;
	peerId: string;
	tenantType: string | number;
	isInvalid: boolean;

	loadAfter(id: string): IMMessage[];
	loadMore(limit: number, id?: string): Promise<IMMessage[]>;
	sendText(content: string): void;
	sendImage(fileId: string): void;
	sendOrder(orderId: string): void;
	sendProduct(productId: string): void;
	mark(ids: string | string[]);
}

type IMConversation = IMSimpleConversation;

interface IMTransport {
	onOpen(callback: () => void): void;
	onClose(callback: () => void): void;
	onError(callback: () => void): void;
	onMessage(callback: (data: any) => void): void;
	send(data: any): void;
	close(): void;
}

// 通知消息
interface NoticeMessage {
	type: 'notice';
	noticeData?: {
		messageId: string;
		messageType: number;
		messageTitle: string;
		category?: string;
		readFlag?: boolean;
		timestamp?: number;
	};
	[key: string]: any;
}

// 系统消息
interface SystemMessage {
	type: 'system';
	noticeData: {
		messageId: string;
		messageType: number;
		messageTitle: string;
		content?: string;
		level?: 'info' | 'warning' | 'error' | 'success';
		timestamp?: number;
	};
}

// 用户登出消息
interface UserLogoutMessage {
	type: 'userLogout';
	noticeData?: {
		userId: string;
		timestamp: number;
		reason?: string;
	};
}

// Ping/Pong 心跳消息
interface PingMessage {
	type: 'ping';
	timestamp?: number;
}

interface PongMessage {
	type: 'pong';
	timestamp?: number;
}

// 消息类型枚举
type MessageType = 'notice' | 'system' | 'userLogout' | 'ping' | 'pong';

// 统一的WebSocket消息类型
type WebSocketMessage = NoticeMessage | SystemMessage | UserLogoutMessage | PingMessage | PongMessage | { type: string; [key: string]: any };

// 消息处理器类型
type MessageHandler = (msg: WebSocketMessage) => void;

// 消息订阅配置
interface MessageSubscription {
	type: MessageType | string;
	handler: MessageHandler;
	once?: boolean; // 是否只处理一次
}

// WebSocket连接状态
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'reconnecting' | 'error';

// WebSocket配置
interface WebSocketConfig {
	url: string;
	accessToken: string;
	tenantId: string;
	reconnectInterval?: number;
	maxReconnectAttempts?: number;
	heartbeatInterval?: number;
	messageDeadInterval?: number;
}
