<template>
	<DIWAppPage mode="1" :title="title">
		<DIWProtect>
			<DIWRoot>
				<view class="chat-container">
					<view
						class="message-container"
						@touchmove="handleContainerTouchMove"
						:style="{
							height: `${getMessageContainerHeight()}px`,
						}"
					>
						<scroll-view
							id="scrollView"
							class="message-area"
							ref="scrollView"
							scroll-y
							:scroll-top="scrollTop"
							:scroll-with-animation="true"
							:enable-back-to-top="false"
							:enhanced="true"
							:bounces="false"
							:show-scrollbar="false"
							:fast-deceleration="false"
							@click="closeEmojiKeyboard"
							@scroll="handleScroll"
							@scrolltoupper="handleScrollToUpper"
							@touchstart="handleScrollViewTouchStart"
							@touchmove="handleScrollViewTouchMove"
							@touchend="handleScrollViewTouchEnd"
						>
							<template v-for="msg in displayMessageList" :key="msg.m.id" class="message-item" :class="{ mine: msg.m.isMine }">
								<view v-if="msg.t" class="time-divider">
									<text class="time-text">{{ msg.t }}</text>
								</view>
								<view class="message-item" :class="{ mine: msg.m.isMine }">
									<view class="message-line">
										<view v-if="!msg.m.isMine" class="avatar">
											<image v-if="conv?.avatarUrl" class="avatar-image" :src="conv.avatarUrl" mode="aspectFill" />
										</view>
										<text v-if="msg.m.isMine" class="read-status-text">{{ msg.m.readFlag ? '已读' : '未读' }}</text>
										<view class="message-frame" :class="{ their: !msg.m.isMine, mine: msg.m.isMine }" @click="handleClickMessage(msg.m)">
											<template v-if="msg.m.type === 0">
												<text
													class="whitespace-pre-wrap break-all"
													:data-msg-id="msg.m.id"
													:class="{ 'message-body-unread': !msg.m.isMine && !msg.m.readFlag }"
													>{{ msg.m.content }}
												</text>
											</template>
											<template v-else-if="msg.m.type === 1">
												<image
													class="max-w-100px"
													mode="widthFix"
													:data-msg-id="msg.m.id"
													:class="{ 'message-body-unread': !msg.m.isMine && !msg.m.readFlag }"
													:src="mapFileIdToUrl(msg.m.fileId)"
													:lazy-load="true"
													:fade-show="true"
													@click="previewImages(msg.m)"
													@load="handleImageLoad"
													@error="handleImageError"
												/>
											</template>
											<template v-if="msg.m.type === 2">
												<OrderCard
													:orderId="msg.m.orderId"
													:orderStatus="order_status"
													:data-msg-id="msg.m.id"
													:class="{ 'message-body-unread': !msg.m.isMine && !msg.m.readFlag }"
												/>
											</template>
											<template v-if="msg.m.type === 3">
												<ProductCard
													:productId="msg.m.productId"
													:data-msg-id="msg.m.id"
													:class="{ 'message-body-unread': !msg.m.isMine && !msg.m.readFlag }"
												/>
											</template>
										</view>
										<view v-if="msg.m.isMine" class="avatar">
											<image v-if="myAvatarUrl" class="avatar-image" :src="myAvatarUrl" mode="aspectFill" />
										</view>
									</view>
								</view>
							</template>
						</scroll-view>

						<!-- 新消息提示组件 -->
						<view v-if="newMessageCount > 0 && !isNearBottomSync" class="new-message-tip" @click="handleNewMessageTipClick">
							<view class="tip-content">
								<wd-icon name="arrow-down" size="16px" color="#ffffff"></wd-icon>
								<text>{{ newMessageTipText }}</text>
							</view>
						</view>
					</view>

					<view class="bottom-bar-container">
						<DIWBottomBar class="z-1" :safe-area="false">
							<!-- 失效聊天提示 -->
							<view v-if="isInvalid" class="invalid-chat-notice">
								<wd-icon name="warning" size="40rpx" color="#f0ad4e"></wd-icon>
								<text>当前联系人已失效，您只能查看历史消息，无法发送新消息</text>
							</view>

							<!-- 正常聊天输入区域 -->
							<view v-else class="chat-input-container">
								<view class="input-wrapper">
									<textarea
										id="message-input"
										class="message-input"
										v-model="messageText"
										:focus="focus"
										:auto-height="true"
										:show-confirm-bar="false"
										:maxlength="500"
										:adjust-position="false"
										:always-embed="false"
										:confirm-hold="true"
										cursor-spacing="0"
										:hold-keyboard="false"
										confirm-type="send"
										placeholder="请输入消息..."
										@confirm="send"
										@focus="handleInputFocus"
										@blur="handleInputBlur"
									/>
								</view>
								<view class="action-buttons">
									<wd-button
										v-if="!emojiKeyboardVisible"
										@click="openEmojiKeyboard()"
										custom-class="emoji-btn"
										custom-style="background-color: #ffffff; border-color: #ffffff"
										type="info"
										size="small"
									>
										😊
									</wd-button>
									<wd-button
										v-else
										@click="closeEmojiKeyboardAndOpenSystemKeyboard()"
										custom-class="emoji-btn"
										custom-style="background-color: #ffffff; border-color: #ffffff"
										type="info"
										size="small"
									>
										<wd-icon name="keyboard-collapse" size="22px"></wd-icon>
									</wd-button>
									<wd-button
										v-if="messageText.trim().length <= 0"
										@click="handlePlusClick"
										custom-class="emoji-btn"
										custom-style="background-color: #ffffff; border-color: #ffffff"
										type="info"
										size="small"
									>
										<wd-icon name="add" size="22px"></wd-icon>
									</wd-button>
									<wd-button
										v-if="messageText.trim().length > 0"
										custom-class="send-btn"
										custom-style="background-color: #ff5000; border-color: #ff5000"
										@click="handleSendClick"
										type="primary"
										size="small"
									>
										发送
									</wd-button>
								</view>
							</view>
							<EmojiKeyboard
								v-show="emojiKeyboardVisible && !isInvalid"
								class="w-full"
								@key-click="handleEmojiClick"
								@back-click="handleBackClick"
								@close-click="closeEmojiKeyboard"
							/>
						</DIWBottomBar>
					</view>
					<!-- 操作菜单 -->
					<wd-action-sheet v-model="showActionMenu" :actions="actionMenuItems" @select="handleActionSelect" cancel-text="取消" />
				</view>
			</DIWRoot>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
import DIWProtect from '@/components/DIWProtect/DIWProtect.vue';
import EmojiKeyboard from './components/EmojiKeyboard.vue';
import OrderCard from './components/OrderCard.vue';
import ProductCard from './components/ProductCard.vue';

const instanceProxy = getCurrentInstance()!.proxy;

const { usePageQuery, conversations, mapFileIdToUrl, protectOp, protect, platformUploadFile, sessionInfo, invokeTo, mapDict } = useFramework();

const { order_status } = mapDict('order_status');

const scrollView = ref();
const scrollTop = ref(0);
const focus = ref(false);
let loadingMore = false;
let hasMore = true;

// 消息发送状态枚举
enum MessageSendState {
	IDLE = 'idle',
	SENDING = 'sending',
	SENT = 'sent',
	CONFIRMED = 'confirmed',
}

// 滚动状态跟踪（参考PC端）
const isUserScrolling = ref(false);
const userScrollTimeout = ref<ReturnType<typeof setTimeout>>();
const BOTTOM_THRESHOLD = 100; // 距离底部100px以内认为是在底部，提升用户体验

// 先声明主要响应式变量，解决初始化顺序问题
const messageList = ref<IMMessage[]>([]);
const newMessageCount = ref(0); // 新消息计数器
const isNearBottomSync = ref(true); // 同步记录是否在底部附近
const lastMessageIdBeforeNewMessages = ref<string | null>(null); // 新消息到达前的最后一条消息ID

// 定义接口
interface DisplayMessage {
	t?: string;
	m: IMMessage;
}

// 消息发送状态管理
const messageSendState = ref<MessageSendState>(MessageSendState.IDLE);
const lastSentMessageId = ref<string | null>(null); // 最后发送的消息ID
const pendingImageLoads = ref(0); // 待加载的图片数量
const lastSentTime = ref(0); // 最后发送消息的时间
const isInitialLoading = ref(true); // 标记是否正在初始化加载历史消息
const isLoadingHistory = ref(false); // 标记是否正在加载历史消息
const lastHistoryLoadTime = ref(0); // 最后一次加载历史消息的时间

// 基于状态的计算属性
const justSentMessage = computed(() => {
	return messageSendState.value === MessageSendState.SENDING || messageSendState.value === MessageSendState.SENT;
});

// 状态转换函数
function setMessageSendState(newState: MessageSendState, messageId?: string) {
	messageSendState.value = newState;
	if (messageId) {
		lastSentMessageId.value = messageId;
	}
	if (newState === MessageSendState.SENDING) {
		lastSentTime.value = Date.now();
	}
}

// 工具函数：创建可控的延迟
function createDelay(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

// 监听消息列表变化，自动管理发送状态
watch(
	() => messageList.value.length,
	(newLength, oldLength) => {
		// 如果当前处于发送状态，检查是否有新消息到达
		if (messageSendState.value === MessageSendState.SENT && newLength > (oldLength || 0)) {
			// 检查最新的消息是否是自己发送的
			const latestMessage = messageList.value[messageList.value.length - 1];
			if (latestMessage && latestMessage.isMine) {
				// 如果是自己发送的消息，标记为已确认
				setMessageSendState(MessageSendState.CONFIRMED, latestMessage.id);
				// 使用 nextTick 确保其他逻辑处理完成后再重置到空闲状态
				nextTick(() => {
					// 再次检查状态，确保没有新的发送操作
					if (messageSendState.value === MessageSendState.CONFIRMED) {
						setMessageSendState(MessageSendState.IDLE);
					}
				});
			}
		}
	}
);

// 监听发送状态变化，自动重置新消息计数
watch(
	() => messageSendState.value,
	(newState) => {
		if (newState === MessageSendState.SENDING) {
			// 发送消息时重置新消息计数
			newMessageCount.value = 0;
			lastMessageIdBeforeNewMessages.value = null;
		}
	}
);

// 监听新消息计数和相关状态变化，自动验证计数正确性
watch([() => newMessageCount.value, () => lastMessageIdBeforeNewMessages.value, () => messageList.value.length], () => {
	// 使用 nextTick 确保所有状态更新完成后再验证
	nextTick(() => {
		validateAndFixNewMessageCount();
	});
});

// 监听滚动状态变化，自动处理新消息计数
watch(
	() => isNearBottomSync.value,
	(nearBottom) => {
		if (nearBottom) {
			// 在底部时，清空新消息计数
			newMessageCount.value = 0;
			lastMessageIdBeforeNewMessages.value = null;
		}
	}
);

// 虚拟滚动优化：分页渲染配置
const VIRTUAL_SCROLL_CONFIG = {
	pageSize: 50, // 每页显示的消息数量
	bufferSize: 10, // 缓冲区大小
	maxRenderCount: 100, // 最大渲染数量
};
const currentPage = ref(0);
const maxPage = ref(0);

// 表情是否启用
const emojiKeyboardVisible = ref(false);

// 操作菜单显示状态
const showActionMenu = ref(false);

const keyboardHeight = ref(0);

// 重新设计布局：系统高度信息
const systemInfo = ref({
	screenHeight: 0,
	statusBarHeight: 0,
	navigationBarHeight: 0,
	safeAreaBottom: 0,
	bottomBarHeight: 80, // 底部输入栏的固定高度（px）
});

// 重新设计布局：消息容器可用高度
const messageContainerHeight = ref(0);

// 图片上传配置
const IMAGE_CONFIG = {
	maxSize: 10 * 1024 * 1024, // 10MB
	maxSizeMB: 10,
	supportedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
	accept: 'image/*',
};

// 从文件名检测文件类型
function getFileExtension(fileName: string): string {
	if (!fileName) return '';
	const lastDot = fileName.lastIndexOf('.');
	return lastDot >= 0 ? fileName.substring(lastDot + 1).toLowerCase() : '';
}

// 检查是否为支持的图片格式
function isSupportedImageFormat(fileName: string, mimeType?: string): boolean {
	// 优先使用MIME类型检查
	if (mimeType && IMAGE_CONFIG.supportedTypes.includes(mimeType.toLowerCase())) {
		return true;
	}

	// 回退到文件扩展名检查
	const ext = getFileExtension(fileName);
	const supportedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
	return supportedExts.includes(ext);
}

// iOS滑动边界控制相关变量
const isIOS = ref(false);
const inputFocused = ref(false);
const scrollViewInfo = ref({
	scrollTop: 0,
	scrollHeight: 0,
	height: 0,
});
const touchStartY = ref(0);
const lastTouchY = ref(0);
const isScrollAtTop = ref(false);
const isScrollAtBottom = ref(false);

// 检测是否为iOS设备
function detectIOS() {
	// #ifdef APP-PLUS
	const systemInfo = uni.getSystemInfoSync();
	isIOS.value = systemInfo.platform === 'ios';
	// #endif

	// #ifdef H5
	isIOS.value = /(iPad|iPhone|iPod)/i.test(navigator.userAgent);
	// #endif
}

// 重新设计布局：初始化系统信息
function initSystemInfo() {
	const sysInfo = uni.getSystemInfoSync();

	// 记录安全区域信息（仅用于调试和其他用途，不加入bottomBarHeight）
	const safeAreaBottom = sysInfo.safeAreaInsets?.bottom || 0;
	// 底部输入栏基础高度，不包含安全区域（安全区域由CSS的env()处理）
	const baseBottomBarHeight = isIOS.value ? 60 : 80;

	systemInfo.value = {
		screenHeight: sysInfo.screenHeight,
		statusBarHeight: sysInfo.statusBarHeight || 0,
		navigationBarHeight: 44, // 导航栏标准高度
		safeAreaBottom: safeAreaBottom, // 仅记录，不用于计算
		bottomBarHeight: baseBottomBarHeight, // 底部输入栏基础高度（不含安全区域）
	};

	console.log('系统信息初始化:', systemInfo.value);
	console.log('安全区域将由CSS的env(safe-area-inset-bottom)处理');

	// 初始化后立即计算消息容器高度
	calculateMessageContainerHeight();
}

// 重新设计布局：计算消息容器的可用高度
function calculateMessageContainerHeight() {
	// 计算表情键盘的高度（如果显示的话）
	const emojiKeyboardHeight = emojiKeyboardVisible.value ? 280 : 0;

	// 选择较大的键盘高度（系统键盘或表情键盘）
	const effectiveKeyboardHeight = Math.max(keyboardHeight.value, emojiKeyboardHeight);
	console.log(systemInfo.value);

	const availableHeight =
		systemInfo.value.screenHeight -
		systemInfo.value.statusBarHeight -
		systemInfo.value.navigationBarHeight -
		systemInfo.value.bottomBarHeight -
		effectiveKeyboardHeight;

	messageContainerHeight.value = Math.max(200, availableHeight); // 最小高度200px

	console.log('计算消息容器高度:', {
		screenHeight: systemInfo.value.screenHeight,
		statusBarHeight: systemInfo.value.statusBarHeight,
		navigationBarHeight: systemInfo.value.navigationBarHeight,
		bottomBarHeight: systemInfo.value.bottomBarHeight,
		keyboardHeight: keyboardHeight.value,
		emojiKeyboardHeight: emojiKeyboardHeight,
		effectiveKeyboardHeight: effectiveKeyboardHeight,
		result: messageContainerHeight.value,
	});
}

// 重新设计布局：获取消息容器高度的响应式函数
function getMessageContainerHeight() {
	return messageContainerHeight.value;
}

const pq = usePageQuery();
const isInvalid = computed(() => {
	// 优先使用会话对象上的属性，如果不存在则使用URL参数
	if (conv.value && conv.value.isInvalid !== undefined) {
		return conv.value.isInvalid;
	}
	// 将字符串参数转换为布尔值
	return pq.value.isInvalid === 'true';
});

const conv = computed(() => {
	return conversations.value.find((e) => e.id === pq.value.id);
});

const myAvatarUrl = computed(() => {
	if (sessionInfo.value && sessionInfo.value.avatarUrl) {
		return sessionInfo.value.avatarUrl;
	}
	return '';
});

function alignDate(t: Date) {
	t.setHours(0);
	t.setMinutes(0);
	t.setSeconds(0);
	t.setMilliseconds(0);
}

const refTime = ref(
	(function () {
		const t = new Date();
		alignDate(t);
		return t;
	})()
);

const TIME_SPAN = 5 * 60000;

function formatTimeText(t: Date, refTime: Date) {
	const t1 = new Date(t);
	alignDate(t1);
	const dt = refTime.getTime() - t1.getTime();
	if (dt === 86400000) {
		return formatDateTime(t, '昨天 HH:mm');
	}

	if (dt === 172800000) {
		return formatDateTime(t, '前天 HH:mm');
	}

	if (t.getFullYear() === refTime.getFullYear()) {
		if (t.getMonth() === refTime.getMonth()) {
			if (t.getDate() === refTime.getDate()) {
				return formatDateTime(t, 'HH:mm');
			}
		}

		return formatDateTime(t, 'M月d日 HH:mm');
	}

	return formatDateTime(t, 'yyyy-MM-dd HH:mm');
}

const displayMessageList = computed(() => {
	const result: DisplayMessage[] = [];
	const totalMessages = messageList.value.length;

	// 虚拟滚动优化：只渲染可见区域的消息
	if (totalMessages > VIRTUAL_SCROLL_CONFIG.maxRenderCount) {
		// 计算渲染范围
		const startIndex = Math.max(0, totalMessages - VIRTUAL_SCROLL_CONFIG.maxRenderCount);
		const endIndex = totalMessages;

		// 更新分页信息
		maxPage.value = Math.ceil(totalMessages / VIRTUAL_SCROLL_CONFIG.pageSize);
		currentPage.value = Math.ceil(endIndex / VIRTUAL_SCROLL_CONFIG.pageSize);

		// 只处理需要渲染的消息
		for (let i = startIndex; i < endIndex; ++i) {
			const m = messageList.value[i];
			const prevMessage = i > 0 ? messageList.value[i - 1] : null;
			const t = !prevMessage || m.time.getTime() - prevMessage.time.getTime() >= TIME_SPAN ? formatTimeText(m.time, refTime.value) : undefined;
			result.push({ m, t });
		}
	} else {
		// 消息数量较少时，正常渲染所有消息
		for (let i = 0; i < totalMessages; ++i) {
			const m = messageList.value[i];
			const t =
				i === 0 || m.time.getTime() - messageList.value[i - 1].time.getTime() >= TIME_SPAN ? formatTimeText(m.time, refTime.value) : undefined;
			result.push({ m, t });
		}
	}

	return result;
});

const messageText = ref('');

// 操作菜单项
const actionMenuItems = computed(() => {
	const items = [{ name: '图片', value: 'image' }];

	items.push({ name: '合同', value: 'order' });
	items.push({ name: '商品', value: 'product' });

	return items;
});

const CHECK_DEBOUNCE_INTERVAL = 1000;

function checkForUnreadImpl() {
	uni
		.createSelectorQuery()
		.in(instanceProxy)
		.select('#scrollView')
		.boundingClientRect((data) => {
			if (data) {
				const rc1 = data as UniApp.NodeInfo;
				uni
					.createSelectorQuery()
					.in(instanceProxy)
					.selectAll('.message-body-unread')
					.fields({ dataset: true, rect: true }, (data) => {
						if (Array.isArray(data)) {
							const ids: string[] = [];
							for (const n of data) {
								if (n.top! >= rc1.top! && n.bottom! <= rc1.bottom!) {
									ids.push(n.dataset.msgId!);
								} else if (rc1.top! >= n.top! && rc1.bottom! <= n.bottom!) {
									ids.push(n.dataset.msgId!);
								}
							}

							if (ids.length > 0) {
								if (conv.value) {
									conv.value.mark(ids);
								}
							}
						}
					})
					.exec();
			}
		})
		.exec();
}

// 使用现代化的防抖逻辑检查未读消息
let checkForUnreadTimer: ReturnType<typeof setTimeout> | null = null;

function checkForUnread() {
	// 清除之前的定时器
	if (checkForUnreadTimer) {
		clearTimeout(checkForUnreadTimer);
	}

	// 设置新的防抖定时器
	checkForUnreadTimer = setTimeout(() => {
		nextTick(() => {
			checkForUnreadImpl();
		});
		checkForUnreadTimer = null;
	}, CHECK_DEBOUNCE_INTERVAL);
}

const title = computed(() => {
	if (conv.value) {
		return conv.value.title;
	}
});

// 新消息提示文案（支持单复数形式）
const newMessageTipText = computed(() => {
	const count = newMessageCount.value;
	return `${count}条新消息`;
});

// 触觉反馈功能 - 新消息到达时震动提醒
function triggerHapticFeedback() {
	try {
		// #ifdef APP-PLUS
		// APP平台使用原生震动API
		uni.vibrateShort({
			type: 'light', // 轻微震动
			success: () => {
				console.log('新消息震动反馈成功');
			},
			fail: (err) => {
				console.log('震动反馈失败:', err);
			},
		});
		// #endif

		// #ifdef H5
		// H5平台使用Web Vibration API
		if (navigator.vibrate) {
			navigator.vibrate(100); // 震动100毫秒
			console.log('新消息震动反馈成功 (H5)');
		} else {
			console.log('当前浏览器不支持震动API');
		}
		// #endif

		// #ifdef MP
		// 小程序平台使用震动API
		uni.vibrateShort({
			type: 'light',
			success: () => {
				console.log('新消息震动反馈成功 (小程序)');
			},
			fail: (err) => {
				console.log('小程序震动反馈失败:', err);
			},
		});
		// #endif
	} catch (error) {
		console.log('触觉反馈执行失败:', error);
	}
}

function send() {
	// 如果聊天已失效，显示提示并阻止发送
	if (isInvalid.value) {
		uni.showToast({
			title: '当前联系人已失效，无法发送消息',
			icon: 'none',
		});
		return;
	}

	if (conv.value) {
		const s = messageText.value.trim();
		if (s) {
			// 先保存当前焦点状态
			const shouldKeepFocus = focus.value || inputFocused.value;

			messageText.value = '';
			// 使用状态管理系统标记发送状态（会自动重置新消息计数）
			setMessageSendState(MessageSendState.SENDING);

			conv.value.sendText(s);

			// 如果输入框之前有焦点，确保发送后保持焦点
			if (shouldKeepFocus) {
				// 使用连续的 nextTick 确保焦点设置的时序
				nextTick(() => {
					focus.value = true;
					nextTick(() => {
						focus.value = true;
					});
				});
			}

			// 发送消息后立即滚动到底部
			nextTick(() => {
				scrollToBottom(true);
				// 标记消息已发送，等待确认
				setMessageSendState(MessageSendState.SENT);
			});
		}
	}
}

function openEmojiKeyboard() {
	// 如果聊天已失效，不允许打开表情键盘
	if (isInvalid.value) {
		uni.showToast({
			title: '当前联系人已失效，无法发送消息',
			icon: 'none',
		});
		return;
	}

	uni.hideKeyboard();
	emojiKeyboardVisible.value = true;

	// 表情键盘打开时，重置系统键盘高度并重新计算布局
	keyboardHeight.value = 0;
	calculateMessageContainerHeight();

	// 使用 nextTick 确保布局计算完成后滚动
	nextTick(async () => {
		await scrollToBottom(true);
	});
}

function closeEmojiKeyboard() {
	emojiKeyboardVisible.value = false;
	// 表情键盘关闭时重新计算布局
	calculateMessageContainerHeight();
}

function closeEmojiKeyboardAndOpenSystemKeyboard() {
	// 关闭表情键盘
	closeEmojiKeyboard();

	// 确保表情键盘关闭后再唤起系统键盘
	nextTick(() => {
		// 先重置 focus 状态，确保能够触发变化
		focus.value = false;

		// 使用连续的 nextTick 确保表情键盘完全关闭后再设置焦点
		nextTick(() => {
			focus.value = true;
			console.log('尝试唤起系统键盘，focus 设置为 true');
		});
	});
}

function sendImage() {
	console.log('sendImage 函数被调用');
	console.log('图片配置:', IMAGE_CONFIG);

	// 统一的文件处理函数（参考DIWFile逻辑）
	const processChooseResult = (res: any, sourceApi: string) => {
		console.log(`${sourceApi} 选择成功，返回数据：`, res);

		try {
			let tempFile: any = null;
			let tempFilePath = '';
			let fileSize = 0;
			let fileName = '';
			let mimeType = '';

			// 处理 chooseMedia 的返回结果（APP平台）
			if (sourceApi === 'chooseMedia' && res.tempFiles && Array.isArray(res.tempFiles) && res.tempFiles.length > 0) {
				tempFile = res.tempFiles[0];
				tempFilePath = tempFile.tempFilePath; // chooseMedia使用tempFilePath
				fileSize = tempFile.size || 0;
				fileName = tempFile.name || '';
				mimeType = tempFile.fileType || '';

				// 如果没有文件名，从路径提取
				if (!fileName && tempFilePath) {
					fileName = tempFilePath.split('/').pop() || '';
				}
				console.log('chooseMedia结果处理:', { tempFilePath, fileSize, fileName, mimeType, tempFile });
			}
			// 处理 chooseFile(H5) 的返回结果
			else if (sourceApi === 'chooseFile' && res.tempFiles && Array.isArray(res.tempFiles) && res.tempFiles.length > 0) {
				tempFile = res.tempFiles[0];
				tempFilePath = res.tempFilePaths?.[0] || '';
				fileSize = (tempFile as File).size || 0;
				fileName = (tempFile as File).name || '';
				mimeType = (tempFile as File).type || '';
				console.log('chooseFile结果处理:', { tempFilePath, fileSize, fileName, mimeType });
			} else {
				console.warn('未知的API类型或数据格式：', sourceApi, res);
				uni.showToast({
					title: '选择图片失败',
					icon: 'none',
				});
				return;
			}

			if (!tempFilePath) {
				console.error('获取图片路径失败');
				uni.showToast({
					title: '获取图片路径失败',
					icon: 'none',
				});
				return;
			}

			// 如果没有文件名，从路径提取
			if (!fileName && tempFilePath) {
				fileName = tempFilePath.split('/').pop() || tempFilePath.split('\\').pop() || '';
			}

			console.log('文件信息:', { fileName, mimeType, fileSize, tempFilePath });

			// 检查是否为支持的图片格式
			if (fileName && !isSupportedImageFormat(fileName, mimeType)) {
				console.log('不支持的图片格式:', { fileName, mimeType });
				uni.showToast({
					title: '不支持该图片格式，请选择jpg、png、gif、webp格式的图片',
					icon: 'none',
					duration: 3000,
				});
				return;
			}

			// 检查文件大小限制
			if (fileSize > 0 && fileSize > IMAGE_CONFIG.maxSize) {
				const sizeInMB = (fileSize / (1024 * 1024)).toFixed(2);
				console.log('文件大小超限:', { fileSize, sizeInMB, maxSizeMB: IMAGE_CONFIG.maxSizeMB });
				uni.showToast({
					title: `图片文件过大 ${sizeInMB}MB，请选择${IMAGE_CONFIG.maxSizeMB}MB以内的图片`,
					icon: 'none',
					duration: 3000,
				});
				return;
			}

			console.log('开始上传图片:', { fileName, fileSize });

			// 上传并发送图片
			protectOp(async () => {
				if (conv.value) {
					// 使用状态管理系统标记发送状态
					setMessageSendState(MessageSendState.SENDING);
					pendingImageLoads.value++;

					const d = await platformUploadFile(tempFile, '10');
					console.log('图片上传成功:', { fileId: d.fileId, originalFile: fileName });

					conv.value.sendImage(d.fileId);
					// 发送图片后滚动到底部
					nextTick(() => {
						scrollToBottom(true);
						setMessageSendState(MessageSendState.SENT);
					});
				}
			}, '发送图片');
		} catch (error) {
			console.error(`处理${sourceApi}选择的图片时出错：`, error);
			uni.showToast({
				title: '处理图片失败',
				icon: 'none',
			});
		}
	};

	// 统一的错误处理函数
	const handleSelectError = (err: any, apiName: string) => {
		console.error(`${apiName}操作失败：`, err);

		// 用户取消操作通常不需要显示错误提示
		if (
			err.errMsg &&
			(err.errMsg.includes('cancel') ||
				err.errMsg.includes('user deny') ||
				err.errMsg.includes('用户取消') ||
				err.errCode === -2 || // 用户取消
				err.errCode === 'cancelChoose')
		) {
			console.log('用户取消选择图片操作');
			return;
		}

		// 权限相关错误特别处理
		if (err.errMsg && (err.errMsg.includes('permission') || err.errMsg.includes('授权') || err.errMsg.includes('权限'))) {
			uni.showToast({
				title: '请检查相机和相册权限',
				icon: 'none',
				duration: 3000,
			});
			return;
		}

		// 其他错误显示通用提示
		uni.showToast({
			title: err.errMsg || '选择图片失败',
			icon: 'none',
		});
	};

	// APP平台（安卓/iOS）- 使用 chooseMedia API
	// #ifdef APP-PLUS
	if (typeof uni.chooseMedia === 'function') {
		uni.chooseMedia({
			count: 1,
			mediaType: ['image'],
			sourceType: ['album', 'camera'],
			sizeType: ['original', 'compressed'],
			camera: 'back',
			success: (res) => processChooseResult(res, 'chooseMedia'),
			fail: (err) => handleSelectError(err, 'chooseMedia'),
		});
	} else {
		// 如果不支持 chooseMedia，提示用户
		uni.showModal({
			title: '提示',
			content: '当前版本不支持图片选择，请升级应用版本',
			showCancel: false,
		});
	}
	// #endif

	// H5平台
	// #ifdef H5
	uni.chooseFile({
		count: 1,
		type: 'image',
		accept: IMAGE_CONFIG.accept,
		success: (res) => processChooseResult(res, 'chooseFile'),
		fail: (err) => handleSelectError(err, 'chooseFile'),
	});
	// #endif
}

function sendOrder() {
	console.log('sendOrder 函数被调用');
	protect(async () => {
		try {
			console.log('准备跳转到合同选择页面');
			const d = await invokeTo<Record<string, any>>({ url: '/mod/chat/orderSelect' });
			console.log('从合同选择页面返回的数据:', d);
			if (d && d.result && conv.value) {
				console.log('选择的合同:', d.result);
				setMessageSendState(MessageSendState.SENDING);
				conv.value.sendOrder(d.result.id);
				// 发送合同后滚动到底部
				nextTick(() => {
					scrollToBottom(true);
					setMessageSendState(MessageSendState.SENT);
				});
			} else {
				console.log('用户取消选择合同或未选择合同');
			}
		} catch (error) {
			console.error('取消发送合同:', error);
			uni.showToast({
				title: '取消发送合同',
				icon: 'none',
			});
		}
	});
}

function sendProduct() {
	console.log('sendProduct 函数被调用');
	protect(async () => {
		try {
			console.log('准备跳转到商品选择页面');
			const d = await invokeTo<Record<string, any>>({ url: '/mod/chat/productSelect' });
			console.log('从商品选择页面返回的数据:', d);
			if (d && d.result && conv.value) {
				console.log('选择的商品:', d.result);
				setMessageSendState(MessageSendState.SENDING);
				conv.value.sendProduct(d.result.id);
				// 发送商品后滚动到底部
				nextTick(() => {
					scrollToBottom(true);
					setMessageSendState(MessageSendState.SENT);
				});
			} else {
				console.log('用户取消选择商品或未选择商品');
			}
		} catch (error) {
			console.error('取消发送商品:', error);
			uni.showToast({
				title: '取消发送商品',
				icon: 'none',
			});
		}
	});
}

function handlePlusClick() {
	console.log('点击了+按钮，显示操作菜单');

	// 如果聊天已失效，显示提示并阻止操作
	if (isInvalid.value) {
		uni.showToast({
			title: '当前联系人已失效，无法发送消息',
			icon: 'none',
		});
		return;
	}

	console.log('当前操作菜单项:', actionMenuItems.value);
	showActionMenu.value = true;
}

function handleActionSelect(action: { item: { name: string; value: string }; index: number }) {
	console.log('handleActionSelect 被调用:', action);
	showActionMenu.value = false;

	// 如果聊天已失效，显示提示并阻止操作
	if (isInvalid.value) {
		uni.showToast({
			title: '当前联系人已失效，无法发送消息',
			icon: 'none',
		});
		return;
	}

	switch (action.item.value) {
		case 'image':
			console.log('调用 sendImage');
			sendImage();
			break;
		case 'order':
			console.log('调用 sendOrder');
			sendOrder();
			break;
		case 'product':
			console.log('调用 sendProduct');
			sendProduct();
			break;
		default:
			console.log('未知的操作类型:', action.item.value);
	}
}

function previewImages(msg: IMImageMessage) {
	const imageMessages = messageList.value.filter((e) => e.type === 1);
	if (imageMessages.length > 0) {
		const index = imageMessages.findIndex((e) => e.id === msg.id);
		if (index >= 0) {
			uni.previewImage({ urls: imageMessages.map((e) => mapFileIdToUrl(e.fileId)), current: index, loop: true, showmenu: true });
			return;
		}
	}

	uni.previewImage({ urls: [mapFileIdToUrl(msg.fileId)], showmenu: true });
}

// 监听会话变化，重置状态（参考PC端）
watch(
	() => conv.value,
	() => {
		// 重置状态
		isUserScrolling.value = false;
		setMessageSendState(MessageSendState.IDLE);
		pendingImageLoads.value = 0;
		isInitialLoading.value = true;
		isLoadingHistory.value = false;
		lastHistoryLoadTime.value = 0;
		newMessageCount.value = 0; // 重置新消息计数
		lastMessageIdBeforeNewMessages.value = null; // 重置最后消息ID
		isNearBottomSync.value = true; // 默认认为在底部

		// 清空消息列表，每次切换会话都重新加载
		messageList.value = [];

		// 延迟一点确保界面清空后再滚动
		nextTick(() => {
			scrollToBottom();
		});
	},
	{ immediate: true }
);

watch(
	() => conv.value && conv.value.last,
	(_, oldVal) => {
		if (conv.value) {
			// 当会话的最新消息变化时，需要加载那些我们还没有获取的新消息
			const locked = conv.value;
			if (messageList.value.length > 0) {
				// 在加载新消息前确定是否在底部
				isNearBottom().then((nearBottom) => {
					// 加载在最后一条消息之后的新消息
					const newMessages = locked.loadAfter(messageList.value[messageList.value.length - 1].id);

					// 如果有新消息
					if (newMessages.length > 0) {
						// 确保是真正的新消息，而不是加载历史消息时的行为
						// 检查是否是新收到的消息（不是加载历史时的现有消息）
						const isReallyNewMessages = oldVal !== undefined; // 非初始化加载

						// 严格判断：只有收到了别人发送的消息且用户不在底部时，才增加新消息计数
						// 确保是别人发送的消息，而不是自己发送的
						const incomingMessages = newMessages.filter((msg) => !msg.isMine);

						// 额外检查：确保不是加载历史消息导致的
						const currentTime = Date.now();
						const isHistoryLoading = isLoadingHistory.value || currentTime - lastHistoryLoadTime.value < 2000;
						const recentlySent = currentTime - lastSentTime.value < 1000; // 1秒内发送的消息

						if (!nearBottom && incomingMessages.length > 0 && !justSentMessage.value && isReallyNewMessages && !isHistoryLoading && !recentlySent) {
							// 增加新消息计数（只计算非自己发送的消息）
							newMessageCount.value += incomingMessages.length;
							// 记录新消息到达前的最后一条消息ID（如果还没有记录）
							if (!lastMessageIdBeforeNewMessages.value) {
								lastMessageIdBeforeNewMessages.value = messageList.value[messageList.value.length - 1].id;
							}
							// 触发震动反馈
							triggerHapticFeedback();

							// 验证逻辑现在由 watch 自动处理，无需手动调用
						}

						// 添加新消息到列表
						messageList.value.push(...newMessages.reverse());

						// 新消息到达时，使用智能滚动
						nextTick(() => {
							// 如果是用户自己发送的消息或当前在底部，滚动到底部
							if (justSentMessage.value || nearBottom) {
								scrollToBottom(true);
							}
						});
					}
				});
			} else {
				// 没有消息时，加载历史消息
				locked.loadMore(20).then((d) => {
					messageList.value = d.reverse();
					// 初始加载时标记为正在加载
					isInitialLoading.value = true;

					// 确保消息加载完成后滚动到底部
					nextTick(async () => {
						await scrollToBottom(true);
						// 滚动完成后重置初始加载标记
						isInitialLoading.value = false;
						// 初始化后确保新消息计数为0
						newMessageCount.value = 0;
						lastMessageIdBeforeNewMessages.value = null;
					});
				});
			}
		}
	},
	{ immediate: true }
);

// 添加一个彻底的检查和重置方法，确保我们从不显示错误的新消息计数
function validateAndFixNewMessageCount() {
	// 如果有新消息计数，检查其合法性
	if (newMessageCount.value > 0) {
		// 1. 检查是否有记录的最后消息ID
		if (!lastMessageIdBeforeNewMessages.value || messageList.value.length === 0) {
			console.log('重置新消息计数：没有最后消息ID或消息列表为空');
			newMessageCount.value = 0;
			lastMessageIdBeforeNewMessages.value = null;
			return;
		}

		// 2. 找到记录的最后消息在当前列表中的位置
		const lastMessageIndex = messageList.value.findIndex((msg) => msg.id === lastMessageIdBeforeNewMessages.value);
		if (lastMessageIndex === -1 || lastMessageIndex === messageList.value.length - 1) {
			console.log('重置新消息计数：最后消息ID不在列表中或已是最后一条');
			newMessageCount.value = 0;
			lastMessageIdBeforeNewMessages.value = null;
			return;
		}

		// 3. 计算新消息中非自己发送的消息数量
		const realNewMessages = messageList.value.slice(lastMessageIndex + 1);
		const incomingMessageCount = realNewMessages.filter((msg) => !msg.isMine).length;

		// 4. 额外检查：如果最近发送过消息，且新消息中包含自己的消息，则重置计数
		const currentTime = Date.now();
		const recentlySent = currentTime - lastSentTime.value < 5000; // 5秒内发送过消息
		const hasOwnMessages = realNewMessages.some((msg) => msg.isMine);

		if (recentlySent && hasOwnMessages) {
			console.log('重置新消息计数：检测到用户最近发送的消息被错误计入');
			newMessageCount.value = 0;
			lastMessageIdBeforeNewMessages.value = null;
			return;
		}

		// 5. 如果实际非自己发送的新消息数量与显示不一致，进行修正
		if (incomingMessageCount !== newMessageCount.value) {
			console.log(`修正新消息计数：从 ${newMessageCount.value} 到 ${incomingMessageCount}`);
			if (incomingMessageCount === 0) {
				// 如果实际上没有新消息，完全重置
				newMessageCount.value = 0;
				lastMessageIdBeforeNewMessages.value = null;
			} else {
				// 更新为正确的数量
				newMessageCount.value = incomingMessageCount;
			}
		}
	}
}

// 在页面显示时调用更彻底的验证
onShow(() => {
	// 使用 nextTick 确保数据已加载
	nextTick(() => {
		validateAndFixNewMessageCount();
	});
});

// 替换之前的resetNewMessageCountIfNeeded函数
function resetNewMessageCountIfNeeded() {
	validateAndFixNewMessageCount();
}

// 监听消息列表变化，为新的图片消息处理计数（参考PC端）
watch(
	() => messageList.value.length,
	(newLength, oldLength) => {
		if (oldLength !== undefined && newLength > oldLength) {
			// 检查新增的消息中是否有图片
			const newMessages = messageList.value.slice(oldLength);
			const newImageCount = newMessages.filter((msg) => msg.type === 1).length;

			if (newImageCount > 0) {
				// 如果是用户自己发送的消息（最近5秒内）
				const recentlySent = Date.now() - lastSentTime.value < 5000;
				if (!recentlySent && !isInitialLoading.value) {
					// 接收到的图片消息（非初始化阶段），增加计数以确保正确滚动
					pendingImageLoads.value += newImageCount;
				}
			}

			// 检查是否不在底部，且不是刚发送的消息，且是新收到的消息
			// 注意：这个监听器主要处理图片消息的加载计数，新消息计数由 conv.value 的监听器统一处理
			// 避免重复计数，这里不再处理新消息计数逻辑
			if (!isInitialLoading.value && !justSentMessage.value) {
				isNearBottom().then((nearBottom) => {
					// 只处理滚动相关逻辑，不处理新消息计数
					if (nearBottom) {
						// 在底部时，清空新消息计数
						newMessageCount.value = 0;
						lastMessageIdBeforeNewMessages.value = null;
						// 如果在底部且有新消息，直接滚动到底部
						nextTick(() => {
							scrollToBottom(true);
						});
					}
				});
			}
		}
	}
);

function handleClickMessage(msg: IMMessage) {
	if (!msg.isMine && !msg.readFlag) {
		if (conv.value) {
			msg.readFlag = true;
			conv.value.mark(msg.id);
		}
	}
}

function handleEmojiClick(c: string) {
	messageText.value += c;
}

function handleBackClick() {
	const ls = [...messageText.value.normalize()];
	messageText.value = ls.slice(0, ls.length - 1).join('');
}

// #ifndef H5
function handleKeyboardHeightChange(ev: { height: number }) {
	const prevHeight = keyboardHeight.value;

	console.log('键盘高度变化:', ev.height, '之前高度:', prevHeight);

	// #ifdef APP-PLUS
	// 重新设计布局：键盘适配逻辑
	if (ev.height > 0) {
		// 虚拟键盘出现，隐藏 emoji 键盘
		closeEmojiKeyboard();

		// 设置键盘高度并重新计算布局
		keyboardHeight.value = ev.height;
		calculateMessageContainerHeight();

		console.log('键盘弹出，重新计算布局，键盘高度:', keyboardHeight.value);

		// 使用 nextTick 确保布局计算完成后滚动
		nextTick(async () => {
			await scrollToBottom(true);
		});
	} else if (prevHeight > 0 && ev.height === 0) {
		// 键盘收起时，重置高度并重新计算布局
		keyboardHeight.value = 0;
		calculateMessageContainerHeight();
		console.log('键盘收起，重新计算布局');

		nextTick(async () => {
			await scrollToBottom();
		});
	}
	// #endif
}
// #endif

// #ifdef H5
let h5ResizeHandler: (() => void) | null = null;
let lastViewportHeight = 0;

// H5环境下检测虚拟键盘
function detectKeyboardInH5() {
	const currentHeight = window.innerHeight;

	if (lastViewportHeight === 0) {
		lastViewportHeight = currentHeight;
		return;
	}

	const heightDiff = lastViewportHeight - currentHeight;

	// 高度减少超过150px认为是键盘弹出
	if (heightDiff > 150) {
		keyboardHeight.value = heightDiff;
		closeEmojiKeyboard();

		// 重新计算消息容器高度
		calculateMessageContainerHeight();

		// 使用 nextTick 确保布局计算完成后滚动
		nextTick(async () => {
			await scrollToBottom(true);
		});
	} else if (heightDiff < -50) {
		// 高度增加超过50px认为是键盘收起
		keyboardHeight.value = 0;

		// 重新计算消息容器高度
		calculateMessageContainerHeight();

		nextTick(async () => {
			await scrollToBottom();
		});
	}
}

// 处理页面可见性变化
function handleVisibilityChange() {
	if (!document.hidden && isIOS.value) {
		// 页面重新可见时，更新滚动信息
		nextTick(() => {
			updateScrollViewInfo();
			// 页面重新可见时检查新消息计数
			resetNewMessageCountIfNeeded();
		});
	}
}
// #endif

onMounted(() => {
	// 初始化iOS检测
	detectIOS();

	// 重新设计布局：初始化系统信息
	initSystemInfo();

	// 初始化滚动状态
	isNearBottomSync.value = true;
	newMessageCount.value = 0;
	lastMessageIdBeforeNewMessages.value = null;

	// 组件挂载后确保滚动到底部（参考PC端）
	nextTick(() => {
		scrollToBottom(true);
		// 初始化滚动信息
		updateScrollViewInfo();
		// 更新底部状态
		updateNearBottomState();
		// 使用 nextTick 确保滚动生效
		nextTick(() => {
			scrollToBottom(true);
			updateNearBottomState();
			// 验证逻辑现在由 watch 自动处理
		});
	});

	// #ifndef H5
	uni.onKeyboardHeightChange(handleKeyboardHeightChange);
	// #endif

	// #ifdef H5
	// H5环境下监听窗口大小变化来检测虚拟键盘
	lastViewportHeight = window.innerHeight;

	h5ResizeHandler = () => {
		// 检测键盘状态
		detectKeyboardInH5();

		// 使用 nextTick 确保键盘动画完成
		nextTick(() => {
			updateScrollViewInfo();
			updateNearBottomState();
			// 验证逻辑现在由 watch 自动处理
		});
	};
	window.addEventListener('resize', h5ResizeHandler);

	// 监听页面可见性变化
	document.addEventListener('visibilitychange', handleVisibilityChange);
	// #endif
});

onBeforeUnmount(() => {
	// 清理定时器（参考PC端）
	if (userScrollTimeout.value) {
		clearTimeout(userScrollTimeout.value);
	}

	// #ifndef H5
	uni.offKeyboardHeightChange(handleKeyboardHeightChange);
	// #endif

	// #ifdef H5
	// 移除H5环境下的resize监听器
	if (h5ResizeHandler) {
		window.removeEventListener('resize', h5ResizeHandler);
		h5ResizeHandler = null;
	}
	// 移除页面可见性监听器
	document.removeEventListener('visibilitychange', handleVisibilityChange);
	// #endif
});

onUpdated(() => {
	checkForUnread();
	// 每次DOM更新后也验证一下新消息计数
	validateAndFixNewMessageCount();
});

function handleScroll() {
	checkForUnread();

	// 标记用户正在滚动（参考PC端）
	isUserScrolling.value = true;

	// 清除之前的定时器
	if (userScrollTimeout.value) {
		clearTimeout(userScrollTimeout.value);
	}

	// 使用防抖处理滚动结束
	userScrollTimeout.value = setTimeout(() => {
		isUserScrolling.value = false;
		// 用户停止滚动后，更新是否在底部状态
		updateNearBottomState();
		// 新消息计数的处理现在由 watch 自动处理
	}, 300);

	// 更新滚动信息，用于iOS边界控制（仅在必要时）
	if (isIOS.value && (inputFocused.value || keyboardHeight.value > 0)) {
		updateScrollViewInfo();
	}
}

function handleScrollToUpper() {
	if (hasMore && messageList.value.length > 0 && conv.value) {
		if (!loadingMore) {
			// 当用户主动加载历史消息时，重置新消息计数
			// 因为此时用户肯定是在查看历史消息，不应该显示新消息提示
			newMessageCount.value = 0;
			lastMessageIdBeforeNewMessages.value = null;

			loadingMore = true;
			conv.value
				.loadMore(20, messageList.value[0].id)
				.then((d) => {
					if (d.length > 0) {
						// 检查新加载的消息中是否有图片（参考PC端）
						const imageMessages = d.filter((msg) => msg.type === 1);

						if (imageMessages.length > 0) {
							// 如果有图片，标记正在加载历史消息
							isLoadingHistory.value = true;
							lastHistoryLoadTime.value = Date.now();
							// 注意：对于历史消息，不需要增加pendingImageLoads，因为它们不影响当前视图滚动
						}

						messageList.value.splice(0, 0, ...d.reverse());

						uni
							.createSelectorQuery()
							.in(instanceProxy)
							.select('#scrollView')
							.fields({ size: true, scrollOffset: true }, (data) => {
								const n = data as UniApp.NodeInfo;
								const offsetFromBottom = n.scrollHeight! - n.scrollTop!;

								nextTick(() => {
									uni
										.createSelectorQuery()
										.in(instanceProxy)
										.select('#scrollView')
										.fields({ size: true, scrollOffset: true }, (data) => {
											loadingMore = false;
											const n = data as UniApp.NodeInfo;
											const offsetY = n.scrollHeight! - offsetFromBottom;
											scrollTop.value = offsetY;

											// 使用 watch 来监听图片加载完成，而不是固定延迟
											if (imageMessages.length > 0) {
												// 创建一个 watch 来监听图片加载状态
												const stopWatching = watch(
													() => pendingImageLoads.value,
													(newValue) => {
														if (newValue === 0) {
															isLoadingHistory.value = false;
															stopWatching(); // 停止监听
														}
													}
												);
												// 备用方案：如果5秒后仍未完成，强制重置
												setTimeout(() => {
													isLoadingHistory.value = false;
													stopWatching();
												}, 5000);
											}
										})
										.exec();
								});
							})
							.exec();
					} else {
						hasMore = false;
						loadingMore = false;
					}
				})
				.catch((err) => {
					loadingMore = false;
					console.error(err);
				});
		}
	}
}

// 检测用户是否在消息底部附近（参考PC端）
function isNearBottom(): Promise<boolean> {
	return new Promise((resolve) => {
		uni
			.createSelectorQuery()
			.in(instanceProxy)
			.select('#scrollView')
			.fields({ size: true, scrollOffset: true }, (data) => {
				const n = data as UniApp.NodeInfo;
				if (n) {
					const { scrollTop, scrollHeight, height } = n;
					const nearBottom = scrollHeight! - scrollTop! - height! <= BOTTOM_THRESHOLD;
					// 同步更新底部状态
					isNearBottomSync.value = nearBottom;
					resolve(nearBottom);
				} else {
					resolve(false);
				}
			})
			.exec();
	});
}

// 更新同步底部状态（不返回Promise）
function updateNearBottomState() {
	uni
		.createSelectorQuery()
		.in(instanceProxy)
		.select('#scrollView')
		.fields({ size: true, scrollOffset: true }, (data) => {
			const n = data as UniApp.NodeInfo;
			if (n) {
				const { scrollTop, scrollHeight, height } = n;
				isNearBottomSync.value = scrollHeight! - scrollTop! - height! <= BOTTOM_THRESHOLD;
			}
		})
		.exec();
}

// 智能滚动到底部 - 只在用户在底部时才滚动（参考PC端）
async function smartScrollToBottom() {
	const nearBottom = await isNearBottom();
	if (nearBottom && !isUserScrolling.value) {
		scrollToBottom();
		// 如果在底部，清空新消息计数
		newMessageCount.value = 0;
		lastMessageIdBeforeNewMessages.value = null;
	}
}

// 基于 Promise 的滚动函数
function scrollToBottom(force?: boolean): Promise<void> {
	// 如果正在加载历史消息，避免自动滚动到底部
	if (isLoadingHistory.value && !force) {
		return Promise.resolve();
	}

	return new Promise((resolve) => {
		uni
			.createSelectorQuery()
			.in(instanceProxy)
			.select('#scrollView')
			.fields({ size: true, scrollOffset: true }, (data) => {
				const n = data as UniApp.NodeInfo;
				if (force || Math.abs(n.height! + n.scrollTop! - n.scrollHeight!) < 2) {
					// 当前处于底部，新的消息到来后自动滚动，保持最新消息可见
					nextTick(() => {
						uni
							.createSelectorQuery()
							.in(instanceProxy)
							.select('#scrollView')
							.fields({ size: true, scrollOffset: true }, (data) => {
								const n = data as UniApp.NodeInfo;
								const offsetY = n.scrollHeight! - n.height!;
								scrollTop.value = offsetY;
								resolve();
							})
							.exec();
					});
				} else {
					resolve();
				}
			})
			.exec();
	});
}

// 图片加载完成的处理函数（参考PC端）
function handleImageLoad() {
	// 减少待加载图片计数
	if (pendingImageLoads.value > 0) {
		pendingImageLoads.value--;
	}

	// 如果正在加载历史消息，或者距离最后一次加载历史消息时间很短，不执行滚动操作
	const timeSinceLastHistoryLoad = Date.now() - lastHistoryLoadTime.value;
	if (isLoadingHistory.value || timeSinceLastHistoryLoad < 5000) {
		return;
	}

	// 检查是否需要滚动
	const recentlySent = Date.now() - lastSentTime.value < 5000;

	if (justSentMessage.value || isInitialLoading.value || recentlySent) {
		scrollToBottom();

		// 如果没有待加载的图片了，重置相关标记
		if (pendingImageLoads.value === 0) {
			if (justSentMessage.value) {
				// 使用状态管理系统重置状态
				setMessageSendState(MessageSendState.CONFIRMED);
			}
			if (isInitialLoading.value) {
				nextTick(() => {
					isInitialLoading.value = false;
					nextTick(() => {
						scrollToBottom();
					});
				});
			}
		}
	} else {
		// 使用智能滚动
		smartScrollToBottom();
	}
}

// 图片加载错误的处理函数（参考PC端）
function handleImageError() {
	// 减少待加载图片计数
	if (pendingImageLoads.value > 0) {
		pendingImageLoads.value--;
	}

	// 如果正在加载历史消息，或者距离最后一次加载历史消息时间很短，不执行滚动操作
	const timeSinceLastHistoryLoad = Date.now() - lastHistoryLoadTime.value;
	if (isLoadingHistory.value || timeSinceLastHistoryLoad < 5000) {
		return;
	}

	// 检查是否需要滚动（同图片加载完成的逻辑）
	const recentlySent = Date.now() - lastSentTime.value < 5000;

	if (justSentMessage.value || isInitialLoading.value || recentlySent) {
		scrollToBottom();

		// 如果没有待加载的图片了，重置相关标记
		if (pendingImageLoads.value === 0) {
			if (justSentMessage.value) {
				// 使用状态管理系统重置状态
				setMessageSendState(MessageSendState.CONFIRMED);
			}
			if (isInitialLoading.value) {
				nextTick(() => {
					isInitialLoading.value = false;
					nextTick(() => {
						scrollToBottom();
					});
				});
			}
		}
	} else {
		// 使用智能滚动
		smartScrollToBottom();
	}
}

// 更新scroll-view的滚动信息
function updateScrollViewInfo() {
	if (!isIOS.value) return;

	uni
		.createSelectorQuery()
		.in(instanceProxy)
		.select('#scrollView')
		.fields({ size: true, scrollOffset: true }, (data) => {
			const n = data as UniApp.NodeInfo;
			if (n) {
				scrollViewInfo.value = {
					scrollTop: n.scrollTop || 0,
					scrollHeight: n.scrollHeight || 0,
					height: n.height || 0,
				};

				// 判断是否在顶部或底部
				isScrollAtTop.value = scrollViewInfo.value.scrollTop <= 0;
				isScrollAtBottom.value = scrollViewInfo.value.scrollTop >= scrollViewInfo.value.scrollHeight - scrollViewInfo.value.height;
			}
		})
		.exec();
}

function handleContainerTouchMove(ev: any) {
	// 简化iOS滚动处理：只在必要时阻止穿透
	if (!isIOS.value) return;

	// 只在键盘弹出且输入框聚焦时才阻止穿透
	if (inputFocused.value && keyboardHeight.value > 0) {
		ev.preventDefault();
		ev.stopPropagation();
	}
}

function handleScrollViewTouchStart(ev: any) {
	if (!isIOS.value) return;

	const touch = ev.touches[0];
	touchStartY.value = touch.clientY;
	lastTouchY.value = touch.clientY;

	// 简化：只在需要时更新滚动信息
	if (inputFocused.value) {
		updateScrollViewInfo();
	}
}

function handleScrollViewTouchMove(ev: any) {
	// 重构：简化iOS滚动处理逻辑，提升性能
	if (!isIOS.value) return;

	const touch = ev.touches[0];
	const currentY = touch.clientY;
	const deltaY = currentY - lastTouchY.value;

	// 只在输入框聚焦且键盘弹出时才进行边界检查
	if (inputFocused.value && keyboardHeight.value > 0) {
		updateScrollViewInfo();

		// 简化阈值判断
		const threshold = 10;
		if ((isScrollAtTop.value && deltaY > threshold) || (isScrollAtBottom.value && deltaY < -threshold)) {
			ev.preventDefault();
			return false;
		}
	}

	lastTouchY.value = currentY;
}

function handleScrollViewTouchEnd() {
	if (!isIOS.value) return;

	// 重置触摸状态
	touchStartY.value = 0;
	lastTouchY.value = 0;
}

function handleInputFocus() {
	inputFocused.value = true;
	console.log('输入框获得焦点');

	// #ifdef APP-PLUS
	// Android端备用键盘检测
	const sysInfo = uni.getSystemInfoSync();
	if (sysInfo.platform === 'android') {
		// 使用 nextTick 和 Promise 来处理键盘检测
		nextTick(async () => {
			// 等待一个短暂的时间让键盘有机会弹出
			await createDelay(500);
			if (keyboardHeight.value === 0) {
				// 假设键盘高度为屏幕高度的40%
				const estimatedKeyboardHeight = sysInfo.screenHeight * 0.4;
				console.log('使用估算键盘高度:', estimatedKeyboardHeight);
				keyboardHeight.value = estimatedKeyboardHeight;
				calculateMessageContainerHeight();
				await scrollToBottom(true);
			}
		});
	}
	// #endif

	// iOS键盘弹出时的处理
	if (isIOS.value) {
		// 使用 Promise 来处理键盘动画等待
		(async () => {
			// 等待键盘动画完成
			await createDelay(300);
			updateScrollViewInfo();
			await scrollToBottom(true);
		})();
	}
}

function handleInputBlur() {
	inputFocused.value = false;
	console.log('输入框失去焦点');

	// #ifdef APP-PLUS
	// Android端：强制重置键盘高度
	const sysInfo = uni.getSystemInfoSync();
	if (sysInfo.platform === 'android') {
		// 使用 Promise 来处理键盘收起
		(async () => {
			await createDelay(200);
			if (keyboardHeight.value > 0) {
				console.log('强制重置键盘高度');
				keyboardHeight.value = 0;
				calculateMessageContainerHeight();
				await scrollToBottom();
			}
		})();
	}
	// #endif

	// iOS键盘收起时的处理
	if (isIOS.value) {
		// 使用 Promise 来处理键盘收起
		(async () => {
			// 等待键盘完全收起
			await createDelay(100);
			updateScrollViewInfo();

			// #ifdef H5
			// 修复iOS H5键盘收起后页面不回落的问题
			const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
			await nextTick(() => {
				document.body.scrollTop = document.documentElement.scrollTop = scrollTop;
			});
			// #endif
		})();
	}
}

function handleSendClick(event: Event) {
	event.preventDefault();
	send();
}

// 点击新消息提示时滚动到底部
async function handleNewMessageTipClick() {
	// 在滚动前验证新消息计数的正确性
	validateAndFixNewMessageCount();

	// 如果验证后依然有新消息计数，则滚动到底部
	if (newMessageCount.value > 0) {
		// 滚动到底部并等待完成
		await scrollToBottom(true);
		// 滚动完成后清空新消息计数
		newMessageCount.value = 0;
		lastMessageIdBeforeNewMessages.value = null;
	} else {
		// 如果已经没有新消息计数，直接隐藏提示
		newMessageCount.value = 0;
		lastMessageIdBeforeNewMessages.value = null;
	}
}
</script>

<style scoped lang="scss">
$bg-their: #ffffff;
$bg-mine: #d2e7f9;

/* 聊天容器布局 */
.chat-container {
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.message-container {
	/* 重新设计布局：使用固定高度而非flex */
	position: relative;
	overflow: hidden;
	width: 100%;
	/* 重构滚动容器优化 */
	overscroll-behavior: none;
	/* 优化触摸操作 */
	touch-action: manipulation;
	/* 防止选择文本和长按菜单 */
	-webkit-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
	/* 性能优化 */
	will-change: height;
	contain: layout style;
	/* 确保平滑过渡 */
	transition: height 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 新消息提示组件样式 */
.new-message-tip {
	position: absolute;
	right: 16rpx;
	bottom: 24rpx;
	z-index: 10;
	border-radius: 36rpx;
	background-color: #ff5000;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	transform-origin: center bottom;
	cursor: pointer;
	/* 优化动画性能 */
	will-change: transform, opacity;
	backface-visibility: hidden;
	/* 进入动画 */
	animation: slideInBounce 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	/* 退出动画通过Vue的transition处理 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 鼠标悬停效果（H5平台） */
.new-message-tip:hover {
	transform: scale(1.05);
	box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
}

/* 点击效果 */
.new-message-tip:active {
	transform: scale(0.95);
}

.tip-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 12rpx 24rpx;
	gap: 8rpx;
}

.tip-content text {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 500;
}

/* 优化的进入动画 - 从右下角滑入并带有弹性效果 */
@keyframes slideInBounce {
	0% {
		transform: translateX(100%) translateY(50%) scale(0.3);
		opacity: 0;
	}
	50% {
		transform: translateX(-10%) translateY(0%) scale(1.1);
		opacity: 0.8;
	}
	70% {
		transform: translateX(5%) translateY(0%) scale(0.95);
		opacity: 1;
	}
	100% {
		transform: translateX(0%) translateY(0%) scale(1);
		opacity: 1;
	}
}

/* 退出动画 - 淡出并缩小 */
@keyframes slideOutFade {
	0% {
		transform: translateX(0%) translateY(0%) scale(1);
		opacity: 1;
	}
	100% {
		transform: translateX(50%) translateY(20%) scale(0.8);
		opacity: 0;
	}
}

.bottom-bar-container {
	/* 重新设计布局：动态高度的底部容器 */
	position: relative;
	z-index: 10;
	width: 100%;
	flex-shrink: 0;
	/* 跨平台优化 */
	overscroll-behavior: none;
	touch-action: manipulation;
	box-sizing: border-box;
	padding-bottom: env(safe-area-inset-bottom, 0px);
	/* 性能优化 */
	will-change: auto;
	backface-visibility: hidden;
	/* 样式 */
	background-color: #f7f7f7;
	border-top: 1px solid #e5e5e5;
}

/* 失效聊天提示样式 */
.invalid-chat-notice {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 24rpx;
	background-color: #fff8e6;
	border-radius: 8rpx;
	margin: 16rpx 24rpx;
	border: 1px dashed #f3cc62;
}

.invalid-chat-notice text {
	margin-left: 16rpx;
	font-size: 28rpx;
	color: #8c6c3e;
}

/* 键盘占位区域样式 */
.keyboard-placeholder {
	width: 100%;
	background-color: transparent;
	flex-shrink: 0;
}

:deep() {
	.diw-bottom-bar {
		background-color: #f7f7f7;
		border-top: 1px solid #e5e5e5;
		&__container {
			padding: 12rpx 24rpx;
			display: flex;
			flex-direction: column;
		}
	}
}

.chat-input-container {
	display: flex;
	flex-direction: row;
	align-items: flex-end;
	gap: 16rpx;
	width: 100%;
}

.input-wrapper {
	flex: 1;
	background-color: #ffffff;
	border-radius: 40rpx;
	padding: 16rpx 24rpx;
	border: 1px solid #e5e5e5;
	max-height: 200rpx;
	overflow-y: auto;
}

.message-input {
	width: 100%;
	font-size: 32rpx;
	line-height: 1.4;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
	resize: none;
	min-height: 44rpx;
}

.action-buttons {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	height: 80rpx;
	gap: 12rpx;
}

:deep(.emoji-btn) {
	background-color: #ffffff !important;
	border: 1px solid #e5e5e5 !important;
	border-radius: 10rpx !important;
	width: 80rpx !important;
	height: 80rpx !important;
	font-size: 40rpx !important;
	color: #666 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

:deep(.send-btn) {
	background-color: #ff5000 !important;
	border: none !important;
	border-radius: 10rpx !important;
	height: 72rpx !important;
	font-size: 24rpx !important;
	color: #ffffff !important;
}

.message-area {
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
	background-color: #f5f6f9;
	font-size: 32rpx;
	/* 重构滚动性能优化 */
	-webkit-overflow-scrolling: touch;
	/* 防止iOS橡皮筋效果和滚动穿透 */
	overscroll-behavior: none;
	/* 优化触摸响应 */
	touch-action: pan-y;
	/* 硬件加速优化 */
	will-change: scroll-position;
	transform: translateZ(0);
	/* 内容渲染优化 */
	contain: layout style paint;

	.time-divider {
		text-align: center;
		margin: 32rpx 0 16rpx 0;
	}

	.time-text {
		font-size: 24rpx;
		color: #999;
		background-color: rgba(255, 255, 255, 0.8);
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		display: inline-block;
	}

	.message-item {
		padding: 16rpx 24rpx;

		&.mine {
			.message-line {
				justify-content: flex-end;

				.read-status {
					order: 1;
				}

				.message-frame {
					order: 2;
				}

				.avatar {
					order: 3;
				}
			}
		}

		&:not(.mine) {
			.message-line {
				justify-content: flex-start;

				.avatar {
					order: 1;
				}

				.message-frame {
					order: 2;
				}
			}
		}

		.message-line {
			width: 100%;
			display: flex;
			flex-direction: row;
			gap: 16rpx;

			.avatar {
				display: flex;
				align-items: flex-start;
				justify-content: center;
				border-radius: 12rpx;
				width: 80rpx;
				height: 80rpx;
				background-color: #f0f0f0;
				overflow: hidden;
				flex-shrink: 0;
			}

			.avatar-image {
				width: 100%;
				height: 100%;
			}

			.avatar-placeholder {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #409eff;
			}

			.avatar-text {
				color: white;
				font-size: 28rpx;
				font-weight: bold;
			}

			.message-frame {
				position: relative;
				display: flex;
				flex-direction: column;
				max-width: calc(100% - 120rpx);
				border-radius: 28rpx;
				padding: 20rpx 24rpx;
				text-align: left;
				font-size: 32rpx;
				line-height: 1.4;
				word-wrap: break-word;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

				&::after {
					content: '';
					position: absolute;
					display: block;
					width: 0;
					height: 0;
					border: 16rpx solid transparent;
					top: 32rpx;
				}
			}

			.message-frame.their {
				background-color: $bg-their;
				&::after {
					left: -32rpx;
					border-right-color: $bg-their;
				}
			}

			.message-frame.mine {
				background-color: $bg-mine;
				&::after {
					right: -32rpx;
					border-left-color: $bg-mine;
				}
			}
		}

		.read-status {
			display: flex;
			align-items: flex-end;
			padding-bottom: 8rpx;
		}

		.read-status-text {
			font-size: 24rpx;
			color: #999;
			white-space: nowrap;
			align-self: flex-end;
			margin-right: 16rpx;
			margin-bottom: 8rpx;
		}
	}
}
</style>
