@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(picker) {
		@include e(action) {
			@include m(cancel) {
				color: $wot-dark-color;
			}
			@include when(loading) {
				color: $wot-dark-color3;
			}
		}

		:deep(.wd-picker__arrow),
		:deep(.wd-picker__clear) {
			color: $wot-dark-color;
		}

		:deep(.wd-picker__cell--placeholder) {
			.wd-cell__value {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(picker) {
	@include edeep(popup) {
		border-radius: 16px 16px 0px 0px;
	}

	@include edeep(cell) {
		@include when(disabled) {
			.wd-cell__value {
				color: $wot-input-disabled-color;
				cursor: not-allowed;
			}
		}
		@include when(error) {
			.wd-cell__value {
				color: $wot-input-error-color;
			}
			.wd-picker__arrow {
				color: $wot-input-error-color;
			}
		}
		@include when(large) {
			.wd-picker__arrow,
			.wd-picker__clear {
				font-size: $wot-cell-icon-size-large;
			}
		}
		@include m(placeholder) {
			.wd-cell__value {
				color: $wot-input-placeholder-color;
			}
		}
	}

	@include edeep(arrow, clear) {
		display: block;
		font-size: $wot-cell-icon-size;
		color: $wot-cell-arrow-color;
		line-height: $wot-cell-line-height;
	}

	@include edeep(clear) {
		color: $wot-cell-clear-color;
	}

	@include e(wraper) {
		padding-bottom: var(--window-bottom);
	}

	@include e(toolbar) {
		position: relative;
		display: flex;
		font-size: $wot-picker-toolbar-fs;
		height: $wot-picker-toolbar-height;
		line-height: $wot-picker-action-height;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
	}

	@include e(action) {
		display: block;
		border: none;
		outline: none;
		font-size: $wot-picker-toolbar-fs;
		color: $wot-picker-toolbar-finish-color;
		background: transparent;
		padding: 24px 15px 14px 15px;

		@include m(cancel) {
			color: $wot-picker-toolbar-cancel-color;
		}

		@include when(loading) {
			color: $wot-picker-loading-button-color;
		}
	}

	@include e(title) {
		display: block;
		float: 1;
		color: $wot-picker-toolbar-title-color;
	}
}
