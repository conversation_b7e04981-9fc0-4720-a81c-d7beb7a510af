<template>
	<picker v-if="props.mode === 'selector'" :range="selectorRange" range-key="label" :value="selectorIndex" @change="handleSelectorChange">
		<slot :displayText="displayText" />
	</picker>
	<picker
		v-else-if="props.mode === 'area'"
		mode="multiSelector"
		:range="areaRange"
		range-key="name"
		:value="areaIndex"
		@columnchange="handleAreaColumnChange"
		@change="handleAreaChange"
	>
		<slot :displayText="displayText" />
	</picker>
</template>

<script setup lang="ts">
const { getAreaList } = useFramework();

const props = withDefaults(
	defineProps<{
		mode?: 'selector' | 'area';
		options?: Array<Record<string, any>>;
		labelProp?: string;
		valueProp?: string;
	}>(),
	{ mode: 'selector', labelProp: 'label', valueProp: 'value' }
);

const emit = defineEmits<{
	(e: 'change', value: string | number | Array<string | number> | undefined): void;
}>();

const model = defineModel<string | number | Array<string | number>>();

const selectorIndex = ref(-1);

const selectorRange = computed(() => {
	if (Array.isArray(props.options)) {
		return props.options.map((item) => {
			return { label: item[props.labelProp], value: item[props.valueProp] };
		});
	}

	return [];
});

const areaIndex = ref([0, 0, 0]);
const areaRange = ref<Array<Array<Record<string, any>>>>([[], [], []]);

const displayText = ref('');

let syncInProgress = false;
let pendingSync = false;
let pendingUpdateDisplay = false;
let pendingValue: any = undefined;

watchEffect(() => {
	if (props.mode === 'selector') {
		displayText.value = '';
		selectorIndex.value = 0;
		for (let i = 0; i < selectorRange.value.length; i++) {
			const item = selectorRange.value[i];
			if (item.value === model.value) {
				selectorIndex.value = i;
				displayText.value = item.label;
				break;
			}
		}
	} else if (props.mode === 'area') {
		syncAreaModel(model.value, true);
	}
});

function computeDisplayText() {
	let code0 = getAreaSafe(0, areaIndex.value);
	let code1 = getAreaSafe(1, areaIndex.value);
	let code2 = getAreaSafe(2, areaIndex.value);

	if (code0 && code1) {
		if (code2) {
			return `${code0.name}${code1.name}${code2.name}`;
		} else {
			if (code1.areaType == '2') {
				return `${code0.name}${code1.name}`;
			} else if (code1.areaType == '3') {
				return `${code0.name}${code1.name}`;
			}
		}
	}
	return '';
}

function updateDisplayText() {
	displayText.value = computeDisplayText();
}

function handleSelectorChange(ev: { detail: { value: number } }) {
	selectorIndex.value = ev.detail.value;
	const item = selectorRange.value[selectorIndex.value];
	model.value = item.value;
	displayText.value = item.label;
	emit('change', item.value);
}

function handleAreaColumnChange(ev: { detail: { column: number; value: number } }) {
	if (ev.detail.column === 0) {
		const d0 = areaRange.value[0][ev.detail.value];
		if (d0) {
			syncAreaModel([d0.adcode, '', ''], false);
		} else {
			syncAreaModel(undefined, false);
		}
	} else if (ev.detail.column === 1) {
		syncAreaModel([getAreaCodeSafe(0, areaIndex.value[0]), getAreaCodeSafe(1, ev.detail.value), ''], false);
	} else if (ev.detail.column === 2) {
		areaIndex.value[2] = ev.detail.value;
	}
}

function handleAreaChange(ev: { detail: { value: number[] } }) {
	let code0 = getAreaSafe(0, ev.detail.value);
	let code1 = getAreaSafe(1, ev.detail.value);
	let code2 = getAreaSafe(2, ev.detail.value);
	if (code0 && code1) {
		if (code2) {
			model.value = [code0.adcode, code1.adcode, code2.adcode];
		} else {
			if (code1.areaType == '2') {
				model.value = [code0.adcode, code1.adcode, ''];
			} else if (code1.areaType == '3') {
				model.value = [code0.adcode, code0.adcode, code1.adcode];
			}
		}
	}

	updateDisplayText();
	emit('change', model.value);
}

function getAreaCodeSafe(rangeIndex: number, index: any) {
	if (rangeIndex >= 0 && rangeIndex < areaRange.value.length) {
		const r = areaRange.value[rangeIndex];
		if (index >= 0 && index < r.length) {
			const k = r[index];
			if (k && typeof k === 'object') {
				return k.adcode;
			}
		}
	}

	return '';
}

function getAreaSafe(index: number, values: number[]) {
	if (index >= 0 && index < values.length && index < areaRange.value.length) {
		const k = areaRange.value[index][values[index]];
		if (k && typeof k === 'object') {
			return k;
		}
	}
	return null;
}

async function syncAreaModel(value: any, updateDisplay: boolean) {
	if (!syncInProgress) {
		syncInProgress = true;
		try {
			await syncAreaModelImpl(value, updateDisplay);
		} catch (err) {
			console.error(err);
		} finally {
			syncInProgress = false;
			if (pendingSync) {
				pendingSync = false;
				syncAreaModel(pendingValue, pendingUpdateDisplay);
			}
		}
	} else {
		pendingSync = true;
		pendingUpdateDisplay = updateDisplay;
		pendingValue = value;
	}
}

async function syncAreaModelImpl(value: any, updateDisplay: boolean) {
	const d0 = await getAreaList();
	if (Array.isArray(value)) {
		let code0 = value[0];
		const index0 = d0.findIndex((e) => e.adcode === code0);
		if (index0 >= 0) {
			code0 = d0[index0].adcode;
			const d1 = await getAreaList(code0);
			let code1 = value[1];
			const index1 = d1.findIndex((e) => e.adcode === code1);
			if (index1 >= 0) {
				code1 = d1[index1].adcode;
				const d2 = d1[index1].areaType == '2' ? (await getAreaList(code1)).filter((e) => e.areaType == '3') : [];
				let code2 = value[2];
				const index2 = d2.findIndex((e) => e.adcode === code2);
				if (index2 >= 0) {
					areaRange.value = [d0, d1, d2];
					areaIndex.value = [index0, index1, index2];
				} else {
					areaRange.value = [d0, d1, d2];
					areaIndex.value = [index0, index1, 0];
				}
			} else {
				if (code0 === code1) {
					let code2 = value[2];
					const index2 = d1.findIndex((e) => e.adcode === code2);
					if (index2 >= 0) {
						areaRange.value = [d0, d1, []];
						areaIndex.value = [index0, index2, 0];
					} else {
						areaRange.value = [d0, d1, []];
						areaIndex.value = [index0, 0, 0];
					}
				} else {
					areaRange.value = [d0, d1, []];
					areaIndex.value = [index0, 0, 0];
				}
			}
		} else {
			areaRange.value = [d0, [], []];
			areaIndex.value = [0, 0, 0];
		}
	} else {
		updateDisplay = false;
		areaRange.value = [d0, [], []];
		areaIndex.value = [0, 0, 0];
		if (d0.length > 0) {
			const index0 = 0;
			const code0 = d0[index0].adcode;
			const d1 = await getAreaList(code0);
			if (d1.length > 0) {
				let code1 = d1[0].adcode;
				const index1 = d1.findIndex((e) => e.adcode === code1);
				if (index1 >= 0) {
					code1 = d1[index1].adcode;
					const d2 = d1[index1].areaType == '2' ? (await getAreaList(code1)).filter((e) => e.areaType == '3') : [];
					if (d2.length > 0) {
					} else {
						areaRange.value = [d0, d1, []];
						areaIndex.value = [0, 0, 0];
					}
				}
			}
		}
	}

	if (updateDisplay) {
		updateDisplayText();
	}
}
</script>
