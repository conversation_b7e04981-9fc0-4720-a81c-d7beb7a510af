<template>
	<view class="diw-bottom-bar" :class="customClass" :style="customStyle">
		<view class="diw-bottom-bar__container">
			<slot />
		</view>
		<wd-gap v-if="props.safeArea" safe-area-bottom height="0" />
	</view>
</template>

<script setup lang="ts">
const props = withDefaults(
	defineProps<{
		// 根结点 class
		customClass?: DIWClassType;

		// 根结点 style
		customStyle?: DIWStyleType;

		safeArea?: boolean;
	}>(),
	{ safeArea: true }
);

const customClass = computed(() => parseClass(props.customClass));
const customStyle = computed(() => parseStyle(props.customStyle));
</script>

<style lang="scss">
.diw-bottom-bar {
	background-color: white;
	box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.06);
	z-index: 1;

	&__container {
		padding: 24rpx 32rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}
}
</style>
