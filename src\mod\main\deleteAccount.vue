<template>
	<view class="p-4">
		<view>
			<text class="text-xl font-bold">重要提示</text>
		</view>
		<view class="mt-2">
			<text class="text-xl"> 您即将注销{{ appName }}账号，注销后您将无法使用该账号进行任何操作，并且您的个人信息将被永久删除，无法恢复。 </text>
		</view>
		<view class="flex flex-row mt-8 items-center justify-between p-4">
			<wd-button type="success" @click="goBack()">不注销账号</wd-button>
			<wd-button type="error" @click="confirmAndDelete">注销账号</wd-button>
		</view>
	</view>
</template>

<script setup lang="ts">
const { protectOp, apiGet, logout } = useFramework();

const appName = uni.getAppBaseInfo().appName;

function goBack() {
	uni.navigateBack();
}

function confirmAndDelete() {
	protectOp(
		'确定要注销账号吗？',
		async () => {
			await apiGet('admin/user/cancelUser');
			await logout();
			await uni.reLaunch({ url: '/mod/mall/home' });
		},
		null
	);
}
</script>
