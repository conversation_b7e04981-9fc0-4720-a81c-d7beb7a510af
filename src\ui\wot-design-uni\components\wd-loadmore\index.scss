@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;
.wot-theme-dark {
	@include b(loadmore) {
		color: $wot-dark-color;
	}
}

@include b(loadmore) {
	width: 100%;
	height: $wot-loadmore-height;
	line-height: $wot-loadmore-height;
	text-align: center;
	color: $wot-loadmore-color;

	@include edeep(loading) {
		display: inline-block;
		margin-right: 8px;
		vertical-align: middle;
		width: $wot-loadmore-loading-size;
		height: $wot-loadmore-loading-size;
	}
	@include e(text) {
		display: inline-block;
		font-size: $wot-loadmore-fs;
		vertical-align: middle;

		@include when(light) {
			margin: 0 6px;
			color: $wot-loadmore-error-color;
		}
	}
	@include edeep(refresh) {
		display: inline-block;
		color: $wot-loadmore-error-color;
		vertical-align: middle;
		font-size: $wot-loadmore-refresh-fs;
	}
}
