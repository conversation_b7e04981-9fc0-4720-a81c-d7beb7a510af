<template>
	<DIWAppPage>
		<DIWProtect>
			<wd-form ref="form" :model="model">
				<wd-gap />

				<wd-cell-group border>
					<wd-input
						label="密码"
						prop="password"
						v-model="model.password"
						show-password
						required
						:maxlength="32"
						:rules="[{ required: true, message: '密码不能为空', validator: validatePassword }]"
					/>
					<wd-input
						label="确认密码"
						prop="password2"
						v-model="model.password2"
						show-password
						required
						:maxlength="32"
						:rules="[{ required: true, message: '密码不能为空', validator: validatePassword }]"
					/>
				</wd-cell-group>

				<wd-gap />

				<view class="px-4 text-right">
					<wd-button :loading="saving" @click="save">保存</wd-button>
				</view>
			</wd-form>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { sessionInfo, protect, protectOp, validateForm, logout, apiPut, encryptPassword } = useFramework();

const form = ref();

const model = ref({ password: '', password2: '' });

const saving = ref(false);

function validatePassword(val: any) {
	if (typeof val === 'string') {
		if (!val.trim()) {
			return Promise.reject('密码不能为空');
		}

		if (model.value.password !== model.value.password2) {
			return Promise.reject('两次输入的密码不相同');
		}

		return Promise.resolve();
	}

	return Promise.reject('密码不能为空');
}

function save() {
	protect(async () => {
		await validateForm(form);

		protectOp(async () => {
			try {
				saving.value = true;
				await apiPut({
					url: 'admin/user/updateCurrentUserInfo',
					data: { username: sessionInfo.value!.username, password: encryptPassword(model.value.password) },
				});
				await logout();
				await uni.reLaunch({ url: '/mod/mall/home' });
			} finally {
				saving.value = false;
			}
		});
	});
}
</script>
