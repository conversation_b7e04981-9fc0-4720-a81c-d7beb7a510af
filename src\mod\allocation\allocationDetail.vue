<template>
  <DIWAppPage mode="1" title="调拨单详情">
    <DIWScrollView>
      <view v-if="loading" class="loading-container">
        <wd-loading color="#4169e1" size="36px" />
        <text class="loading-text">加载中...</text>
      </view>
      
      <template v-else>
        <!-- 状态卡片 -->
        <view class="status-card">
          <view class="status-card__icon" :class="getStatusClass(info.status)">
            <text class="status-card__icon-text">{{ getStatusIcon(info.status) }}</text>
          </view>
          <view class="status-card__content">
            <text class="status-card__title">{{ info.statusStr }}</text>
            <text class="status-card__desc">{{ getStatusDesc(info.status) }}</text>
          </view>
        </view>
        
        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="detail-card__header">
            <text class="detail-card__title">基本信息</text>
          </view>
          <view class="detail-card__body">
            <view class="detail-item">
              <text class="detail-item__label">调拨单号</text>
              <text class="detail-item__value">{{ info.allocationNo }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-item__label">调拨类型</text>
              <text class="detail-item__value">{{ info.allocationTypeStr }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-item__label">调拨名称</text>
              <text class="detail-item__value">{{ info.allocationName }}</text>
            </view>
            <view class="detail-item" v-if="info.sellerName">
              <text class="detail-item__label">商家名称</text>
              <text class="detail-item__value">{{ info.sellerName }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-item__label">创建时间</text>
              <text class="detail-item__value">{{ info.createTime }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-item__label">创建人</text>
              <text class="detail-item__value">{{ info.createByName }}</text>
            </view>
          </view>
        </view>
        
        <!-- 仓库信息卡片 -->
        <view class="detail-card">
          <view class="detail-card__header">
            <text class="detail-card__title">仓库信息</text>
          </view>
          <view class="detail-card__body">
            <view class="warehouse-flow">
              <view class="warehouse-item">
                <view class="warehouse-item__header">
                  <text class="warehouse-item__icon">📤</text>
                  <text class="warehouse-item__title">出库仓库</text>
                </view>
                <text class="warehouse-item__name">{{ info.outWarehouseName }}</text>
              </view>
              
              <view class="warehouse-arrow">
                <text class="warehouse-arrow__icon">→</text>
              </view>
              
              <view class="warehouse-item">
                <view class="warehouse-item__header">
                  <text class="warehouse-item__icon">📥</text>
                  <text class="warehouse-item__title">入库仓库</text>
                </view>
                <text class="warehouse-item__name">{{ info.inWarehouseName }}</text>
              </view>
            </view>
            
            <view class="detail-item">
              <text class="detail-item__label">出库时间</text>
              <text class="detail-item__value">{{ info.outAllocationTime }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-item__label">到达时间</text>
              <text class="detail-item__value">{{ info.arrivalAllocationTime }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-item__label">配送方式</text>
              <text class="detail-item__value">{{ info.deliveryMethodStr }}</text>
            </view>
          </view>
        </view>
        
        <!-- 商品信息卡片 -->
        <view class="detail-card">
          <view class="detail-card__header">
            <text class="detail-card__title">商品信息</text>
          </view>
          <view class="detail-card__body">
            <view v-for="(item, index) in info.details" :key="index" class="product-item">
              <view class="product-item__header">
                <text class="product-item__name">{{ item.productName }}</text>
                <text class="product-item__code">编码: {{ item.productNo }}</text>
              </view>
              <view class="product-item__body">
                <view class="product-item__info">
                  <text class="product-item__label">品牌</text>
                  <text class="product-item__value">{{ item.brandName }}</text>
                </view>
                <view class="product-item__info">
                  <text class="product-item__label">工厂</text>
                  <text class="product-item__value">{{ item.factoryName }}</text>
                </view>
                <view class="product-item__info">
                  <text class="product-item__label">数量</text>
                  <text class="product-item__value">{{ item.allocationNum }} {{ item.productUnitName }}</text>
                </view>
                <view class="product-item__info">
                  <text class="product-item__label">批次号</text>
                  <text class="product-item__value">{{ item.batchNo }}</text>
                </view>
              </view>
            </view>
            
            <view class="total-section">
              <text class="total-section__label">总数量</text>
              <text class="total-section__value">{{ info.quantity }} 吨</text>
            </view>
          </view>
        </view>
        
        <!-- 物流信息卡片 -->
        <view class="detail-card" v-if="info.details && info.details.length > 0 && info.details[0].driverName">
          <view class="detail-card__header">
            <text class="detail-card__title">物流信息</text>
          </view>
          <view class="detail-card__body">
            <view v-for="(item, index) in info.details" :key="index" class="logistics-item">
              <view class="logistics-item__header">
                <text class="logistics-item__number">物流单号: {{ item.logisticsNo }}</text>
              </view>
              <view class="logistics-item__body">
                <view class="logistics-item__info">
                  <text class="logistics-item__label">司机姓名</text>
                  <text class="logistics-item__value">{{ item.driverName }}</text>
                </view>
                <view class="logistics-item__info">
                  <text class="logistics-item__label">司机电话</text>
                  <text class="logistics-item__value">{{ item.phone }}</text>
                </view>
                <view class="logistics-item__info">
                  <text class="logistics-item__label">身份证号</text>
                  <text class="logistics-item__value">{{ item.driverIdCard }}</text>
                </view>
                <view class="logistics-item__info">
                  <text class="logistics-item__label">车牌号</text>
                  <text class="logistics-item__value">{{ item.licenseNumber }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 备注信息卡片 -->
        <view class="detail-card" v-if="info.remark">
          <view class="detail-card__header">
            <text class="detail-card__title">备注信息</text>
          </view>
          <view class="detail-card__body">
            <text class="remark-text">{{ info.remark }}</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="action-buttons" v-if="info.status === 1 || info.status === 2 || info.status === 3">
          <wd-button type="primary" size="large" @click="handleLogistics">维护物流信息</wd-button>
        </view>
      </template>
    </DIWScrollView>
  </DIWAppPage>
</template>

<script setup lang="ts">

const { apiGet, navigateTo, protect, usePageQuery } = useFramework();

const pq = usePageQuery();
const loading = ref(true);
const info = ref<Record<string, any>>({});

onMounted(() => {
  loadData();
});

// 加载数据
async function loadData() {
  if (!pq.value || !pq.value.id) {
    uni.showToast({ title: '参数错误', icon: 'none' });
    return;
  }
  
  try {
    loading.value = true;
    const res = await apiGet({url: `stock/warehouseAllocation/${pq.value.id}`});
	console.log('获取详情数据:', res);
    if (res) {
      info.value = res;
    } else {
      uni.showToast({ title: '数据格式错误', icon: 'none' });
    }
  } catch (error) {
    console.error('加载调拨单详情失败', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
  }
}

// 获取状态样式类
function getStatusClass(status: number) {
  const statusMap: Record<number, string> = {
    1: 'status-blue',
    2: 'status-orange',
    3: 'status-yellow',
    4: 'status-green',
    5: 'status-gray',
  };
  
  return statusMap[status] || 'status-gray';
}

// 获取状态图标
function getStatusIcon(status: number) {
  const iconMap: Record<number, string> = {
    1: '📝',
    2: '🚚',
    3: '⏳',
    4: '✅',
    5: '❌',
  };
  
  return iconMap[status] || '📋';
}

// 获取状态描述
function getStatusDesc(status: number) {
  const descMap: Record<number, string> = {
    1: '调拨单已创建，等待处理',
    2: '调拨单正在运输中',
    3: '调拨单等待入库确认',
    4: '调拨单已完成',
    5: '调拨单已取消',
  };
  
  return descMap[status] || '状态未知';
}

// 维护物流信息
function handleLogistics() {
  protect(async () => {
    await navigateTo({
      url: `/mod/allocation/logisticsInfo?id=${pq.value.id}`,
    });
  });
}
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f7fa;
}

.loading-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #86909c;
}

/* 状态卡片样式 */
.status-card {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  
  &__icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    
    &-text {
      font-size: 40rpx;
    }
  }
  
  &__content {
    flex: 1;
  }
  
  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 8rpx;
    display: block;
  }
  
  &__desc {
    font-size: 26rpx;
    color: #86909c;
    display: block;
  }
}

/* 详情卡片样式 */
.detail-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  &__header {
    padding: 24rpx 32rpx;
    border-bottom: 2rpx solid #e5e6eb;
  }
  
  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1d2129;
  }
  
  &__body {
    padding: 24rpx 32rpx;
  }
}

/* 详情项样式 */
.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f2f3f5;
  
  &:last-child {
    border-bottom: none;
  }
  
  &__label {
    font-size: 28rpx;
    color: #86909c;
    width: 180rpx;
  }
  
  &__value {
    font-size: 28rpx;
    color: #1d2129;
    flex: 1;
    text-align: right;
    word-break: break-all;
  }
}

/* 仓库流程样式 */
.warehouse-flow {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f2f3f5;
}

.warehouse-item {
  flex: 1;
  
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
  }
  
  &__icon {
    font-size: 32rpx;
    margin-right: 8rpx;
  }
  
  &__title {
    font-size: 28rpx;
    color: #86909c;
  }
  
  &__name {
    font-size: 30rpx;
    color: #1d2129;
    font-weight: 500;
    margin-bottom: 8rpx;
    display: block;
  }
}

.warehouse-arrow {
  width: 80rpx;
  display: flex;
  justify-content: center;
  
  &__icon {
    font-size: 32rpx;
    color: #2b5aed;
    font-weight: bold;
  }
}

/* 商品项样式 */
.product-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f2f3f5;
  
  &:last-child {
    border-bottom: none;
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }
  
  &__name {
    font-size: 30rpx;
    color: #1d2129;
    font-weight: 500;
  }
  
  &__code {
    font-size: 24rpx;
    color: #86909c;
  }
  
  &__body {
    display: flex;
    flex-wrap: wrap;
  }
  
  &__info {
    width: 50%;
    margin-bottom: 12rpx;
  }
  
  &__label {
    font-size: 26rpx;
    color: #86909c;
    margin-right: 16rpx;
  }
  
  &__value {
    font-size: 26rpx;
    color: #1d2129;
  }
}

/* 总计部分样式 */
.total-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 24rpx;
  border-top: 2rpx solid #f2f3f5;
  margin-top: 16rpx;
  
  &__label {
    font-size: 28rpx;
    color: #86909c;
    margin-right: 16rpx;
  }
  
  &__value {
    font-size: 32rpx;
    color: #1d2129;
    font-weight: 600;
  }
}

/* 物流项样式 */
.logistics-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f2f3f5;
  
  &:last-child {
    border-bottom: none;
  }
  
  &__header {
    margin-bottom: 16rpx;
  }
  
  &__number {
    font-size: 28rpx;
    color: #2b5aed;
    font-weight: 500;
  }
  
  &__body {
    display: flex;
    flex-wrap: wrap;
  }
  
  &__info {
    width: 50%;
    margin-bottom: 12rpx;
  }
  
  &__label {
    font-size: 26rpx;
    color: #86909c;
    margin-right: 16rpx;
  }
  
  &__value {
    font-size: 26rpx;
    color: #1d2129;
  }
}

/* 备注文本样式 */
.remark-text {
  font-size: 28rpx;
  color: #1d2129;
  line-height: 1.6;
}

/* 操作按钮样式 */
.action-buttons {
  padding: 32rpx;
  margin-bottom: 32rpx;
}

/* 状态颜色 */
.status-blue {
  background-color: #eef2ff;
  color: #2b5aed;
}

.status-orange {
  background-color: #fff8e6;
  color: #ff7d00;
}

.status-yellow {
  background-color: #fffbe6;
  color: #faad14;
}

.status-green {
  background-color: #e8ffea;
  color: #00b42a;
}

.status-gray {
  background-color: #f2f3f5;
  color: #86909c;
}
</style>