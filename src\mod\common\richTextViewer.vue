<template>
	<DIWAppPage mode="1" :title="title">
		<DIWScrollView>
			<DIWRichTextEditor readonly :model-value="content" />
		</DIWScrollView>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { usePageInitData } = useFramework();

const initData = usePageInitData();

const content = shallowRef<Array<Record<string, any>>>([]);

const title = computed(() => {
	if (initData.value) {
		if (initData.value.title) {
			return initData.value.title;
		}
	}

	return '';
});

watchEffect(() => {
	if (initData.value) {
		if (Array.isArray(initData.value.content)) {
			content.value = initData.value.content;
		}
	}
});
</script>
