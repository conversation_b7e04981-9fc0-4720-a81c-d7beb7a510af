import { useSystemStore } from './system';
import { detectFileTypeFromFileName } from '@/util/file';
import { appconfig } from '@/config/appconfig';

export const useUpload = defineStore('upload', () => {
	const { apiUpload } = useSystemStore();

	function mapFileIdToUrl(fileId: string) {
		const backOrigin = appconfig.origin;
		return `${backOrigin}/api/admin/sys-file/fileRedirectUrl?id=` + encodeURIComponent(fileId);
	}

	function parseImageFiles(value: any) {
		const items = [];

		if (typeof value === 'string') {
			for (const e of value.split(',')) {
				const fileId = e.trim();
				if (fileId) {
					items.push({ type: 2, fileId, url: mapFileIdToUrl(fileId) });
				}
			}
		}
		return items;
	}

	interface FileUploadResult {
		fileId: string;
		fileName: string;
		original: string;
	}

	async function batchUploadFiles(files: DIWFileItem_New[]): Promise<FileUploadResult[]> {
		if (files.length > 0) {
			return await Promise.all(
				files.map((f) =>
					apiUpload({
						url: 'admin/sys-file/upload',
						files: [f.raw],
						formData: {
							type: f.fileType === 'image' ? '10' : f.fileType === 'video' ? '20' : '30',
						},
					})
				)
			);
		}

		return [];
	}

	async function platformUploadFile(raw: any, type: string) {
		return await apiUpload({ url: 'admin/sys-file/upload', files: [raw], formData: { type } });
	}

	async function platformUploadFileEx(url: string, raw: any, formData: Record<string, any>) {
		return await apiUpload({ url, files: [raw], formData });
	}

	async function uploadFileHelper(mv: Array<DIWFileItem | string>) {
		const result: DIWFileItem[] = [];

		const filesToUpload = new Map<string, DIWFileItem_New>();

		for (let i = 0; i < mv.length; ++i) {
			const item = mv[i];
			if (typeof item === 'string') {
				result.push({ type: 2, id: item });
			} else {
				if (item.type === 0) {
					result.push(item);
					filesToUpload.set(item.id, item);
				} else if (item.type === 1) {
					result.push(item);
				}
			}
		}

		if (filesToUpload.size > 0) {
			const keys = [...filesToUpload.keys()];
			const d = await batchUploadFiles([...filesToUpload.values()]);
			const resultMap = new Map<string, FileUploadResult>();
			for (let i = 0; i < keys.length; ++i) {
				resultMap.set(keys[i], d[i]);
			}

			for (let i = 0; i < result.length; ++i) {
				const item = result[i];
				if (item.type === 0 || item.type === 2) {
					const f = resultMap.get(item.id);
					if (f) {
						const displayName = f.original || f.fileName;
						result[i] = {
							type: 1,
							id: f.fileId,
							url: mapFileIdToUrl(f.fileId),
							fileType: detectFileTypeFromFileName(displayName),
							displayName,
						};
					}
				}
			}

			return result;
		}

		return null;
	}

	return { mapFileIdToUrl, uploadFileHelper, parseImageFiles, platformUploadFile, platformUploadFileEx };
});
