<template>
	<view class="watch-section">
		<view class="machine-container">
			<!-- 头部标题 -->
			<view class="watch-header">
				<view class="title-wrapper">
					<view class="title-wrapper-inner">
						<view class="title-indicator"></view>
						<text class="title">{{ t('myStand') }}</text>
					</view>
				</view>
				<wd-picker :columns="shiftScheduleColumns" v-model="shiftSchedule" @confirm="onChangeTime" />
				<view class="query-btn">
					<text>{{ t('errorReport') }}</text>
				</view>
			</view>
			<!-- 机台区域 -->
			<view class="machines-section">
				<!-- 循环渲染每种机器类型 -->
				<template v-for="machineGroup in machineGroups" :key="machineGroup.type">
					<!-- 机器类型区域 -->
					<view class="machine-type-section">
						<view class="machine-type-header">
							<text class="machine-type-title">{{ machineGroup.title }}</text>
						</view>
						<view class="machine-cards">
							<view v-for="machine in machineGroup.machines" :key="machine.id" class="machine-card">
								<view class="machine-number-wrapper">
									<view class="machine-number">{{ machine.number }}</view>
									<view class="machine-ranks">
										<text class="rank-item">{{ t('shiftProductionRank') }}:{{ machine.productionRank }}</text>
										<text class="rank-item" v-if="isFineSpinningMachine(machine)">{{ t('breakRank') }}:{{ machine.breakRank }}</text>
									</view>
								</view>
								<!-- 分隔线 -->
								<view class="machine-divider"></view>
								<view class="machine-stats">
									<view class="stat-row">
										<text class="stat-label">{{ t('shiftProduction') }}</text>
										<text class="stat-value">{{ machine.production }}kg</text>
									</view>
									<view class="stat-row" v-if="isFineSpinningMachine(machine)">
										<text class="stat-label">{{ t('shiftBreak') }}</text>
										<text class="stat-value">{{ machine.breakCount }}个</text>
									</view>
									<view class="stat-row">
										<text class="stat-label">{{ t('parkingAlarm') }}</text>
										<text class="stat-value">{{ machine.alarmCount }}个</text>
									</view>
									<view class="stat-row">
										<text class="stat-label">{{ t('maintenanceWorker') }}</text>
										<text class="stat-value">{{ machine.worker }}</text>
									</view>
									<view class="machine-status-row">
										<view class="machine-status" :class="machine.statusClass">
											<text class="status-text">{{ machine.statusText }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 效率图表 -->
					<view class="chart-section">
						<view class="chart-header">
							<text class="chart-title">{{ t('efficiencyValue') }}</text>
						</view>
						<view class="chart-container">
							<view class="chart-svg-container">
								<!-- 使用SVG绘制简单的线图 -->
								<view class="chart-grid">
									<view class="grid-line" v-for="i in 6" :key="i" :style="{ top: (i - 1) * 20 + '%' }"></view>
								</view>
								<view class="chart-lines">
									<view
										v-for="(data, index) in getEfficiencyDataForType(machineGroup.type)"
										:key="data.machineId"
										class="chart-line"
										:style="{
											'--line-color': getChartLegendForType(machineGroup.type)[index]?.color || '#ccc',
											'--line-points': getLinePoints(data.values),
										}"
									></view>
								</view>
								<view class="chart-x-axis">
									<text v-for="time in timeLabels" :key="time" class="x-label">{{ time }}</text>
								</view>
								<view class="chart-y-axis">
									<text v-for="value in yAxisLabels" :key="value" class="y-label">{{ value }}</text>
								</view>
							</view>
						</view>
						<view class="chart-legend">
							<view class="legend-items">
								<view v-for="legend in getChartLegendForType(machineGroup.type)" :key="legend.id" class="legend-item">
									<view class="legend-color" :style="{ backgroundColor: legend.color }"></view>
									<text class="legend-text">{{ legend.label }}</text>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();

const props = defineProps<{
	shiftScheduleList: {
		planStartTime: string;
		shiftCodeName: string;
	}[];
	needRefresh: boolean;
}>();
const { apiGet, protect } = useFramework();
const { shiftScheduleList, needRefresh } = toRefs(props);

const shiftSchedule = ref('');

async function loadData() {
	await protect(async () => {
		const d = await apiGet('mesmultidata/DiwShiftSchedule/hisToTodaylist');
		shiftScheduleList.value = d || [];
	});
}

onMounted(async () => {
	await loadData();
});

const shiftScheduleColumns = computed(() => {
	if (!shiftScheduleList.value || shiftScheduleList.value.length === 0) {
		return [];
	}
	return shiftScheduleList.value.map((item) => ({
		label: item.planStartTime + ' ' + item.shiftCodeName,
	}));
});

watch(
	() => needRefresh,
	async (newVal) => {
		if (newVal) {
			await loadData();
		}
	}
);

// 监听 shiftScheduleList 的变化，设置默认值
watch(
	shiftScheduleList,
	(newList) => {
		if (newList && newList.length > 0 && !shiftSchedule.value) {
			shiftSchedule.value = newList[0].planStartTime;
		}
	},
	{ immediate: true }
);

// 设备类型枚举
enum MachineType {
	FINE_SPINNING = 'fine_spinning', // 细纱机
	BOBBIN = 'bobbin', // 络筒机
}

// 设备状态枚举
enum MachineStatus {
	TODAY = 'today', // 今日指牌车
	COMPLETED = 'completed', // 摘车完成
	WEEK_AGO = 'week_ago', // 7天前指车
	THREE_DAYS = 'three_days', // 3天前指车
}

// 统一设备数据接口
interface BaseMachineData {
	id: string;
	number: string;
	type: MachineType;
	productionRank: number;
	production: number;
	alarmCount: number;
	worker: string;
	status: MachineStatus;
	statusText: string;
	statusClass: string;
}

// 细纱机特有数据
interface FineSpinningMachineData extends BaseMachineData {
	type: MachineType.FINE_SPINNING;
	breakRank: number;
	breakCount: number;
}

// 络筒机特有数据
interface BobbinMachineData extends BaseMachineData {
	type: MachineType.BOBBIN;
}

// 联合类型
type MachineData = FineSpinningMachineData | BobbinMachineData;

// 机器组数据接口
interface MachineGroup {
	type: MachineType;
	title: string;
	machines: MachineData[];
}

// 图例数据接口
interface ChartLegend {
	id: string;
	label: string;
	color: string;
}

// 效率数据接口
interface EfficiencyData {
	machineId: string;
	timePoints: string[];
	values: number[];
}

// 细纱机数据
const fineSpinningMachines = ref<FineSpinningMachineData[]>([
	{
		id: '001',
		number: '001',
		type: MachineType.FINE_SPINNING,
		productionRank: 1,
		breakRank: 8,
		production: 282.35,
		breakCount: 12,
		alarmCount: 0,
		worker: '王大雷',
		status: MachineStatus.TODAY,
		statusText: '今日指牌车',
		statusClass: 'status-today',
	},
	{
		id: '009',
		number: '009',
		type: MachineType.FINE_SPINNING,
		productionRank: 12,
		breakRank: 23,
		production: 267.35,
		breakCount: 15,
		alarmCount: 0,
		worker: '李子君',
		status: MachineStatus.COMPLETED,
		statusText: '摘车完成',
		statusClass: 'status-completed',
	},
	{
		id: '012',
		number: '012',
		type: MachineType.FINE_SPINNING,
		productionRank: 25,
		breakRank: 34,
		production: 209.68,
		breakCount: 23,
		alarmCount: 6,
		worker: '周大生',
		status: MachineStatus.TODAY,
		statusText: '今日指牌车',
		statusClass: 'status-today',
	},
	{
		id: '025',
		number: '025',
		type: MachineType.FINE_SPINNING,
		productionRank: 43,
		breakRank: 52,
		production: 225.37,
		breakCount: 19,
		alarmCount: 8,
		worker: '赵志伟',
		status: MachineStatus.WEEK_AGO,
		statusText: '7天前指车',
		statusClass: 'status-week-ago',
	},
	{
		id: '034',
		number: '034',
		type: MachineType.FINE_SPINNING,
		productionRank: 90,
		breakRank: 94,
		production: 253.16,
		breakCount: 20,
		alarmCount: 12,
		worker: '孙志刚',
		status: MachineStatus.THREE_DAYS,
		statusText: '3天前指车',
		statusClass: 'status-three-days',
	},
	{
		id: '038',
		number: '038',
		type: MachineType.FINE_SPINNING,
		productionRank: 99,
		breakRank: 82,
		production: 245.12,
		breakCount: 28,
		alarmCount: 10,
		worker: '刘志坚',
		status: MachineStatus.THREE_DAYS,
		statusText: '3天前指车',
		statusClass: 'status-three-days',
	},
]);

// 络筒机数据
const bobbinMachines = ref<BobbinMachineData[]>([
	{
		id: 'b001',
		number: '001',
		type: MachineType.BOBBIN,
		productionRank: 3,
		production: 1253.16,
		alarmCount: 0,
		worker: '王大雷',
		status: MachineStatus.TODAY,
		statusText: '今日待揩车',
		statusClass: 'status-today',
	},
	{
		id: 'b009',
		number: '009',
		type: MachineType.BOBBIN,
		productionRank: 10,
		production: 1024.37,
		alarmCount: 0,
		worker: '李子君',
		status: MachineStatus.COMPLETED,
		statusText: '揩车完成',
		statusClass: 'status-completed',
	},
	{
		id: 'b012',
		number: '012',
		type: MachineType.BOBBIN,
		productionRank: 24,
		production: 985.15,
		alarmCount: 6,
		worker: '周大生',
		status: MachineStatus.TODAY,
		statusText: '今日待揩车',
		statusClass: 'status-today',
	},
	{
		id: 'b025',
		number: '025',
		type: MachineType.BOBBIN,
		productionRank: 37,
		production: 957.24,
		alarmCount: 8,
		worker: '赵志伟',
		status: MachineStatus.WEEK_AGO,
		statusText: '7天前揩车',
		statusClass: 'status-week-ago',
	},
	{
		id: 'b034',
		number: '034',
		type: MachineType.BOBBIN,
		productionRank: 74,
		production: 812.57,
		alarmCount: 12,
		worker: '孙志刚',
		status: MachineStatus.THREE_DAYS,
		statusText: '3天前揩车',
		statusClass: 'status-three-days',
	},
	{
		id: 'b038',
		number: '038',
		type: MachineType.BOBBIN,
		productionRank: 89,
		production: 785.49,
		alarmCount: 10,
		worker: '刘志坚',
		status: MachineStatus.THREE_DAYS,
		statusText: '3天前揩车',
		statusClass: 'status-three-days',
	},
]);

// 机器分组数据
const machineGroups = computed<MachineGroup[]>(() => {
	return [
		{
			type: MachineType.FINE_SPINNING,
			title: '细纱机',
			machines: fineSpinningMachines.value,
		},
		{
			type: MachineType.BOBBIN,
			title: '络筒机',
			machines: bobbinMachines.value,
		},
	];
});

// 工具函数：判断是否为细纱机
const isFineSpinningMachine = (machine: MachineData): machine is FineSpinningMachineData => {
	return machine.type === MachineType.FINE_SPINNING;
};

// 效率数据
const efficiencyData = ref<EfficiencyData[]>([
	{
		machineId: '001',
		timePoints: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
		values: [30, 25, 20, 15, 20, 25, 40],
	},
	{
		machineId: '009',
		timePoints: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
		values: [85, 80, 95, 85, 80, 75, 80],
	},
	{
		machineId: '012',
		timePoints: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
		values: [15, 35, 35, 55, 65, 60, 25],
	},
	{
		machineId: '025',
		timePoints: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
		values: [75, 75, 60, 70, 40, 15, 15],
	},
	{
		machineId: '034',
		timePoints: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
		values: [95, 85, 95, 85, 85, 95, 70],
	},
	{
		machineId: '038',
		timePoints: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
		values: [95, 85, 95, 85, 85, 95, 70],
	},
]);

// 图表图例数据
const chartLegends = ref<ChartLegend[]>([
	{ id: '001', label: '001', color: '#8B5CF6' },
	{ id: '009', label: '009', color: '#EC4899' },
	{ id: '012', label: '012', color: '#10B981' },
	{ id: '025', label: '025', color: '#06B6D4' },
	{ id: '034', label: '034', color: '#3B82F6' },
	{ id: '038', label: '038', color: '#F59E0B' },
]);

// 图表相关计算属性
const timeLabels = computed(() => ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00']);
const yAxisLabels = computed(() => [100, 80, 60, 40, 20, 0]);

// 根据机器类型获取效率数据
const getEfficiencyDataForType = (machineType: MachineType) => {
	// 根据机器类型过滤效率数据
	const machineIds = machineGroups.value.find((group) => group.type === machineType)?.machines.map((machine) => machine.id) || [];

	return efficiencyData.value.filter((data) => machineIds.includes(data.machineId));
};

// 根据机器类型获取图表图例
const getChartLegendForType = (machineType: MachineType) => {
	// 根据机器类型过滤图例数据
	const machineIds = machineGroups.value.find((group) => group.type === machineType)?.machines.map((machine) => machine.id) || [];

	return chartLegends.value.filter((legend) => machineIds.includes(legend.id));
};

// 生成线条路径点
const getLinePoints = (values: number[]) => {
	const points = values
		.map((value, index) => {
			const x = (index / (values.length - 1)) * 100;
			const y = 100 - value;
			return `${x}% ${y}%`;
		})
		.join(', ');
	return points;
};

const onChangeTime = (value: any) => {
	console.log(`Selected item: ${value}`);
};
</script>

<style lang="scss" scoped>
.watch-section {
	margin-top: 26rpx;
	width: 702rpx;
	min-height: 1178rpx;
	padding-bottom: 32rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 702rpx 1178rpx;
	background-repeat: no-repeat;
	background-position: top center;
	background-color: #feffff;
	border-radius: 16rpx;
}
.machine-container {
	width: 702rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 34rpx 36rpx 0 36rpx;
}

.watch-header {
	width: 654rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title-wrapper {
		display: flex;
		align-items: center;
		.title-wrapper-inner {
			display: flex;
			align-items: center;

			.title-indicator {
				width: 8rpx;
				height: 32rpx;
				background: #0082f0;
				border-radius: 4rpx;
				margin-right: 16rpx;
			}

			.title {
				font-size: 30rpx;
				font-weight: normal;
				line-height: 24rpx;
				text-align: center;
				letter-spacing: 0rpx;
				color: #1d2129;
			}
		}
	}

	.date-selector {
		display: flex;
		align-items: center;
		gap: 8rpx;

		.date-selector-inner,
		.evening-selector-inner {
			display: flex;
			align-items: center;
		}

		.date-text,
		.evening-text {
			font-size: 24rpx;
			font-weight: normal;
			line-height: 32rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #4b5563;
		}
	}

	.query-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8rpx 12rpx;
		font-size: 20rpx;
		font-weight: normal;
		line-height: 18rpx;
		letter-spacing: 0rpx;
		color: #ffffff;
		background: #e43535;
		border-radius: 18rpx;
	}
}
.machines-section {
	width: 100%;
	margin-top: 42rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.machine-type-section {
	width: 658rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.machine-type-header {
		width: 658rpx;
		margin-left: 6rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 48rpx;
		border-radius: 8rpx;
		background: linear-gradient(270deg, rgba(0, 130, 240, 0) 0%, rgba(0, 130, 240, 0.2) 100%);

		.machine-type-title {
			padding-left: 24rpx;

			font-size: 28rpx;
			font-weight: 600;
			line-height: 12px;
			display: flex;
			align-items: center;
			letter-spacing: 0rpx;
			color: #1d2129;
		}
	}
}

.machine-cards {
	margin-top: 20rpx;
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
	gap: 20rpx;
	width: 100%;
}

.machine-card {
	background: white;
	border-radius: 16rpx;
	border: 1rpx solid #cfd3d7;
	padding: 20rpx 20rpx 0 20rpx;
	position: relative;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	min-height: 240rpx;
}
.machine-number-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.machine-divider {
	width: 100%;
	margin: 8rpx 0;
	border: 1rpx solid #cfd3d7;
}
.machine-number {
	font-size: 36rpx;
	font-weight: normal;
	line-height: 14px;
	text-align: center;
	display: flex;
	align-items: center;
	letter-spacing: 0rpx;
	color: #091f44;
}

.machine-ranks {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 4rpx;

	.rank-item {
		padding: 2rpx 4rpx;
		border-radius: 6rpx 12rpx 12rpx 2rpx;
		background: rgba(8, 161, 244, 0.1);

		font-size: 16rpx;
		font-weight: normal;
		line-height: 28rpx;
		text-align: center;
		display: flex;
		align-items: center;
		letter-spacing: 0rpx;

		font-variation-settings: 'opsz' auto;
		color: #08a1f4;
	}
}

.machine-stats {
	display: flex;
	flex-direction: column;
	align-items: space-between;
	gap: 8rpx;
}

.stat-row {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.stat-label {
		font-size: 20rpx;
		font-weight: normal;
		line-height: 28rpx;
		display: flex;
		align-items: center;
		letter-spacing: 0rpx;
		color: #4b5563;
	}

	.stat-value {
		font-size: 20rpx;
		font-weight: normal;
		line-height: 28rpx;
		text-align: center;
		display: flex;
		align-items: center;
		letter-spacing: 0rpx;
		color: #1d2129;
	}
}

.machine-status {
	flex: 0 0 auto;
	padding: 0rpx 8rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	.status-text {
		font-size: 16rpx;
		font-weight: normal;
		line-height: 28rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		letter-spacing: 0rpx;
	}

	// 今日待揩车状态
	&.status-today {
		background: rgba(255, 154, 26, 0.2);

		.status-text {
			color: #ff9a1a;
		}
	}

	// 揩车完成状态
	&.status-completed {
		background: rgba(147, 107, 254, 0.2);

		.status-text {
			color: #936bfe;
		}
	}

	// 3天前揩车状态
	&.status-three-days {
		background: rgba(0, 130, 240, 0.2);

		.status-text {
			color: #0082f0;
		}
	}

	// 7天前揩车状态
	&.status-week-ago {
		background: rgba(10, 210, 225, 0.2);

		.status-text {
			color: #0bb9c5;
		}
	}
}

.machine-status-row {
	display: flex;
	justify-content: flex-start;
}

.chart-section {
	width: 600rpx;
	padding: 24rpx;
	background: white;
	border-radius: 12rpx;

	.chart-header {
		.chart-title {
			font-size: 20rpx;
			font-weight: normal;
			line-height: 40rpx;
			text-align: right;
			letter-spacing: 0rpx;
			color: #4b5563;
		}
	}

	.chart-container {
		height: 300rpx;
		width: 100%;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;

		.chart-svg-container {
			width: 100%;
			height: 100%;
			position: relative;
			padding: 20rpx;
			box-sizing: border-box;
		}

		.chart-grid {
			position: absolute;
			top: 20rpx;
			left: 40rpx;
			right: 20rpx;
			bottom: 40rpx;

			.grid-line {
				position: absolute;
				left: 0;
				right: 0;
				height: 1rpx;
				background: #e5e5e5;
			}
		}

		.chart-lines {
			position: absolute;
			top: 20rpx;
			left: 40rpx;
			right: 20rpx;
			bottom: 40rpx;

			.chart-line {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;

				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: linear-gradient(
						to right,
						var(--line-color) 0%,
						var(--line-color) 14.28%,
						transparent 14.28%,
						transparent 28.56%,
						var(--line-color) 28.56%,
						var(--line-color) 42.84%,
						transparent 42.84%,
						transparent 57.12%,
						var(--line-color) 57.12%,
						var(--line-color) 71.4%,
						transparent 71.4%,
						transparent 85.68%,
						var(--line-color) 85.68%,
						var(--line-color) 100%
					);
					height: 2rpx;
					border-radius: 1rpx;
				}
			}
		}

		.chart-x-axis {
			position: absolute;
			bottom: 0;
			left: 40rpx;
			right: 20rpx;
			height: 40rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.x-label {
				font-size: 20rpx;
				color: #86909c;
			}
		}

		.chart-y-axis {
			position: absolute;
			top: 20rpx;
			left: 0;
			bottom: 40rpx;
			width: 40rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: flex-end;
			padding-right: 8rpx;

			.y-label {
				font-size: 20rpx;
				color: #86909c;
			}
		}
	}

	.chart-legend {
		.legend-items {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			justify-content: center;
		}

		.legend-item {
			display: flex;
			align-items: center;
			gap: 8rpx;

			.legend-color {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
			}

			.legend-text {
				font-size: 24rpx;
				color: #1d2129;
			}
		}
	}
}

:deep(.wd-picker) {
	.wd-cell {
		padding: 0;
		background-color: transparent;
		.wd-cell__wrapper {
			padding: 0;
		}
	}
}
</style>
