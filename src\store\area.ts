import { useSystemStore } from './system';

interface AreaItem {
	adcode: string;
}

export const useAreaStore = defineStore('area', () => {
	const { apiGet } = useSystemStore();

	const cache = new Map<string, Record<string, any>[]>();

	async function getAreaList(pid?: string): Promise<Record<string, any>[]> {
		if (pid === undefined) {
			pid = '100000';
		}

		const result = cache.get(pid);
		if (result) {
			return result;
		}

		const d = (await getAreaListImpl(pid)).sort((a: AreaItem, b: AreaItem) => a.adcode.localeCompare(b.adcode));
		cache.set(pid, d);
		return d;
	}

	async function getAreaListImpl(pid: string) {
		return apiGet('admin/sysArea/tree?pid=' + encodeURI(pid));
	}

	return { getAreaList };
});
