<template>
	<DIWRoot>
		<DIWSection title="section1">
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="section2">
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="section3">
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="section4">
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="section5">
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="section6">
			<wd-cell-group title="列表与滚动" border>
				<wd-cell title="mode1" is-link to="/mod/devel/sampleHost?mode=list1" />
				<wd-cell title="mode2" is-link to="/mod/devel/sampleHostPull?mode=list1" />
				<wd-cell title="mode3" is-link to="/mod/devel/sampleHostCustom?mode=list1" />
				<wd-cell title="mode4" is-link to="/mod/devel/sampleHostPullCustom?mode=list1" />
			</wd-cell-group>
		</DIWSection>

		<wd-gap safe-area-bottom height="0" />
	</DIWRoot>
</template>

<script setup lang="ts">
const { apiGet, protect } = useFramework();

onPullDownRefresh(async () => {
	await loadImpl();
	uni.stopPullDownRefresh();
});

onReachBottom(() => {
	loadImpl();
});

async function loadImpl() {
	return await protect(async () => {
		const d = await apiGet({
			url: 'admin/log/page',
			params: {
				current: 1,
				size: 20,
				descs: 'create_time',
			},
		});
		console.log(d);
	});
}

onMounted(() => {
	loadImpl();
});
</script>
