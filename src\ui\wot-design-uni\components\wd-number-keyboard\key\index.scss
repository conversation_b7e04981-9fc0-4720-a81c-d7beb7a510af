@use './../../common/abstracts/_mixin.scss' as *;
@use './../../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(key) {
		background: $wot-dark-background2;
		color: $wot-dark-color;

		&:active {
			background-color: $wot-dark-background4;
		}

		@include m(active) {
			background-color: $wot-dark-background4;
		}
	}
}

.wd-key-wrapper {
	position: relative;
	flex: 1;
	flex-basis: 33%;
	box-sizing: border-box;
	padding: 0 6px 6px 0;

	@include m(wider) {
		flex-basis: 66%;
	}
}

@include b(key) {
	display: flex;
	align-items: center;
	justify-content: center;
	height: $wot-number-keyboard-key-height;
	font-size: $wot-number-keyboard-key-font-size;
	line-height: 1.5;
	background: $wot-number-keyboard-key-background;
	border-radius: $wot-number-keyboard-key-border-radius;

	&:active {
		background-color: $wot-number-keyboard-key-active-color;
	}

	@include m(large) {
		position: absolute;
		top: 0;
		right: 6px;
		bottom: 6px;
		left: 0;
		height: auto;
	}

	@include m(delete, close) {
		font-size: $wot-number-keyboard-delete-font-size;
	}

	@include m(active) {
		background-color: $wot-number-keyboard-key-active-color;
	}

	@include m(close) {
		color: $wot-number-keyboard-button-text-color;
		background: $wot-number-keyboard-button-background;

		&:active {
			background: $wot-number-keyboard-button-background;
			opacity: $wot-number-keyboard-button-active-opacity;
		}
	}

	@include edeep(loading-icon) {
		color: $wot-number-keyboard-button-text-color;
	}

	@include edeep(icon) {
		font-size: $wot-number-keyboard-icon-size;
	}
}
