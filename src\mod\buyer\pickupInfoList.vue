<template>
	<DIWAppPage mode="1" :title="title">
		<DIWScrollView>
			<DIWListView ref="listView" :meta="listViewMeta">
				<template #default="{ data }">
					<DIWListItem @click="openDetail(data)">
						<template #header>
							<view class="flex flex-row justify-between items-center">
								<text>🚚 {{ data.plateNumber }}</text>
								<wd-tag v-if="data.defaultFlag" type="primary" round custom-class="h-fit whitespace-nowrap">默认</wd-tag>
							</view>
						</template>

						<view class="flex flex-row justify-between">
							<text>{{ data.name }}</text>
							<text>📞 {{ data.phone }}</text>
						</view>
						<!--
						<text>{{ data.certId }}</text>
						<template #footer>
							<text v-if="data.remark">{{ data.remark }}</text>
						</template>
                    -->
					</DIWListItem>
				</template>
			</DIWListView>
		</DIWScrollView>
		<template #bottomBar>
			<DIWAuth v-if="!isSelectMode" auth="admin_pickup_info_add">
				<DIWBottomBar>
					<view class="flex flex-row items-center gap-4 ml-auto">
						<wd-button @click="openDetail()">添加自提信息</wd-button>
					</view>
				</DIWBottomBar>
			</DIWAuth>
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { apiGet, protect, invokeTo, usePageQuery, usePageResult } = useFramework();

const pq = usePageQuery();
const { finishPage } = usePageResult();

const isSelectMode = ref(false);

watchEffect(() => {
	isSelectMode.value = pq.value.mode === 'select';
});

const title = computed(() => {
	if (isSelectMode.value) {
		if (pq.value.ct) {
			return pq.value.ct;
		}
	}

	return '自提信息';
});

const listView = ref();

const searchModel = ref({});

const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(trackInfo) {
			const size1 = 20;
			const size2 = 5;

			const isReload = trackInfo === undefined;

			let current = isReload ? 1 : trackInfo;
			let size = isReload ? size1 : size2;

			const d = await apiGet({
				url: 'admin/pickup/info/page',
				params: {
					current,
					size,
					...searchModel.value,
				},
			});

			const items = d.records;

			if (isReload) {
				return { hasMore: d.total > size, items, trackInfo: 1 + size1 / size2 };
			}

			return { hasMore: d.total > current * size, items, trackInfo: current + 1 };
		},
	});
});

function openDetail(data?: Record<string, any>) {
	if (isSelectMode.value) {
		finishPage(data!);
		return;
	}

	protect(async () => {
		await invokeTo({ url: '/mod/buyer/pickupInfoDetail', params: { id: data?.id } });
		listView.value.reload();
	});
}
</script>
