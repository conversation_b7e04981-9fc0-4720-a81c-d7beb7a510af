@use '../../common/abstracts/variable' as *;
@use '../../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(month) {
		@include e(title) {
			color: $wot-dark-color;
		}

		@include e(days) {
			color: $wot-dark-color;
		}

		@include e(day) {
			@include when(disabled) {
				.wd-month__day-text {
					color: $wot-dark-color-gray;
				}
			}
		}
	}
}

@include b(month) {
	@include e(title) {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 45px;
		font-size: $wot-calendar-panel-title-fs;
		color: $wot-calendar-panel-title-color;
	}

	@include e(days) {
		display: flex;
		flex-wrap: wrap;
		font-size: $wot-calendar-day-fs;
		color: $wot-calendar-day-color;
	}

	@include e(day) {
		position: relative;
		width: 14.285%;
		height: $wot-calendar-day-height;
		line-height: $wot-calendar-day-height;
		text-align: center;
		margin-bottom: $wot-calendar-item-margin-bottom;

		@include when(disabled) {
			.wd-month__day-text {
				color: $wot-calendar-disabled-color;
			}
		}

		@include when(current) {
			color: $wot-calendar-active-color;
		}

		@include when(selected, multiple-selected) {
			.wd-month__day-container {
				border-radius: $wot-calendar-active-border;
				background: $wot-calendar-active-color;
				color: $wot-calendar-selected-color;
			}
		}

		@include when(middle) {
			.wd-month__day-container {
				background: $wot-calendar-range-color;
			}
		}
		@include when(multiple-middle) {
			.wd-month__day-container {
				background: $wot-calendar-active-color;
				color: $wot-calendar-selected-color;
			}
		}

		@include when(start) {
			&::after {
				position: absolute;
				content: '';
				height: $wot-calendar-day-height;
				top: 0;
				right: 0;
				left: 50%;
				background: $wot-calendar-range-color;
				z-index: 1;
			}

			&.is-without-end::after {
				display: none;
			}

			.wd-month__day-container {
				background: $wot-calendar-active-color;
				color: $wot-calendar-selected-color;
				border-radius: $wot-calendar-active-border 0 0 $wot-calendar-active-border;
			}
		}

		@include when(end) {
			&::after {
				position: absolute;
				content: '';
				height: $wot-calendar-day-height;
				top: 0;
				left: 0;
				right: 50%;
				background: $wot-calendar-range-color;
				z-index: 1;
			}

			.wd-month__day-container {
				background: $wot-calendar-active-color;
				color: $wot-calendar-selected-color;
				border-radius: 0 $wot-calendar-active-border $wot-calendar-active-border 0;
			}
		}

		@include when(same) {
			.wd-month__day-container {
				background: $wot-calendar-active-color;
				color: $wot-calendar-selected-color;
				border-radius: $wot-calendar-active-border;
			}
		}

		@include when(last-row) {
			margin-bottom: 0;
		}
	}

	@include e(day-container) {
		position: relative;
		z-index: 2;
	}

	@include e(day-text) {
		font-weight: $wot-calendar-day-fw;
	}

	@include e(day-top) {
		position: absolute;
		top: 10px;
		left: 0;
		right: 0;
		line-height: 1.1;
		font-size: $wot-calendar-info-fs;
		text-align: center;
	}

	@include e(day-bottom) {
		position: absolute;
		bottom: 10px;
		left: 0;
		right: 0;
		line-height: 1.1;
		font-size: $wot-calendar-info-fs;
		text-align: center;
	}
}
