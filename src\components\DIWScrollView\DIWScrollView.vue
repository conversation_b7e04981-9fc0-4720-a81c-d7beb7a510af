<template>
	<scroll-view
		:scroll-y="props.scrollY"
		:scroll-x="props.scrollX"
		:class="customClass"
		:style="customStyle"
		:refresher-enabled="enablePull"
		:refresher-triggered="triggered"
		:refresher-background="refresherBackground"
		@scrolltolower="onScrollToLower"
		@refresherpulling="onPulling"
		@refresherrefresh="onRefresh"
		@refresherrestore="onRestore"
		@refresherabort="onAbort"
	>
		<slot />
	</scroll-view>
</template>

<script setup lang="ts">
import { provideDIWScrollManager } from '@/framework';

interface DIWScrollViewProps {
	scrollY?: boolean;
	scrollX?: boolean;

	// 根结点 class
	customClass?: DIWClassType;

	// 根结点 style
	customStyle?: DIWStyleType;

	refresherBackground?: string;
}

const props = withDefaults(defineProps<DIWScrollViewProps>(), {
	scrollY: true,
	scrollX: false,
	customClass: 'diw-scroll-view',
	refresherBackground: '#f0f0f0',
});
const customClass = computed(() => parseClass(props.customClass));
const customStyle = computed(() => parseStyle(props.customStyle));

const enablePull = ref(false);
const triggered = ref(false);

let onPullDownRefreshCallback: (() => void) | null = null;
let onReachBottomCallback: (() => void) | null = null;

function onPulling() {}

function onRefresh() {
	triggered.value = true;
	if (onPullDownRefreshCallback) {
		onPullDownRefreshCallback();
	}
}

function onRestore() {
	triggered.value = false;
}

function onAbort() {
	triggered.value = false;
}

function onScrollToLower() {
	if (onReachBottomCallback) {
		onReachBottomCallback();
	}
}

provideDIWScrollManager({
	onPulldownRefresh(callback) {
		enablePull.value = true;
		onPullDownRefreshCallback = callback;
	},

	onReachBottom(callback) {
		onReachBottomCallback = callback;
	},

	startPulldownRefresh() {
		enablePull.value = true;
		triggered.value = true;
	},

	stopPulldownRefresh() {
		enablePull.value = true;
		triggered.value = false;
	},

	scrollToSelector(selector: string) {
		console.log('scrollToSelector', selector);
	},
});
</script>

<style lang="scss">
.diw-scroll-view {
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
}
</style>
