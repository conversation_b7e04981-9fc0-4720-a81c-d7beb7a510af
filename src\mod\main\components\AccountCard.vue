<template>
	<view class="account-card">
		<view class="account-header">
			<text class="account-title">资金账户</text>
			<view class="account-badge" @click="props.onMoreClick">
				<text class="account-badge-text">{{ hasData ? '查看详情' : '查看账户' }}</text>
			</view>
		</view>

		<view class="account-content">
			<view class="account-item account-item-main">
				<view class="account-label">账户余额</view>
				<view class="account-value">
					<text class="currency-symbol">¥</text>
					<DIWCurrency custom-class="currency-amount" :value="props.accountAmount || 0" suffix="" prefix="" />
				</view>
				<view v-if="!hasData" class="account-tips">
					<text>首次登录请先查看账户信息</text>
				</view>
			</view>

			<view class="account-divider" />

			<view class="account-sub-items">
				<view class="account-item">
					<view class="account-label">可用余额</view>
					<view class="account-value">
						<text class="currency-symbol-small">¥</text>
						<DIWCurrency custom-class="currency-amount-small" :value="props.balance || 0" suffix="" prefix="" />
					</view>
				</view>

				<view class="account-item">
					<view class="account-label">冻结金额</view>
					<view class="account-value">
						<text class="currency-symbol-small">¥</text>
						<DIWCurrency custom-class="currency-amount-small" :value="props.frozenAmount || 0" suffix="" prefix="" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
const props = defineProps<{
	accountAmount?: number;
	balance?: number;
	frozenAmount?: number;
	onMoreClick?: () => void;
}>();

const hasData = computed(() => {
	return props.accountAmount !== undefined || props.balance !== undefined || props.frozenAmount !== undefined;
});
</script>

<style lang="scss" scoped>
.account-card {
	margin: 16rpx 16rpx 8rpx 16rpx;
	background: linear-gradient(135deg, #ff6b6b 0%, #ff5000 100%);
	border-radius: 20rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(255, 80, 0, 0.2);

	.account-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;

		.account-title {
			font-size: 34rpx;
			font-weight: bold;
			color: #ffffff;
		}

		.account-badge {
			display: flex;
			align-items: center;
			background: rgba(255, 255, 255, 0.2);
			padding: 8rpx 16rpx;
			border-radius: 30rpx;

			.account-badge-text {
				font-size: 24rpx;
				color: #ffffff;
			}
		}
	}

	.account-content {
		.account-item {
			&-main {
				margin-bottom: 20rpx;

				.account-value {
					margin-top: 8rpx;

					.currency-symbol {
						font-size: 36rpx;
						color: #ffffff;
						font-weight: bold;
					}

					:deep(.currency-amount) {
						font-size: 56rpx;
						color: #ffffff;
						font-weight: bold;
						margin-left: 4rpx;
					}
				}

				.account-tips {
					margin-top: 8rpx;

					text {
						font-size: 22rpx;
						color: rgba(255, 255, 255, 0.7);
					}
				}
			}

			.account-label {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.8);
			}

			.account-value {
				display: flex;
				align-items: baseline;

				.currency-symbol-small {
					font-size: 24rpx;
					color: #ffffff;
					font-weight: 500;
				}

				:deep(.currency-amount-small) {
					font-size: 32rpx;
					color: #ffffff;
					font-weight: 500;
					margin-left: 4rpx;
				}
			}
		}

		.account-divider {
			height: 1rpx;
			background: rgba(255, 255, 255, 0.3);
			margin: 20rpx 0;
		}

		.account-sub-items {
			display: flex;
			justify-content: space-between;
		}
	}
}
</style>
