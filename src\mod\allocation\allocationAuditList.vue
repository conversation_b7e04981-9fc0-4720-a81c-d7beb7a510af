<template>
  <DIWAppPage title="调拨单入库">
    <DIWScrollView>
      <DIWListView ref="auditListView" :meta="auditListMeta">
        <template #default="{ data }">
          <view class="allocation-card">
            <view class="allocation-card__header">
              <view class="allocation-card__header-left">
                <text class="allocation-card__code">{{ data.allocationNo }}</text>
                <text class="allocation-card__order-no">调拨名称: {{ data.allocationName }}</text>
              </view>
              <view class="allocation-card__status" :class="getStatusClass(data.status)">
                {{ data.statusStr }}
              </view>
            </view>

            <view class="allocation-card__body">
              <view class="allocation-card__warehouse">
                <view class="allocation-card__warehouse-item">
                  <text class="allocation-card__warehouse-label">出库仓库</text>
                  <text class="allocation-card__warehouse-name">{{ data.outWarehouseName }}</text>
                </view>
                <view class="allocation-card__warehouse-arrow">
                  <text class="allocation-card__warehouse-arrow-icon">→</text>
                </view>
                <view class="allocation-card__warehouse-item">
                  <text class="allocation-card__warehouse-label">入库仓库</text>
                  <text class="allocation-card__warehouse-name">{{ data.inWarehouseName }}</text>
                </view>
              </view>

              <view class="allocation-card__info-grid">
                <view class="allocation-card__info-item">
                  <text class="allocation-card__info-label">调拨类型</text>
                  <text class="allocation-card__info-value">{{ data.allocationTypeStr }}</text>
                </view>
                <view class="allocation-card__info-item">
                  <text class="allocation-card__info-label">数量</text>
                  <DIWCurrency class="allocation-card__info-value" :value="data.quantity" :precision="4" suffix="吨" />
                </view>
                <view class="allocation-card__info-item">
                  <text class="allocation-card__info-label">运输方式</text>
                  <text class="allocation-card__info-value">{{ data.deliveryMethodStr }}</text>
                </view>
                <view class="allocation-card__info-item">
                  <text class="allocation-card__info-label">创建时间</text>
                  <text class="allocation-card__info-value">{{ formatDateTime(data.createTime) }}</text>
                </view>
              </view>

              <view class="allocation-card__product">
                <text class="allocation-card__product-title">商品信息</text>
                <view class="allocation-card__product-info">
                  <text class="allocation-card__product-name">{{ data.productNameArray }}</text>
                  <text class="allocation-card__product-batch">批次号: {{ data.batchNoArray }}</text>
                </view>
              </view>
            </view>

            <!-- <view class="allocation-card__footer">
              <DIWAuth auth="warehouseAllocation_view2">
                <view class="allocation-card__action-btn" @click="handleInbound(data)" v-if="data.status === 3">
                  <text class="allocation-card__action-text">入库</text>
                </view>
                <view class="allocation-card__view-btn" @click="handleViewDetail(data)">
                  <text class="allocation-card__view-text">查看详情</text>
                </view>
              </DIWAuth>
            </view> -->
          </view>
        </template>

        <template #empty>
          <view class="allocation-empty">
            <image class="allocation-empty__image" src="/static/images/empty-box.png" mode="aspectFit"></image>
            <text class="allocation-empty__text">暂无待入库调拨单</text>
          </view>
        </template>
      </DIWListView>
    </DIWScrollView>
  </DIWAppPage>
</template>

<script setup lang="ts">
const { apiGet, protect, navigateTo } = useFramework();

// 列表引用
const auditListView = ref();

// 调拨单入库列表数据
const auditListMeta = computed(() => {
  return defineDIWListViewMeta({
    rowKey: 'id',
    async loadData(pageIndex) {
      const isReload = pageIndex === undefined;
      const current = isReload ? 1 : pageIndex;
      const size = isReload ? 10 : 5;

      const res = await apiGet({
        url: 'stock/warehouseAllocation/page',
        params: {
          current,
          size,
          descs: 'create_time',
          code: '',
        },
      });

      return {
        hasMore: res ? res.total > current * size : false,
        items: res?.records || [],
        trackInfo: isReload ? 1 + 10 / 5 : current + 1,
      };
    },
  });
});

// 处理入库操作
function handleInbound(data: any) {
  protect(async () => {
    navigateTo({
      url: '/mod/allocation/allocationInbound',
      params: {
        id: data.id,
      },
    });
  });
}

// 查看调拨单详情
function handleViewDetail(data: any) {
  protect(async () => {
    navigateTo({
      url: '/mod/allocation/allocationDetail',
      params: {
        id: data.id,
      },
    });
  });
}

// 获取状态样式类
function getStatusClass(status: number) {
  switch (status) {
    case 0: // 草稿
      return 'status-pending';
    case 1: // 待出库
      return 'status-todo';
    case 2: // 待收货
      return 'status-processing';
    case 3: // 待入库
      return 'status-processing';
    case 4: // 部分入库
      return 'status-partial';
    case 5: // 已完成
      return 'status-done';
    default:
      return 'status-default';
  }
}

// 格式化日期时间
function formatDateTime(dateStr: string) {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
}

// 页面加载时刷新列表
onMounted(() => {
  if (auditListView.value) {
    auditListView.value.reload();
  }
});

// 暴露刷新方法给父组件
defineExpose({
  reload: () => {
    if (auditListView.value) {
      auditListView.value.reload();
    }
  },
});
</script>

<style lang="scss" scoped>
/* 卡片样式 */
.allocation-card {
  margin: 24rpx 32rpx;
  border-radius: 24rpx;
  background-color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 8rpx;
    height: 100%;
    background: #5b8ef9;
  }
}

.allocation-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 32rpx;
  border-bottom: 2rpx solid #e5e6eb;
}

.allocation-card__header-left {
  display: flex;
  flex-direction: column;
}

.allocation-card__code {
  font-size: 34rpx;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.allocation-card__order-no {
  font-size: 24rpx;
  color: #86909c;
}

.allocation-card__status {
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 24rpx;
  border-radius: 32rpx;
}

.allocation-card__body {
  padding: 28rpx 32rpx;
}

.allocation-card__warehouse {
  display: flex;
  align-items: center;
  margin-bottom: 28rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #e5e6eb;
}

.allocation-card__warehouse-item {
  flex: 1;
}

.allocation-card__warehouse-label {
  display: block;
  font-size: 24rpx;
  color: #86909c;
  margin-bottom: 8rpx;
}

.allocation-card__warehouse-name {
  display: block;
  font-size: 28rpx;
  color: #1d2129;
  font-weight: 500;
}

.allocation-card__warehouse-arrow {
  width: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.allocation-card__warehouse-arrow-icon {
  font-size: 32rpx;
  color: #5b8ef9;
  font-weight: bold;
}

.allocation-card__info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx 32rpx;
  margin-bottom: 28rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #e5e6eb;
}

.allocation-card__info-item {
  display: flex;
  flex-direction: column;
}

.allocation-card__info-label {
  font-size: 24rpx;
  color: #86909c;
  margin-bottom: 8rpx;
}

.allocation-card__info-value {
  font-size: 28rpx;
  color: #1d2129;
  font-weight: 500;
}

.allocation-card__product {
  display: flex;
  flex-direction: column;
}

.allocation-card__product-title {
  font-size: 26rpx;
  color: #86909c;
  margin-bottom: 16rpx;
}

.allocation-card__product-info {
  background-color: #f9fafc;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
}

.allocation-card__product-name {
  display: block;
  font-size: 28rpx;
  color: #1d2129;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.allocation-card__product-batch {
  display: block;
  font-size: 24rpx;
  color: #86909c;
}

.allocation-card__footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 32rpx;
  border-top: 2rpx solid #e5e6eb;
  background-color: #fafbfc;
  gap: 16rpx;
}

.allocation-card__action-btn {
  display: flex;
  align-items: center;
  background-color: #5b8ef9;
  color: #ffffff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
}

.allocation-card__view-btn {
  display: flex;
  align-items: center;
  background-color: #f2f3f5;
  color: #4e5969;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
}

.allocation-card__action-text, .allocation-card__view-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 空状态样式 */
.allocation-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.allocation-empty__image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.allocation-empty__text {
  font-size: 28rpx;
  color: #86909c;
}

/* 状态样式 */
.status-pending {
  background-color: #f2f3f5;
  color: #86909c;
}

.status-todo {
  background-color: #fff8e6;
  color: #ff7d00;
}

.status-processing {
  background-color: #e8f3ff;
  color: #5b8ef9;
}

.status-partial {
  background-color: #f2e8ff;
  color: #722ed1;
}

.status-done {
  background-color: #e8ffea;
  color: #00b42a;
}

.status-default {
  background-color: #f2f3f5;
  color: #86909c;
}
</style>