@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(collapse) {
		background: $wot-dark-background2;

		@include e(content) {
			color: $wot-dark-color3;
		}
	}
}

@include b(collapse) {
	background: $wot-color-white;

	@include when(viewmore) {
		padding: $wot-collapse-side-padding;
	}
	@include e(content) {
		font-size: $wot-collapse-body-fs;
		color: $wot-collapse-body-color;

		@include when(retract) {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			overflow: hidden;
			font-size: $wot-collapse-retract-fs;
		}
	}
	@include e(more) {
		display: inline-block;
		font-size: $wot-collapse-retract-fs;
		margin-top: 8px;
		color: $wot-collapse-more-color;
		user-select: none;
	}
	@include e(more-txt) {
		display: inline-block;
		vertical-align: middle;
		margin-right: 4px;
	}
	@include e(arrow) {
		display: inline-block;
		vertical-align: middle;
		transition: transform 0.1s;
		font-size: $wot-collapse-arrow-size;
		height: $wot-collapse-arrow-size;
		line-height: $wot-collapse-arrow-size;

		@include when(retract) {
			transform: rotate(-180deg);
		}
	}
}
