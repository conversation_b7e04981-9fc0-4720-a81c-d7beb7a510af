<!-- 故障上报浮动按钮 -->
<template>
	<view
		class="fault-float-container"
		:style="{
			left: position.x + 'rpx',
			top: position.y + 'rpx',
			transition: isDragging ? 'none' : 'all 0.3s ease',
		}"
		@touchstart="onTouchStart"
		@touchmove="onTouchMove"
		@touchend="onTouchEnd"
		@click="handleClick"
	>
		<view class="fault-float-btn" :class="{ dragging: isDragging }">
			<image src="/static/icons/<EMAIL>" class="w-[24rpx] h-[24rpx]" mode="aspectFill" />
			<text class="fault-text">{{ t('faultReport') }}</text>
			<wd-icon name="arrow-right" size="24rpx" color="#fff" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { navigateTo } = useFramework();

// 获取屏幕尺寸和安全区域信息
const { windowHeight, windowWidth, safeAreaInsets } = uni.getSystemInfoSync();

// 拖拽状态
const isDragging = ref(false);
const position = reactive({
	x: 0, // 初始左侧位置 rpx
	y: 150, // 初始位置 rpx，从顶部开始计算
});

// 拖拽相关变量
const startTouch = reactive({
	y: 0,
	startY: 0,
});

// iOS性能优化 - 防抖
let rafId: number | null = null;

// 按钮尺寸 rpx
const buttonHeight = 80;

// 初始化位置
onMounted(() => {
	// 将初始位置设置为屏幕中间偏下位置
	const screenHeightRpx = (windowHeight / windowWidth) * 750; // 转换为rpx
	// 考虑iOS安全区域
	const safeTopRpx = safeAreaInsets ? (safeAreaInsets.top / windowWidth) * 750 : 0;
	position.y = Math.max(safeTopRpx + 100, screenHeightRpx * 0.15); // 屏幕15%的位置，但不超过安全区域
});

const onTouchStart = (e: TouchEvent) => {
	e.stopPropagation();
	isDragging.value = true;
	startTouch.y = e.touches[0].clientY;
	startTouch.startY = position.y;
};

const onTouchMove = (e: TouchEvent) => {
	if (!isDragging.value) return;
	e.preventDefault();
	e.stopPropagation();

	// 使用requestAnimationFrame优化iOS性能
	if (rafId) {
		cancelAnimationFrame(rafId);
	}

	rafId = requestAnimationFrame(() => {
		const deltaY = (e.touches[0].clientY - startTouch.y) * (750 / windowWidth); // 转换为rpx
		let newY = startTouch.startY + deltaY;

		// 限制在安全范围内，考虑iOS安全区域
		const screenHeightRpx = (windowHeight / windowWidth) * 750;
		const safeTopRpx = safeAreaInsets ? (safeAreaInsets.top / windowWidth) * 750 : 0;
		const safeBottomRpx = safeAreaInsets ? (safeAreaInsets.bottom / windowWidth) * 750 : 0;

		const minY = Math.max(100, safeTopRpx + 50); // 顶部最小距离，考虑安全区域
		const maxY = screenHeightRpx - buttonHeight - Math.max(100, safeBottomRpx + 50); // 底部最大距离，考虑安全区域

		newY = Math.max(minY, Math.min(maxY, newY));
		position.y = newY;
		rafId = null;
	});
};

const onTouchEnd = (e: TouchEvent) => {
	if (!isDragging.value) return;
	e.stopPropagation();

	isDragging.value = false;

	// 松开后贴合左侧
	position.x = 0;
};

let clickTimer: ReturnType<typeof setTimeout> | null = null;

const handleClick = () => {
	// 防止拖拽时触发点击
	if (clickTimer) {
		clearTimeout(clickTimer);
	}

	clickTimer = setTimeout(() => {
		if (!isDragging.value) {
			// 跳转到故障上报页面
			navigateTo('/mod/report/faultReport');
		}
		clickTimer = null;
	}, 100);
};

// 组件卸载时清理资源 - iOS优化
onUnmounted(() => {
	if (rafId) {
		cancelAnimationFrame(rafId);
		rafId = null;
	}
	if (clickTimer) {
		clearTimeout(clickTimer);
		clickTimer = null;
	}
});
</script>

<style lang="scss" scoped>
.fault-float-container {
	position: fixed;
	z-index: 999;
	cursor: move;
	user-select: none;
	/* iOS优化 */
	-webkit-user-select: none;
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;
	touch-action: none;
}

.fault-float-btn {
	background: linear-gradient(270deg, #ef4444 0%, rgba(239, 68, 68, 0.7) 100%);
	gap: 12rpx;
	padding: 6rpx 20rpx;
	border-radius: 0 24rpx 24rpx 0;
	box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	/* iOS优化 */
	user-select: none;
	-webkit-user-select: none;
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;
	transform: translateZ(0); /* 启用硬件加速 */
	will-change: transform; /* 优化动画性能 */

	&:active {
		transform: scale(1.15);
		box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
	}

	&.dragging {
		transform: scale(1.15);
		box-shadow: 0 12rpx 32rpx rgba(255, 77, 79, 0.5);
		opacity: 0.9;
	}
}

.fault-text {
	font-size: 20rpx;
	font-weight: 500;
	color: white;
	white-space: nowrap;
	line-height: 1;
}
</style>
