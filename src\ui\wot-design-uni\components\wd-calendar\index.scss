@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(calendar) {
		@include e(title) {
			color: $wot-dark-color;
		}

		:deep(.wd-calendar__arrow),
		:deep(.wd-calendar__close),
		:deep(.wd-calendar__clear) {
			color: $wot-dark-color;
		}

		@include e(range-label-item) {
			color: $wot-dark-color;

			@include when(placeholder) {
				color: $wot-dark-color-gray;
			}
		}

		@include e(range-sperator) {
			color: $wot-dark-color-gray;
		}

		:deep(.wd-calendar__cell--placeholder) {
			.wd-cell__value {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(calendar) {
	@include e(header) {
		position: relative;
		overflow: hidden;
	}

	@include e(title) {
		color: $wot-action-sheet-color;
		height: $wot-action-sheet-title-height;
		line-height: $wot-action-sheet-title-height;
		text-align: center;
		font-size: $wot-action-sheet-title-fs;
		font-weight: $wot-action-sheet-weight;
	}

	@include edeep(close) {
		position: absolute;
		top: $wot-action-sheet-close-top;
		right: $wot-action-sheet-close-right;
		color: $wot-action-sheet-close-color;
		font-size: $wot-action-sheet-close-fs;
		transform: rotate(-45deg);
		line-height: 1.1;
	}

	@include e(tabs) {
		width: 222px;
		margin: 10px auto 12px;
	}

	@include e(shortcuts) {
		padding: 20px 0;
		text-align: center;
	}

	@include edeep(tag) {
		margin-right: 8px;
	}

	@include e(view) {
		@include when(show-confirm) {
			height: 394px;

			@include when(range) {
				height: 384px;
			}
		}
	}

	@include e(range-label) {
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 14px;

		@include when(monthrange) {
			padding-bottom: 10px;
			box-shadow: 0px 4px 8px 0 rgba(0, 0, 0, 0.02);
		}
	}

	@include e(range-label-item) {
		flex: 1;
		color: rgba(0, 0, 0, 0.85);

		@include when(placeholder) {
			color: rgba(0, 0, 0, 0.25);
		}
	}

	@include e(range-sperator) {
		margin: 0 24px;
		color: rgba(0, 0, 0, 0.25);
	}

	@include e(confirm) {
		padding: 12px 25px 14px;
	}

	@include edeep(cell) {
		@include when(disabled) {
			.wd-cell__value {
				color: $wot-input-disabled-color;
				cursor: not-allowed;
			}
		}
		@include when(error) {
			.wd-cell__value {
				color: $wot-input-error-color;
			}
			:deep(.wd-calendar__arrow) {
				color: $wot-input-error-color;
			}
		}
		@include when(large) {
			.wd-calendar__arrow {
				font-size: $wot-cell-icon-size-large;
			}
		}

		@include m(placeholder) {
			.wd-cell__value {
				color: $wot-input-placeholder-color;
			}
		}
	}

	@include edeep(arrow) {
		display: block;
		font-size: $wot-cell-icon-size;
		color: $wot-cell-arrow-color;
		line-height: $wot-cell-line-height;
	}

	@include edeep(clear) {
		display: block;
		font-size: $wot-cell-icon-size;
		color: $wot-cell-clear-color;
		line-height: $wot-cell-line-height;
	}
}
