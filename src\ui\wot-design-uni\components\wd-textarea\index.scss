@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(textarea) {
		background: $wot-dark-background2;

		&::after {
			background: $wot-dark-color-gray;
		}

		@include when(not-empty) {
			&:not(.is-disabled) {
				&::after {
					background-color: $wot-dark-color;
				}
			}
		}

		@include e(value) {
			background: $wot-dark-background2;
		}

		@include e(inner) {
			color: $wot-dark-color;

			&::-webkit-input-placeholder {
				color: $wot-dark-color3;
			}
		}

		@include e(count) {
			color: $wot-dark-color3;
			background: transparent;
		}

		@include e(count-current) {
			color: $wot-dark-color;
		}

		:deep(.wd-textarea__icon),
		:deep(.wd-textarea__clear) {
			color: $wot-dark-color;
			background: transparent;
		}

		@include when(cell) {
			background-color: $wot-dark-background2;

			@include when(border) {
				@include halfPixelBorder('top', $wot-textarea-cell-padding, $wot-dark-border-color);
			}
		}

		@include when(disabled) {
			.wd-textarea__inner {
				color: $wot-dark-color-gray;
				background: transparent;
			}
		}

		@include e(label) {
			color: $wot-dark-color;
		}
	}
}

@include b(textarea) {
	position: relative;
	-webkit-tap-highlight-color: transparent;
	text-align: left;
	background: $wot-textarea-bg;
	padding: $wot-textarea-cell-padding $wot-textarea-padding;

	&::after {
		position: absolute;
		display: none;
		content: '';
		bottom: 0;
		left: 0;
		right: 0;
		height: 1px;
		background: $wot-textarea-border-color;
		transform: scaleY(0.5);
		transition: background-color 0.2s ease-in-out;
	}

	@include e(label) {
		position: relative;
		display: flex;
		width: $wot-input-cell-label-width;
		color: $wot-cell-title-color;
		margin-right: $wot-cell-padding;
		box-sizing: border-box;
		font-size: $wot-textarea-fs;
		flex-shrink: 0;

		@include when(required) {
			padding-left: 12px;

			&::after {
				position: absolute;
				left: 0;
				top: 2px;
				content: '*';
				font-size: $wot-cell-required-size;
				line-height: 1.1;
				color: $wot-cell-required-color;
			}
		}
	}

	@include e(label-inner) {
		display: inline-block;
		line-height: $wot-cell-line-height;
		font-size: $wot-textarea-fs;
	}

	@include e(prefix) {
		margin-right: $wot-textarea-icon-margin;
		font-size: $wot-textarea-fs;
		line-height: initial;

		:deep(.wd-textarea__icon) {
			margin-left: 0;
		}
	}

	@include e(suffix) {
		flex-shrink: 0;
		line-height: initial;
	}

	@include e(value) {
		position: relative;
		padding: 0;
		font-size: 0;
		background: $wot-textarea-bg;
		box-sizing: border-box;

		@include when(show-limit) {
			padding-bottom: 36px;
		}

		@include when(suffix) {
			padding-right: calc($wot-textarea-icon-size + 8px);
		}
	}

	@include e(inner) {
		padding: 0;
		width: 100%;
		font-size: $wot-textarea-fs;
		line-height: $wot-cell-line-height;
		color: $wot-textarea-color;
		outline: none;
		background: none;
		border: none;
		box-sizing: border-box;
		word-break: break-word;
		min-height: 24px;

		&::-webkit-input-placeholder {
			color: $wot-input-placeholder-color;
		}
	}

	@include e(suffix) {
		position: absolute;
		z-index: 1;
		right: 0;
		top: 0;
		bottom: 0;
	}

	@include edeep(icon) {
		margin-left: $wot-textarea-icon-margin;
		font-size: $wot-textarea-icon-size;
		color: $wot-textarea-icon-color;
		background: $wot-textarea-bg;
	}

	@include edeep(clear) {
		margin-left: $wot-textarea-icon-margin;
		font-size: $wot-textarea-icon-size;
		color: $wot-textarea-clear-color;
		vertical-align: middle;
		background: $wot-textarea-bg;
		line-height: $wot-cell-line-height;
	}

	@include e(count) {
		position: absolute;
		bottom: 8px;
		right: 0;
		font-size: $wot-textarea-count-fs;
		color: $wot-textarea-count-color;
		background: $wot-textarea-bg;
		line-height: 20px;
		display: inline-flex;
	}

	@include e(count-current) {
		color: $wot-textarea-count-current-color;

		@include when(error) {
			color: $wot-input-error-color;
		}
	}

	@include e(readonly-mask) {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 2;
		width: 100%;
		height: 100%;
	}

	@include e(error-message) {
		color: $wot-form-item-error-message-color;
		font-size: $wot-form-item-error-message-font-size;
		line-height: $wot-form-item-error-message-line-height;
		text-align: left;
		vertical-align: middle;
	}

	@include when(not-empty) {
		&:not(.is-disabled) {
			&::after {
				background-color: $wot-textarea-not-empty-border-color;
			}
		}
	}

	@include when(disabled) {
		.wd-textarea__inner {
			color: $wot-input-disabled-color;
			background: transparent;
		}
	}

	@include when(error) {
		.wd-textarea__inner {
			color: $wot-input-error-color;
			background: transparent;
		}
	}

	@include when(auto-height) {
		&:not(.is-cell) {
			padding: 5px 0;
		}

		&::after {
			display: block;
		}
	}

	@include when(no-border) {
		&::after {
			display: none;
		}
	}

	@include when(cell) {
		display: flex;
		line-height: $wot-cell-line-height;

		&.is-error::after {
			background: $wot-textarea-cell-border-color;
		}

		.wd-textarea__value {
			flex: 1;
		}

		:deep(.wd-textarea__icon) {
			display: inline-flex;
			align-items: center;
			height: $wot-textarea-cell-height;
			line-height: $wot-textarea-cell-height;
		}

		.wd-textarea__prefix {
			display: inline-block;
			margin-right: $wot-cell-icon-right;
		}

		&.wd-textarea::after {
			display: none;
		}

		.wd-textarea__suffix {
			right: 0;
		}

		@include when(center) {
			align-items: center;
		}

		@include when(border) {
			@include halfPixelBorder('top', $wot-textarea-cell-padding);
		}
	}

	@include when(large) {
		padding: $wot-textarea-cell-padding-large;

		.wd-textarea__prefix {
			font-size: $wot-textarea-fs-large;
		}

		.wd-textarea__label-inner {
			font-size: $wot-textarea-fs-large;
		}

		.wd-textarea__inner {
			font-size: $wot-textarea-fs-large;
		}

		.wd-textarea__count {
			font-size: $wot-textarea-count-fs-large;
		}

		:deep(.wd-textarea__icon),
		:deep(.wd-textarea__clear) {
			font-size: $wot-textarea-icon-size-large;
		}
	}
}
