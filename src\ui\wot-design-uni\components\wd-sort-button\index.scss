@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(sort-button) {
		@include e(wrapper) {
			color: $wot-dark-color;
		}
	}
}

@include b(sort-button) {
	display: inline-block;
	height: $wot-sort-button-height;
	line-height: $wot-sort-button-height;

	@include m(line) {
		.wd-sort-button__left {
			&::after {
				position: absolute;
				content: '';
				width: 19px;
				height: 3px;
				bottom: 6px;
				left: 50%;
				transform: translate(-50%, 0);
				background: $wot-sort-button-line-color;
				border-radius: calc($wot-sort-button-line-height / 2);
				transition: opacity 0.15s;
				opacity: 0;
			}

			&.is-active::after {
				opacity: 1;
			}
		}
	}

	@include e(wrapper) {
		font-size: $wot-sort-button-fs;
		color: $wot-sort-button-color;
		word-break: break-all;
		white-space: nowrap;
	}

	@include e(left) {
		position: relative;
		display: inline-block;
		vertical-align: middle;

		@include when(active) {
			font-weight: $wot-fw-medium;

			.wd-sort-button__right {
				justify-content: center;
			}
		}
	}

	@include e(right) {
		display: inline-block;
		min-width: 14px;
		margin-left: 2px;
		vertical-align: middle;
		line-height: 1.1;

		@include when(active) {
			:deep(.wd-sort-button__icon-up),
			:deep(.wd-sort-button__icon-down) {
				transform: scale(calc((10 / 14)));
			}
		}
	}

	@include edeep(icon-up) {
		display: block !important;
		line-height: 1.1;
		transform: scale(calc((10 / 14))) translate(0, 7px);
	}

	@include edeep(icon-down) {
		display: block !important;
		line-height: 1.1;
		transform: scale(calc((10 / 14))) translate(0, -7px);
	}
}
