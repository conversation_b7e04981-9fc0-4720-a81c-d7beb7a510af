<template>
	<view class="product-card">
		<view v-if="loading === 0" class="loading-container">
			<wd-loading size="small" />
			<text class="loading-text">商品详情加载中...</text>
		</view>
		<view v-else-if="loading === 1" class="product-content" @click="viewProduct(props.productId)">
			<!-- 商品头部信息 -->
			<view class="product-header">
				<view class="header-left">
					<wd-icon name="goods" size="32rpx" color="#409eff" />
					<text class="product-name">{{ productDetail!.name }}</text>
				</view>
				<view class="status-container">
					<wd-tag :type="productDetail!.status === 1 ? 'success' : 'danger'" size="small">
						{{ productDetail!.status === 1 ? '在售' : '已下架' }}
					</wd-tag>
				</view>
			</view>

			<!-- 商品图片和基本信息 -->
			<view class="product-body">
				<view class="product-image">
					<image v-if="productDetail!.imageUrl" :src="productDetail!.imageUrl" mode="aspectFill" lazy-load />
					<view v-else class="no-image">
						<wd-icon name="picture" size="40rpx" color="#ccc" />
						<text class="no-image-text">暂无图片</text>
					</view>
				</view>
				<view class="product-info">
					<view class="seller-info">
						<wd-icon name="shop" size="24rpx" color="#999" />
						<text class="seller-name">{{ productDetail!.sellerName || '未知卖家' }}</text>
					</view>
					<view class="price-section">
						<DIWCurrency class="price-value" :value="productDetail!.price" />
					</view>

					<view class="tags-section">
						<wd-tag v-if="productDetail!.productType === 1" type="success" size="small"> 现货 </wd-tag>
						<wd-tag v-if="productDetail!.brandName" type="primary" size="small">
							{{ productDetail!.brandName }}
						</wd-tag>
					</view>

					<view v-if="productDetail!.inventoryTon" class="inventory-section">
						<text class="inventory-label">库存:</text>
						<text class="inventory-value">{{ productDetail!.inventoryTon }} 吨</text>
					</view>
				</view>
			</view>
		</view>
		<view v-else class="error-state" @click="reloadProductDetail(props.productId)">
			<wd-icon name="warning" size="32rpx" color="#f56c6c" />
			<text class="error-text">商品加载失败，点击重试</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useProductStore } from '@/store/imProduct';

const props = defineProps<{ productId: string }>();

const { protect, parseImageFiles, navigateTo } = useFramework();

const productDetail = ref<Record<string, any> | null>(null);
const loading = ref<0 | 1 | 2>(0);
const { getProductDetail } = useProductStore();
watch(
	() => props.productId,
	() => {
		reloadProductDetail(props.productId);
	},
	{ immediate: true }
);

function reloadProductDetail(productId: string) {
	protect(async () => {
		try {
			loading.value = 0;
			const response = await getProductDetail(productId);

			// 处理图片
			if (response) {
				const imageFiles = parseImageFiles(response.imageFileIds);
				response.imageUrl = imageFiles.length > 0 ? imageFiles[0].url : '';
				response.imageList = imageFiles.map((img: any) => img.url);
			}

			productDetail.value = response;
			loading.value = 1;
		} catch (err) {
			loading.value = 2;
			console.error('Failed to load product:', err);
		}
	});
}

function viewProduct(productId: string) {
	protect(async () => {
		if (productDetail.value) {
			// 导航到商品详情页面 - 需要根据实际页面路径调整
			await navigateTo({
				url: '/mod/chat/product',
				params: { id: productId },
			});
		}
	});
}
</script>

<style scoped lang="scss">
.product-card {
	border: 1rpx solid #e0e0e0;
	border-radius: 16rpx;
	overflow: hidden;
	background-color: white;
	transition: all 0.2s;
	width: 100%;
	max-width: 470rpx;
	min-width: 400rpx;
	margin: 8rpx 0;
}

.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 48rpx 24rpx;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 16rpx;
}

.product-content {
	padding: 24rpx;
}

.product-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
	gap: 16rpx;
}

.header-left {
	display: flex;
	align-items: flex-start;
	flex: 1;
	gap: 8rpx;
	min-width: 0;
}

.product-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #409eff;
	line-height: 1.4;
	word-wrap: break-word;
	word-break: break-all;
	flex: 1;
	min-width: 0;
}

.status-container {
	flex-shrink: 0;
	margin-top: 4rpx;
}

.product-body {
	display: flex;
	gap: 24rpx;
	margin-bottom: 24rpx;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	flex-shrink: 0;
	border-radius: 12rpx;
	overflow: hidden;
	border: 1rpx solid #f0f0f0;
	background-color: #f9f9f9;

	image {
		width: 100%;
		height: 100%;
	}

	.no-image {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #ccc;
	}

	.no-image-text {
		font-size: 24rpx;
		margin-top: 8rpx;
	}
}

.product-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.seller-info {
	display: flex;
	align-items: flex-start;
	gap: 8rpx;
}

.seller-name {
	font-size: 24rpx;
	color: #333;
	line-height: 1.4;
	word-wrap: break-word;
	word-break: break-all;
	flex: 1;
	min-width: 0;
}

.price-section {
	display: flex;
	align-items: center;
	margin: 8rpx 0;

	.price-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5000;
	}
}

.tags-section {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.inventory-section {
	display: flex;
	align-items: center;
	gap: 8rpx;

	.inventory-label {
		font-size: 24rpx;
		color: #666;
	}

	.inventory-value {
		font-size: 24rpx;
		color: #409eff;
		font-weight: 500;
	}
}

.error-state {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
}

.error-text {
	font-size: 28rpx;
	color: #f56c6c;
}
</style>
