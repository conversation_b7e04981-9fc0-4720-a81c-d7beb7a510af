@use './function' as *;

/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
$wot-color-theme: var(--wot-color-theme, $default-theme) !default; // 主题色
$wot-color-white: var(--wot-color-white, rgb(255, 255, 255)) !default; // 用于mix的白色
$wot-color-black: var(--wot-color-black, rgb(0, 0, 0)) !default; // 用于mix的黑色

/* 辅助色 */
$wot-color-success: var(--wot-color-success, #34d19d) !default; // 成功色
$wot-color-warning: var(--wot-color-warning, #f0883a) !default; // 警告色
$wot-color-danger: var(--wot-color-danger, #fa4350) !default; // 危险出错色
$wot-color-purple: var(--wot-color-purple, #8268de) !default; // 紫色
$wot-color-yellow: var(--wot-color-yellow, #f0cd1d) !default; // 黄色
$wot-color-blue: var(--wot-color-blue, #2bb3ed) !default; // 蓝色
$wot-color-info: var(--wot-color-info, #909399) !default;

$wot-color-gray-1: var(--wot-color-gray-1, #f7f8fa) !default;
$wot-color-gray-2: var(--wot-color-gray-2, #f2f3f5) !default;
$wot-color-gray-3: var(--wot-color-gray-3, #ebedf0) !default;
$wot-color-gray-4: var(--wot-color-gray-4, #dcdee0) !default;
$wot-color-gray-5: var(--wot-color-gray-5, #c8c9cc) !default;
$wot-color-gray-6: var(--wot-color-gray-6, #969799) !default;
$wot-color-gray-7: var(--wot-color-gray-7, #646566) !default;
$wot-color-gray-8: var(--wot-color-gray-8, #323233) !default;

$wot-font-gray-1: var(--wot-font-gray-1, rgba(0, 0, 0, 0.9));
$wot-font-gray-2: var(--wot-font-gray-2, rgba(0, 0, 0, 0.6));
$wot-font-gray-3: var(--wot-font-gray-3, rgba(0, 0, 0, 0.4));
$wot-font-gray-4: var(--wot-font-gray-4, rgba(0, 0, 0, 0.26));

$wot-font-white-1: var(--wot-font-white-1, rgba(255, 255, 255, 1));
$wot-font-white-2: var(--wot-font-white-2, rgba(255, 255, 255, 0.55));
$wot-font-white-3: var(--wot-font-white-3, rgba(255, 255, 255, 0.35));
$wot-font-white-4: var(--wot-font-white-4, rgba(255, 255, 255, 0.22));

/* 文字颜色（默认浅色背景下 */
$wot-color-title: var(--wot-color-title, $wot-color-black) !default; // 模块标题/重要正文 000
$wot-color-content: var(--wot-color-content, #262626) !default; // 普通正文 262626
$wot-color-secondary: var(--wot-color-secondary, #595959) !default; // 次要信息，注释/补充/正文 595959
$wot-color-aid: var(--wot-color-aid, #8c8c8c) !default; // 辅助文字字号，弱化信息，引导性/不可点文字 8c8c8c
$wot-color-tip: var(--wot-color-tip, #bfbfbf) !default; // 失效、默认提示文字 bfbfbf
$wot-color-border: var(--wot-color-border, #d9d9d9) !default; // 控件边框线 d9d9d9
$wot-color-border-light: var(--wot-color-border-light, #e8e8e8) !default; // 分割线颜色 e8e8e8
$wot-color-bg: var(--wot-color-bg, #f5f5f5) !default; // 背景色、禁用填充色 f5f5f5

/* 暗黑模式 */
$wot-dark-background: var(--wot-dark-background, #131313) !default;
$wot-dark-background2: var(--wot-dark-background2, #1b1b1b) !default;
$wot-dark-background3: var(--wot-dark-background3, #141414) !default;
$wot-dark-background4: var(--wot-dark-background4, #323233) !default;
$wot-dark-background5: var(--wot-dark-background5, #646566) !default;
$wot-dark-background6: var(--wot-dark-background6, #380e08) !default;
$wot-dark-background7: var(--wot-dark-background7, #707070) !default;
$wot-dark-color: var(--wot-dark-color, $wot-color-white) !default;
$wot-dark-color2: var(--wot-dark-color2, #f2270c) !default;
$wot-dark-color3: var(--wot-dark-color3, rgba(232, 230, 227, 0.8)) !default;
$wot-dark-color-gray: var(--wot-dark-color-gray, $wot-color-secondary) !default;
$wot-dark-border-color: var(--wot-dark-border-color, #3a3a3c) !default;

/* 图形颜色 */
$wot-color-icon: var(--wot-color-icon, #d9d9d9) !default; // icon颜色
$wot-color-icon-active: var(--wot-color-icon-active, #eee) !default; // icon颜色hover
$wot-color-icon-disabled: var(--wot-color-icon-disabled, #a7a7a7) !default; // icon颜色disabled

/*----------------------------------------- Theme color. end -------------------------------------------*/

/*-------------------------------- Theme color application size.  start --------------------------------*/

/* 文字字号 */
$wot-fs-big: var(--wot-fs-big, 24px) !default; // 大型标题
$wot-fs-important: var(--wot-fs-important, 19px) !default; // 重要数据
$wot-fs-title: var(--wot-fs-title, 16px) !default; // 标题字号/重要正文字号
$wot-fs-content: var(--wot-fs-content, 14px) !default; // 普通正文
$wot-fs-secondary: var(--wot-fs-secondary, 12px) !default; // 次要信息，注释/补充/正文
$wot-fs-aid: var(--wot-fs-aid, 10px) !default; // 辅助文字字号，弱化信息，引导性/不可点文字

/* 文字字重 */
$wot-fw-medium: var(--wot-fw-medium, 500) !default; // PingFangSC-Medium
$wot-fw-semibold: var(--wot-fw-semibold, 600) !default; // PingFangSC-Semibold

/* 尺寸 */
$wot-size-side-padding: var(--wot-size-side-padding, 15px) !default; // 屏幕两边留白
$wot-size-side-padding-small: var(--wot-size-side-padding-small, 6px) !default; // 屏幕两边留白小值

/*-------------------------------- Theme color application size.  end --------------------------------*/

/* component var */

/* action-sheet */
$wot-action-sheet-weight: var(--wot-action-sheet-weight, 500) !default; // 面板字重
$wot-action-sheet-radius: var(--wot-action-sheet-radius, 16px) !default; // 面板圆角大小
$wot-action-sheet-loading-size: var(--wot-action-sheet-loading-size, 20px) !default; // loading动画尺寸
$wot-action-sheet-action-height: var(--wot-action-sheet-action-height, 48px) !default; // 单条菜单高度
$wot-action-sheet-color: var(--wot-action-sheet-color, rgba(0, 0, 0, 0.85)) !default; // 选项名称颜色
$wot-action-sheet-fs: var(--wot-action-sheet-fs, $wot-fs-title) !default; // 选项名称字号
$wot-action-sheet-active-color: var(--wot-action-sheet-active-color, $wot-color-bg) !default; // 点击高亮颜色
$wot-action-sheet-subname-fs: var(--wot-action-sheet-subname-fs, $wot-fs-secondary) !default; // 描述信息字号
$wot-action-sheet-subname-color: var(--wot-action-sheet-subname-color, rgba(0, 0, 0, 0.45)) !default; // 描述信息颜色
$wot-action-sheet-disabled-color: var(--wot-action-sheet-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用颜色
$wot-action-sheet-bg: var(--wot-action-sheet-bg, $wot-color-white) !default; // 菜单容器颜色（取消按钮上方的颜色）
$wot-action-sheet-title-height: var(--wot-action-sheet-title-height, 64px) !default; // 标题高度
$wot-action-sheet-title-fs: var(--wot-action-sheet-title-fs, $wot-fs-title) !default; // 标题字号
$wot-action-sheet-close-fs: var(--wot-action-sheet-close-fs, $wot-fs-title) !default; // 关闭按钮大小
$wot-action-sheet-close-color: var(--wot-action-sheet-close-color, rgba(0, 0, 0, 0.65)) !default; // 关闭按钮颜色
$wot-action-sheet-close-top: var(--wot-action-sheet-close-top, 25px) !default; // 关闭按钮距离标题顶部距离
$wot-action-sheet-close-right: var(--wot-action-sheet-close-right, 15px) !default; // 关闭按钮距离标题右侧距离
$wot-action-sheet-cancel-color: var(--wot-action-sheet-cancel-color, #131415) !default; // 取消按钮颜色
$wot-action-sheet-cancel-height: var(--wot-action-sheet-cancel-height, 44px) !default; // 取消按钮高度
$wot-action-sheet-cancel-bg: var(--wot-action-sheet-cancel-bg, rgba(240, 240, 240, 1)) !default; // 取消按钮背景色
$wot-action-sheet-cancel-radius: var(--wot-action-sheet-cancel-radius, 22px) !default; // 取消按钮圆角大小
$wot-action-sheet-panel-padding: var(--wot-action-sheet-panel-padding, 12px 0 11px) !default; // 自定义面板内边距大小
$wot-action-sheet-panel-img-fs: var(--wot-action-sheet-panel-img-fs, 40px) !default; // 自定义面板图片大小
$wot-action-sheet-panel-img-radius: var(--wot-action-sheet-panel-img-radius, 4px) !default; // 自定义面板图片圆角大小

/* badge */
$wot-badge-bg: var(--wot-badge-bg, $wot-color-danger) !default; // 背景填充颜色
$wot-badge-color: var(--wot-badge-color, #fff) !default; // 文字颜色
$wot-badge-fs: var(--wot-badge-fs, 12px) !default; // 文字字号
$wot-badge-padding: var(--wot-badge-padding, 0 5px) !default; // padding
$wot-badge-height: var(--wot-badge-height, 16px) !default; // 高度
$wot-badge-primary: var(--wot-badge-primary, $wot-color-theme) !default;
$wot-badge-success: var(--wot-badge-success, $wot-color-success) !default;
$wot-badge-warning: var(--wot-badge-warning, $wot-color-warning) !default;
$wot-badge-danger: var(--wot-badge-danger, $wot-color-danger) !default;
$wot-badge-info: var(--wot-badge-info, $wot-color-info) !default;
$wot-badge-dot-size: var(--wot-badge-dot-size, 6px) !default; // dot 类型大小
$wot-badge-border: var(--wot-badge-border, 2px solid $wot-badge-color) !default; // 边框样式

/* button */
$wot-button-disabled-opacity: var(--wot-button-disabled-opacity, 0.6) !default; // button禁用透明度
$wot-button-small-height: var(--wot-button-small-height, 28px) !default; // 小型按钮高度
$wot-button-small-padding: var(--wot-button-small-padding, 0 12px) !default; // 小型按钮padding
$wot-button-small-fs: var(--wot-button-small-fs, $wot-fs-secondary) !default; // 小型按钮字号
$wot-button-small-radius: var(--wot-button-small-radius, 2px) !default; // 小型按钮圆角大小
$wot-button-small-loading: var(--wot-button-small-loading, 14px) !default; // 小型按钮loading图标大小
$wot-button-medium-height: var(--wot-button-medium-height, 36px) !default; // 中型按钮高度
$wot-button-medium-padding: var(--wot-button-medium-padding, 0 16px) !default; // 中型按钮padding
$wot-button-medium-fs: var(--wot-button-medium-fs, $wot-fs-content) !default; // 中型按钮字号
$wot-button-medium-radius: var(--wot-button-medium-radius, 4px) !default; // 中型按钮圆角大小
$wot-button-medium-loading: var(--wot-button-medium-loading, 18px) !default; // 中型按钮loading图标大小
$wot-button-medium-box-shadow-size: var(--wot-button-medium-box-shadow-size, 0px 2px 4px 0px) !default; // 中尺寸阴影尺寸
$wot-button-large-height: var(--wot-button-large-height, 44px) !default; // 大型按钮高度
$wot-button-large-padding: var(--wot-button-large-padding, 0 36px) !default; // 大型按钮padding
$wot-button-large-fs: var(--wot-button-large-fs, $wot-fs-title) !default; // 大型按钮字号
$wot-button-large-radius: var(--wot-button-large-radius, 8px) !default; // 大型按钮圆角大小
$wot-button-large-loading: var(--wot-button-large-loading, 24px) !default; // 大小按钮loading图标大小
$wot-button-large-box-shadow-size: var(--wot-button-large-box-shadow-size, 0px 4px 8px 0px) !default; // 大尺寸阴影尺寸
$wot-button-icon-fs: var(--wot-button-icon-fs, 1.18em) !default; // 带图标的按钮的图标大小
$wot-button-icon-size: var(--wot-button-icon-size, 40px) !default; // icon 类型按钮尺寸
$wot-button-icon-color: var(--wot-button-icon-color, rgba(0, 0, 0, 0.65)) !default; // icon 类型按钮颜色
$wot-button-icon-disabled-color: var(--wot-button-icon-disabled-color, $wot-color-icon-disabled) !default; // icon 类型按钮禁用颜色
$wot-button-normal-color: var(--wot-button-normal-color, $wot-color-title) !default; // 文字颜色
$wot-button-normal-disabled-color: var(--wot-button-normal-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 默认按钮禁用文字色
$wot-button-plain-bg-color: var(--wot-button-plain-bg-color, $wot-color-white) !default; // 幽灵按钮背景色
$wot-button-primary-color: var(--wot-button-primary-color, $wot-color-white) !default; // 主要按钮颜色
$wot-button-primary-bg-color: var(--wot-button-primary-bg-color, $wot-color-theme) !default; // 主要按钮背景颜色
$wot-button-success-color: var(--wot-button-success-color, $wot-color-white) !default; // 成功按钮文字颜色
$wot-button-success-bg-color: var(--wot-button-success-bg-color, $wot-color-success) !default; // 成功按钮颜色
$wot-button-info-color: var(--wot-button-info-color, $wot-color-title) !default; // 信息按钮颜色
$wot-button-info-bg-color: var(--wot-button-info-bg-color, #f0f0f0) !default; // 信息按钮背景颜色
$wot-button-info-plain-border-color: var(--wot-button-info-plain-border-color, rgba(0, 0, 0, 0.45)) !default; // 信息按钮禁用颜色
$wot-button-info-plain-normal-color: var(--wot-button-info-plain-normal-color, rgba(0, 0, 0, 0.85)) !default; // 信息幽灵按钮默认颜色
$wot-button-warning-color: var(--wot-button-warning-color, $wot-color-white) !default; // 警告按钮字体颜色
$wot-button-warning-bg-color: var(--wot-button-warning-bg-color, $wot-color-warning) !default; // 警告按钮背景颜色
$wot-button-error-color: var(--wot-button-error-color, $wot-color-white) !default; // 错误按钮颜色
$wot-button-error-bg-color: var(--wot-button-error-bg-color, $wot-color-danger) !default; // 错误按钮背景颜色
$wot-button-text-hover-opacity: var(--wot-button-text-hover-opacity, 0.7) !default; // 文字button激活时透明度

/* cell */
$wot-cell-padding: var(--wot-cell-padding, $wot-size-side-padding) !default; // cell 左右padding距离
$wot-cell-line-height: var(--wot-cell-line-height, 24px) !default; // 行高

$wot-cell-group-title-fs: var(--wot-cell-group-title-fs, $wot-fs-title) !default; // 组标题字号
$wot-cell-group-padding: var(--wot-cell-group-padding, 13px $wot-cell-padding) !default; // 组padding
$wot-cell-group-title-color: var(--wot-cell-group-title-color, rgba(0, 0, 0, 0.85)) !default; // 组标题文字颜色
$wot-cell-group-value-fs: var(--wot-cell-group-value-fs, $wot-fs-content) !default; // 组值字号
$wot-cell-group-value-color: var(--wot-cell-group-value-color, $wot-color-content) !default; // 组值文字颜色

$wot-cell-wrapper-padding: var(--wot-cell-wrapper-padding, 10px) !default; // cell 容器padding
$wot-cell-wrapper-padding-large: var(--wot-cell-wrapper-padding-large, 12px) !default; // large类型cell容器padding

$wot-cell-wrapper-padding-with-label: var(--wot-cell-wrapper-padding-with-label, 16px) !default; // cell 容器上下padding（有label情况下）
$wot-cell-icon-right: var(--wot-cell-icon-right, 4px) !default; // 图标距离右边缘
$wot-cell-icon-size: var(--wot-cell-icon-size, 16px) !default; // 图标大小
$wot-cell-title-fs: var(--wot-cell-title-fs, 14px) !default; // 标题字号
$wot-cell-title-color: var(--wot-cell-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题文字颜色
$wot-cell-label-fs: var(--wot-cell-label-fs, 12px) !default; // 描述信息字号
$wot-cell-label-color: var(--wot-cell-label-color, rgba(0, 0, 0, 0.45)) !default; // 描述信息文字颜色
$wot-cell-value-fs: var(--wot-cell-value-fs, 14px) !default; // 右侧内容字号
$wot-cell-value-fs-large: var(--wot-cell-value-fs-large, 16px) !default; // 大尺寸右侧内容字号
$wot-cell-value-color: var(--wot-cell-value-color, rgba(0, 0, 0, 0.85)) !default; // 右侧内容文字颜色
$wot-cell-arrow-size: var(--wot-cell-arrow-size, 18px) !default; // 右箭头大小
$wot-cell-arrow-color: var(--wot-cell-arrow-color, rgba(0, 0, 0, 0.25)) !default; // 右箭头颜色
$wot-cell-clear-color: var(--wot-cell-clear-color, #585858) !default; // 清空按钮颜色
$wot-cell-tap-bg: var(--wot-cell-tap-bg, rgba(0, 0, 0, 0.06)) !default; // 点击态背景色

$wot-cell-title-fs-large: var(--wot-cell-title-fs-large, 16px) !default; // 大尺寸标题字号
$wot-cell-label-fs-large: var(--wot-cell-label-fs-large, 14px) !default; // 描述信息字号
$wot-cell-icon-size-large: var(--wot-cell-icon-size-large, 18px) !default; // 图标大小

$wot-cell-required-color: var(--wot-cell-required-color, $wot-color-danger) !default; // 要求必填*颜色
$wot-cell-required-size: var(--wot-cell-required-size, 18px) !default; // 必填*字号
$wot-cell-vertical-top: var(--wot-cell-vertical-top, 16px) !default; // 表单类型-上下结构的间距

/* calendar */
$wot-calendar-fs: var(--wot-calendar-fs, 16px) !default;
$wot-calendar-panel-padding: var(--wot-calendar-panel-padding, 0 12px) !default;
$wot-calendar-panel-title-fs: var(--wot-calendar-panel-title-fs, 14px) !default;
$wot-calendar-panel-title-color: var(--wot-calendar-panel-title-color, rgba(0, 0, 0, 0.85)) !default;
$wot-calendar-week-color: var(--wot-calendar-week-color, rgba(0, 0, 0, 0.85)) !default;
$wot-calendar-week-height: var(--wot-calendar-week-height, 36px) !default;
$wot-calendar-week-fs: var(--wot-calendar-week-fs, 12px) !default;
$wot-calendar-day-fs: var(--wot-calendar-day-fs, 16px) !default;
$wot-calendar-day-color: var(--wot-calendar-day-color, rgba(0, 0, 0, 0.85)) !default;
$wot-calendar-day-fw: var(--wot-calendar-day-fw, 500) !default;
$wot-calendar-day-height: var(--wot-calendar-day-height, 64px) !default;
$wot-calendar-month-width: var(--wot-calendar-month-width, 50px) !default;
$wot-calendar-active-color: var(--wot-calendar-active-color, $wot-color-theme) !default;
$wot-calendar-selected-color: var(--wot-calendar-selected-color, $wot-color-white) !default;
$wot-calendar-disabled-color: var(--wot-calendar-disabled-color, rgba(0, 0, 0, 0.25)) !default;
$wot-calendar-range-color: var(--wot-calendar-range-color, rgba(#4d80f0, 0.09)) !default;
$wot-calendar-active-border: var(--wot-calendar-active-border, 8px) !default;
$wot-calendar-info-fs: var(--wot-calendar-info-fs, 10px) !default;
$wot-calendar-item-margin-bottom: var(--wot-calendar-item-margin-bottom, 4px) !default;

/* checkbox */
$wot-checkbox-margin: var(--wot-checkbox-margin, 10px) !default; // 多个复选框距离
$wot-checkbox-bg: var(--wot-checkbox-bg, $wot-color-white) !default; // 多个复选框距离
$wot-checkbox-label-margin: var(--wot-checkbox-label-margin, 9px) !default; // 右侧文字与左侧图标距离
$wot-checkbox-size: var(--wot-checkbox-size, 16px) !default; // 左侧图标尺寸
$wot-checkbox-icon-size: var(--wot-checkbox-icon-size, 14px) !default; // 左侧图标尺寸
$wot-checkbox-border-color: var(--wot-checkbox-border-color, #dcdcdc) !default; // 左侧图标边框颜色
$wot-checkbox-check-color: var(--wot-checkbox-check-color, $wot-color-white) !default; // 左侧图标边框颜色
$wot-checkbox-label-fs: var(--wot-checkbox-label-fs, 14px) !default; // 右侧文字字号
$wot-checkbox-label-color: var(--wot-checkbox-label-color, rgba(0, 0, 0, 0.85)) !default; // 右侧文字颜色
$wot-checkbox-checked-color: var(--wot-checkbox-checked-color, $wot-color-theme) !default; // 选中颜色

$wot-checkbox-disabled-color: var(--wot-checkbox-disabled-color, rgba(0, 0, 0, 0.04)) !default; // 禁用背景颜色
$wot-checkbox-disabled-label-color: var(--wot-checkbox-disabled-label-color, rgba(0, 0, 0, 0.25)) !default; // 禁用文字颜色
$wot-checkbox-disabled-check-color: var(--wot-checkbox-disabled-check-color, rgba(0, 0, 0, 0.15)) !default; // 禁用图标颜色
$wot-checkbox-disabled-check-bg: var(--wot-checkbox-disabled-check-bg, rgba(0, 0, 0, 0.15)) !default; // 禁用边框背景颜色
$wot-checkbox-square-radius: var(--wot-checkbox-square-radius, 4px) !default; // 方型圆角大小

$wot-checkbox-large-size: var(--wot-checkbox-large-size, 18px) !default; // 左侧图标尺寸
$wot-checkbox-large-label-fs: var(--wot-checkbox-large-label-fs, 16px) !default; // 右侧文字字号

$wot-checkbox-button-height: var(--wot-checkbox-button-height, 32px) !default; // 按钮模式复选框高
$wot-checkbox-button-min-width: var(--wot-checkbox-button-min-width, 78px) !default; // 按钮模式最小宽
$wot-checkbox-button-radius: var(--wot-checkbox-button-radius, 16px) !default; // 按钮圆角大小
$wot-checkbox-button-bg: var(--wot-checkbox-button-bg, rgba(0, 0, 0, 0.04)) !default; // 按钮模式背景颜色
$wot-checkbox-button-font-size: var(--wot-checkbox-button-font-size, 14px) !default; // 按钮模式字号
$wot-checkbox-button-border: var(--wot-checkbox-button-border, #f5f5f5) !default; // 按钮边框颜色
$wot-checkbox-button-disabled-border: var(--wot-checkbox-button-disabled-border, rgba(0, 0, 0, 0.15)) !default; // 按钮禁用边框颜色

/* collapse */
$wot-collapse-side-padding: var(--wot-collapse-side-padding, $wot-size-side-padding) !default; // 左右间距
$wot-collapse-body-padding: var(--wot-collapse-body-padding, 14px $wot-size-side-padding) !default; // body padding
$wot-collapse-header-padding: var(--wot-collapse-header-padding, 13px $wot-size-side-padding) !default; // 头部padding
$wot-collapse-title-color: var(--wot-collapse-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题颜色
$wot-collapse-title-fs: var(--wot-collapse-title-fs, 16px) !default; // 标题字号
$wot-collapse-arrow-size: var(--wot-collapse-arrow-size, 18px) !default; // 箭头大小
$wot-collapse-arrow-color: var(--wot-collapse-arrow-color, #d8d8d8) !default; // 箭头颜色
$wot-collapse-body-fs: var(--wot-collapse-body-fs, 14px) !default; // 内容字号
$wot-collapse-body-color: var(--wot-collapse-body-color, rgba(0, 0, 0, 0.65)) !default; // 内容颜色
$wot-collapse-disabled-color: var(--wot-collapse-disabled-color, rgba(0, 0, 0, 0.15)) !default; // 禁用颜色
$wot-collapse-retract-fs: var(--wot-collapse-retract-fs, 14px) !default; // 更多 字号
$wot-collapse-more-color: var(--wot-collapse-more-color, $wot-color-theme) !default; // 更多 颜色

/* divider */
$wot-divider-padding: var(--wot-divider-padding, 0 $wot-size-side-padding) !default; // 两边间距
$wot-divider-margin: var(--wot-divider-margin, 16px 0) !default; // 上下间距
$wot-divider-color: var(--wot-divider-color, rgba(0, 0, 0, 0.45)) !default; // 字体颜色
$wot-divider-line-color: var(--wot-divider-line-color, currentColor) !default; // 线条颜色
$wot-divider-line-height: var(--wot-divider-line-height, 1px) !default; // 线条高度
$wot-divider-fs: var(--wot-divider-fs, 14px) !default; // 字体大小
$wot-divider-content-left-width: var(--wot-divider-content-left-width, 10%) !default; // 左侧内容宽度
$wot-divider-content-left-margin: var(--wot-divider-content-left-margin, 12px) !default; // 左侧内容距离线距离
$wot-divider-content-right-margin: var(--wot-divider-content-right-margin, 12px) !default; // 右侧内容距离线距离
$wot-divider-content-right-width: var(--wot-divider-content-right-width, 10%) !default; // 右侧内容宽度
$wot-divider-vertical-height: var(--wot-divider-vertical-height, 16px) !default; // 垂直分割线高度
$wot-divider-vertical-content-margin: var(--wot-divider-vertical-content-margin, 0 8px) !default; // 垂直分割线内容间距
$wot-divider-vertical-line-width: var(--wot-divider-vertical-line-width, 1px) !default; // 线条高度

/* drop-menu */
$wot-drop-menu-height: var(--wot-drop-menu-height, 48px) !default; // 展示选中项的高度
$wot-drop-menu-color: var(--wot-drop-menu-color, $wot-color-content) !default; // 展示选中项的颜色
$wot-drop-menu-fs: var(--wot-drop-menu-fs, $wot-fs-content) !default; // 展示选中项的字号
$wot-drop-menu-arrow-fs: var(--wot-drop-menu-arrow-fs, $wot-fs-content) !default; // 箭头图标大小

$wot-drop-menu-side-padding: var(--wot-drop-menu-side-padding, $wot-size-side-padding) !default; // 两边留白间距
$wot-drop-menu-disabled-color: var(--wot-drop-menu-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用颜色
$wot-drop-menu-item-height: var(--wot-drop-menu-item-height, 48px) !default; // 选项高度
$wot-drop-menu-item-color: var(--wot-drop-menu-item-color, $wot-color-content) !default; // 选项颜色
$wot-drop-menu-item-fs: var(--wot-drop-menu-item-fs, $wot-fs-content) !default; // 选项字号
$wot-drop-menu-item-color-active: var(--wot-drop-menu-item-color-active, $wot-color-theme) !default; // 选中颜色
$wot-drop-menu-item-color-tip: var(--wot-drop-menu-item-color-tip, rgba(0, 0, 0, 0.45)) !default; // 提示文字颜色
$wot-drop-menu-item-fs-tip: var(--wot-drop-menu-item-fs-tip, $wot-fs-secondary) !default; // 提示文字字号
$wot-drop-menu-option-check-size: var(--wot-drop-menu-option-check-size, 20px) !default; // check 图标大小
$wot-drop-menu-line-color: var(--wot-drop-menu-line-color, $wot-color-theme) !default; // 下划线颜色
$wot-drop-menu-line-height: var(--wot-drop-menu-line-height, 3px) !default; // 下划线高度

/* input-number */
$wot-input-number-color: var(--wot-input-number-color, #262626) !default; // 文字颜色
$wot-input-number-border-color: var(--wot-input-number-border-color, #e8e8e8) !default; // 边框颜色
$wot-input-number-disabled-color: var(--wot-input-number-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用颜色
$wot-input-number-height: var(--wot-input-number-height, 24px) !default; // 加减号按钮高度
$wot-input-number-btn-width: var(--wot-input-number-btn-width, 26px) !default; // 加减号按钮宽度
$wot-input-number-input-width: var(--wot-input-number-input-width, 36px) !default; // 输入框宽度
$wot-input-number-radius: var(--wot-input-number-radius, 4px) !default; // 加减号按钮圆角大小
$wot-input-number-fs: var(--wot-input-number-fs, 12px) !default; // 输入框字号
$wot-input-number-icon-size: var(--wot-input-number-icon-size, 14px) !default; // 加减号图标大小
$wot-input-number-icon-color: var(--wot-input-number-icon-color, rgba(0, 0, 0, 0.65)) !default; // icon颜色

/* input */
$wot-input-padding: var(--wot-input-padding, $wot-size-side-padding) !default; // input 左右padding距离
$wot-input-border-color: var(--wot-input-border-color, #dadada) !default; // 无label边框颜色
$wot-input-not-empty-border-color: var(--wot-input-not-empty-border-color, #262626) !default; // 输入框有值时 无label边框颜色
$wot-input-fs: var(--wot-input-fs, $wot-cell-title-fs) !default; // 字号
$wot-input-fs-large: var(--wot-input-fs-large, $wot-cell-title-fs-large) !default; // 大尺寸字号
$wot-input-icon-margin: var(--wot-input-icon-margin, 8px) !default; // 图标距离
$wot-input-color: var(--wot-input-color, #262626) !default; // 文字颜色
$wot-input-placeholder-color: var(--wot-input-placeholder-color, #bfbfbf) !default; // 占位符颜色
$wot-input-disabled-color: var(--wot-input-disabled-color, #d9d9d9) !default; // 输入框禁用颜色
$wot-input-error-color: var(--wot-input-error-color, $wot-color-danger) !default; // 输入框错误颜色
$wot-input-icon-color: var(--wot-input-icon-color, #bfbfbf) !default; // 图标颜色
$wot-input-clear-color: var(--wot-input-clear-color, #585858) !default; // 关闭按钮颜色
$wot-input-count-color: var(--wot-input-count-color, #bfbfbf) !default; // 计数文字颜色
$wot-input-count-current-color: var(--wot-input-count-current-color, #262626) !default; // 当前长度颜色
$wot-input-bg: var(--wot-input-bg, $wot-color-white) !default; // 默认背景颜色

$wot-input-cell-bg: var(--wot-input-cell-bg, $wot-color-white) !default; // cell 类型背景色
$wot-input-cell-border-color: var(--wot-input-cell-border-color, $wot-color-border-light) !default; // cell 类型边框颜色
$wot-input-cell-padding: var(--wot-input-cell-padding, 10px) !default; // cell 容器padding
$wot-input-cell-padding-large: var(--wot-input-cell-padding-large, 12px) !default; // large类型cell容器padding
$wot-input-cell-height: var(--wot-input-cell-height, 24px) !default; // cell 高度
$wot-input-cell-label-width: var(--wot-input-cell-label-width, 33%) !default; // cell 下 label 的宽度
$wot-input-inner-height: var(--wot-input-inner-height, 34px) !default; // 非cell和textarea下的高度
$wot-input-inner-height-no-border: var(--wot-input-inner-height-no-border, 24px) !default; // 无边框下的高度
$wot-input-count-fs: var(--wot-input-count-fs, 14px) !default; // 计数字号
$wot-input-count-fs-large: var(--wot-input-count-fs-large, 14px) !default; // 大尺寸计数字号
$wot-input-icon-size: var(--wot-input-icon-size, 16px) !default; // 图标大小
$wot-input-icon-size-large: var(--wot-input-icon-size-large, 18px) !default; // 大尺寸图标大小

/* textarea */
$wot-textarea-padding: var(--wot-textarea-padding, $wot-size-side-padding) !default; // textarea 左右padding距离
$wot-textarea-border-color: var(--wot-textarea-border-color, #dadada) !default; // 无label边框颜色
$wot-textarea-not-empty-border-color: var(--wot-textarea-not-empty-border-color, #262626) !default; // 输入框有值时 无label边框颜色
$wot-textarea-fs: var(--wot-textarea-fs, $wot-cell-title-fs) !default; // 字号
$wot-textarea-fs-large: var(--wot-textarea-fs-large, $wot-cell-title-fs-large) !default; // 大尺寸字号
$wot-textarea-icon-margin: var(--wot-textarea-icon-margin, 8px) !default; // 图标距离
$wot-textarea-color: var(--wot-textarea-color, #262626) !default; // 文字颜色
$wot-textarea-icon-color: var(--wot-textarea-icon-color, #bfbfbf) !default; // 图标颜色
$wot-textarea-clear-color: var(--wot-textarea-clear-color, #585858) !default; // 关闭按钮颜色
$wot-textarea-count-color: var(--wot-textarea-count-color, #bfbfbf) !default; // 计数文字颜色
$wot-textarea-count-current-color: var(--wot-textarea-count-current-color, #262626) !default; // 当前长度颜色
$wot-textarea-bg: var(--wot-textarea-bg, $wot-color-white) !default; // 默认背景颜色
$wot-textarea-cell-border-color: var(--wot-textarea-cell-border-color, $wot-color-border-light) !default; // cell 类型边框颜色
$wot-textarea-cell-padding: var(--wot-textarea-cell-padding, 10px) !default; // cell 容器padding
$wot-textarea-cell-padding-large: var(--wot-textarea-cell-padding-large, 12px) !default; // large类型cell容器padding
$wot-textarea-cell-height: var(--wot-textarea-cell-height, 24px) !default; // cell 高度
$wot-textarea-count-fs: var(--wot-textarea-count-fs, 14px) !default; // 计数字号
$wot-textarea-count-fs-large: var(--wot-textarea-count-fs-large, 14px) !default; // 大尺寸计数字号
$wot-textarea-icon-size: var(--wot-textarea-icon-size, 16px) !default; // 图标大小
$wot-textarea-icon-size-large: var(--wot-textarea-icon-size-large, 18px) !default; // 大尺寸图标大小

/* loadmore */
$wot-loadmore-height: var(--wot-loadmore-height, 48px) !default; // 高度
$wot-loadmore-color: var(--wot-loadmore-color, rgba(0, 0, 0, 0.45)) !default; // 颜色
$wot-loadmore-fs: var(--wot-loadmore-fs, 14px) !default; // 字号
$wot-loadmore-error-color: var(--wot-loadmore-error-color, $wot-color-theme) !default; // 点击重试颜色
$wot-loadmore-refresh-fs: var(--wot-loadmore-refresh-fs, $wot-fs-title) !default; // refresh图标字号
$wot-loadmore-loading-size: var(--wot-loadmore-loading-size, $wot-fs-title) !default; // loading尺寸

/* message-box */
$wot-message-box-width: var(--wot-message-box-width, 300px) !default; // 宽度
$wot-message-box-bg: var(--wot-message-box-bg, $wot-color-white) !default; // 默认背景颜色
$wot-message-box-radius: var(--wot-message-box-radius, 16px) !default; // 圆角大小
$wot-message-box-padding: var(--wot-message-box-padding, 25px 24px 0) !default; // 主体内容padding
$wot-message-box-title-fs: var(--wot-message-box-title-fs, 16px) !default; // 标题字号
$wot-message-box-title-color: var(--wot-message-box-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题颜色
$wot-message-box-content-fs: var(--wot-message-box-content-fs, 14px) !default; // 内容字号
$wot-message-box-content-color: var(--wot-message-box-content-color, #666666) !default; // 内容颜色
$wot-message-box-content-max-height: var(--wot-message-box-content-max-height, 264px) !default; // 内容最大高度
$wot-message-box-content-scrollbar-width: var(--wot-message-box-content-scrollbar-width, 4px) !default; // 内容滚动条宽度
$wot-message-box-content-scrollbar-color: var(--wot-message-box-content-scrollbar-color, rgba(0, 0, 0, 0.1)) !default; // 内容滚动条颜色
$wot-message-box-input-error-color: var(--wot-message-box-input-error-color, $wot-input-error-color) !default; // 输入框错误颜色

/* notice-bar */
$wot-notice-bar-fs: var(--wot-notice-bar-fs, 12px) !default; // 字号
$wot-notice-bar-line-height: var(--wot-notice-bar-line-height, 18px) !default; // 行高
$wot-notice-bar-border-radius: var(--wot-notice-bar-border-radius, 8px) !default; // 圆角
$wot-notice-bar-padding: var(--wot-notice-bar-padding, 9px 20px 9px 15px) !default; // 非换行下的padding
$wot-notice-bar-warning-bg: var(--wot-notice-bar-warning-bg, #fff6c8) !default; // 背景色
$wot-notice-bar-info-bg: var(--wot-notice-bar-info-bg, #f4f9ff) !default; // 背景色
$wot-notice-bar-danger-bg: var(--wot-notice-bar-danger-bg, #feeced) !default; // 背景色
$wot-notice-bar-warning-color: var(--wot-notice-bar-warning-color, $wot-color-warning) !default; // 文字和图标颜色
$wot-notice-bar-info-color: var(--wot-notice-bar-info-color, $wot-color-theme) !default; // 文字和图标颜色
$wot-notice-bar-danger-color: var(--wot-notice-bar-danger-color, $wot-color-danger) !default; // 文字和图标颜色
$wot-notice-bar-prefix-size: var(--wot-notice-bar-prefix-size, 18px) !default; // 图标大小
$wot-notice-bar-close-bg: var(--wot-notice-bar-close-bg, rgba(0, 0, 0, 0.15)) !default; // 右侧关闭按钮背景颜色
$wot-notice-bar-close-size: var(--wot-notice-bar-close-size, 18px) !default; // 右侧关闭按钮背景颜色
$wot-notice-bar-close-color: var(--wot-notice-bar-close-color, $wot-color-white) !default; // 右侧关闭按钮颜色
$wot-notice-bar-wrap-padding: var(--wot-notice-bar-wrap-padding, 14px $wot-size-side-padding) !default; // 换行下的padding

/* pagination */
$wot-pagination-content-padding: var(--wot-pagination-content-padding, 10px 15px) !default;
$wot-pagination-message-padding: var(--wot-pagination-message-padding, 1px 0 16px 0) !default;
$wot-pagination-message-fs: var(--wot-pagination-message-fs, 12px) !default;
$wot-pagination-message-color: var(--wot-pagination-message-color, rgba(0, 0, 0, 0.69)) !default;
$wot-pagination-nav-border: var(--wot-pagination-nav-border, 1px solid rgba(0, 0, 0, 0.45)) !default;
$wot-pagination-nav-border-radius: var(--wot-pagination-nav-border-radius, 16px) !default;
$wot-pagination-nav-fs: var(--wot-pagination-nav-fs, 12px) !default;
$wot-pagination-nav-width: var(--wot-pagination-nav-width, 60px) !default;
$wot-pagination-nav-color: var(--wot-pagination-nav-color, rgba(0, 0, 0, 0.85)) !default;
$wot-pagination-nav-content-fs: var(--wot-pagination-nav-content-fs, 12px) !default;
$wot-pagination-nav-sepatator-padding: var(--wot-pagination-nav-sepatator-padding, 0 4px) !default;
$wot-pagination-nav-current-color: var(--wot-pagination-nav-current-color, $wot-color-theme) !default;
$wot-pagination-icon-size: var(--wot-pagination-icon-size, $wot-fs-content) !default;

/* picker */
$wot-picker-toolbar-height: var(--wot-picker-toolbar-height, 54px) !default; // toolbar 操作条的高度
$wot-picker-action-height: var(--wot-picker-action-height, 16px) !default; // toolbar 操作条的高度
$wot-picker-toolbar-finish-color: var(--wot-picker-toolbar-finish-color, $wot-color-theme) !default; // toolbar 操作条完成按钮的颜色
$wot-picker-toolbar-cancel-color: var(--wot-picker-toolbar-cancel-color, #666666) !default; // toolbar 操作条的边框颜色
$wot-picker-toolbar-fs: var(--wot-picker-toolbar-fs, $wot-fs-title) !default; // toolbar 操作条的字号
$wot-picker-toolbar-title-color: var(--wot-picker-toolbar-title-color, rgba(0, 0, 0, 0.85)) !default; // toolbar 操作台的标题颜色
$wot-picker-column-fs: var(--wot-picker-column-fs, 16px) !default; // 选择器选项的字号
$wot-picker-bg: var(--wot-picker-bg, $wot-color-white) !default; // 选择器选项的字号
$wot-picker-column-active-fs: var(--wot-picker-column-active-fs, 18px) !default; // 选择器选项被选中的字号
$wot-picker-column-color: var(--wot-picker-column-color, rgba(0, 0, 0, 0.85)) !default; // 选择器选项的颜色
$wot-picker-column-height: var(--wot-picker-column-height, 210px) !default; // 列高 滚筒外部的高度
$wot-picker-column-item-height: var(--wot-picker-column-item-height, 35px) !default; // 列高 滚筒外部的高度
$wot-picker-column-select-bg: var(--wot-picker-column-select-bg, #f5f5f5) !default;
$wot-picker-loading-button-color: var(--wot-picker-loading-button-color, rgba(0, 0, 0, 0.25)) !default; // loading 背景颜色
$wot-picker-column-padding: var(--wot-picker-column-padding, 0 $wot-size-side-padding-small) !default; // 选项内间距

$wot-picker-column-disabled-color: var(--wot-picker-column-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 选择器选项禁用的颜色
$wot-picker-mask: var(--wot-picker-mask, linear-gradient(180deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.25)))
	linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.25)) !default; // 上下阴影
$wot-picker-loading-bg: var(--wot-picker-loading-bg, rgba($wot-color-white, 0.8)) !default; // loading 背景颜色
$wot-picker-region-separator-color: var(--wot-picker-region-separator-color, rgba(0, 0, 0, 0.65)) !default; // 区域选择文字颜色
$wot-picker-cell-arrow-size-large: var(--wot-picker-cell-arrow-size-large, $wot-cell-icon-size) !default; // cell 类型的大尺寸 右侧icon尺寸

$wot-picker-region-color: var(--wot-picker-region-color, rgba(0, 0, 0, 0.45)) !default; // 区域选择文字颜色
$wot-picker-region-bg-active-color: var(--wot-picker-region-bg-active-color, $wot-color-theme) !default; // 区域选择激活选中背景颜色

$wot-picker-region-fs: var(--wot-picker-region-fs, 14px) !default; // 区域选择文字字号

/* col-picker */
$wot-col-picker-selected-height: var(--wot-col-picker-selected-height, 44px) !default; // 弹框顶部值高度
$wot-col-picker-selected-padding: var(--wot-col-picker-selected-padding, 0 16px) !default; // 弹框顶部值左右间距
$wot-col-picker-selected-fs: var(--wot-col-picker-selected-fs, 14px) !default; // 弹框顶部值字号
$wot-col-picker-selected-color: var(--wot-col-picker-selected-color, rgba(0, 0, 0, 0.85)) !default; // 弹框顶部值文字颜色
$wot-col-picker-selected-fw: var(--wot-col-picker-selected-fw, 700) !default; // 弹框顶部值高亮字重
$wot-col-picker-line-width: var(--wot-col-picker-line-width, 16px) !default; // 弹框顶部值高亮线条宽度
$wot-col-picker-line-height: var(--wot-col-picker-line-height, 3px) !default; // 弹框顶部值高亮线条高度
$wot-col-picker-line-color: var(
	--wot-col-picker-line-color,
	linear-gradient(315deg, rgba(81, 124, 240, 1), rgba(118, 158, 245, 1))
) !default; // 弹框顶部值高亮线条颜色
$wot-col-picker-line-box-shadow: var(--wot-col-picker-line-box-shadow, 0px 1px 2px 0px rgba(1, 87, 255, 0.2)) !default; // 弹框顶部值高亮线条阴影
$wot-col-picker-list-height: var(--wot-col-picker-list-height, 53vh) !default; // 弹框列表高度
$wot-col-picker-list-padding-bottom: var(--wot-col-picker-list-padding-bottom, 30px) !default; // 弹框列表底部间距
$wot-col-picker-list-color: var(--wot-col-picker-list-color, rgba(0, 0, 0, 0.85)) !default; // 弹框列表文字颜色
$wot-col-picker-list-color-disabled: var(--wot-col-picker-list-color-disabled, rgba(0, 0, 0, 0.15)) !default; // 弹框列表文字禁用颜色
$wot-col-picker-list-color-tip: var(--wot-col-picker-list-color-tip, rgba(0, 0, 0, 0.45)) !default; // 弹框列表提示文字颜色
$wot-col-picker-list-fs: var(--wot-col-picker-list-fs, 14px) !default; // 弹框列表文字字号
$wot-col-picker-list-fs-tip: var(--wot-col-picker-list-fs-tip, 12px) !default; // 弹框列表提示文字字号
$wot-col-picker-list-item-padding: var(--wot-col-picker-list-item-padding, 12px 15px) !default; // 弹框列表选项间距
$wot-col-picker-list-checked-icon-size: var(--wot-col-picker-list-checked-icon-size, 18px) !default; // 弹框列表选中箭头大小
$wot-col-picker-list-color-checked: var(--wot-col-picker-list-color-checked, $wot-color-theme) !default; // 弹框列表选中选项颜色

/* overlay */
$wot-overlay-bg: var(--wot-overlay-bg, rgba(0, 0, 0, 0.65)) !default;
$wot-overlay-bg-dark: var(--wot-overlay-bg-dark, rgba(0, 0, 0, 0.75)) !default;

/* popup */
$wot-popup-close-size: var(--wot-popup-close-size, 24px) !default; // 关闭按钮尺寸
$wot-popup-close-color: var(--wot-popup-close-color, #666) !default; // 关闭按钮颜色

/* progress */
$wot-progress-padding: var(--wot-progress-padding, 9px 0 8px) !default; // 进度条内边距
$wot-progress-bg: var(--wot-progress-bg, rgba(229, 229, 229, 1)) !default; // 进度条底色
$wot-progress-danger-color: var(--wot-progress-danger-color, $wot-color-danger) !default; // 进度条danger颜色
$wot-progress-success-color: var(--wot-progress-success-color, $wot-color-success) !default; // 进度条success进度条颜色
$wot-progress-warning-color: var(--wot-progress-warning-color, $wot-color-warning) !default; // 进度条warning进度条颜色

$wot-progress-color: var(--wot-progress-color, $wot-color-theme) !default; // 进度条颜色
$wot-progress-height: var(--wot-progress-height, 3px) !default; // 进度条高度
$wot-progress-label-color: var(--wot-progress-label-color, #333) !default; // 文字颜色
$wot-progress-label-fs: var(--wot-progress-label-fs, 14px) !default; // 文字字号
$wot-progress-icon-fs: var(--wot-progress-icon-fs, 18px) !default; // 图标字号

/* radio */
$wot-radio-margin: var(--wot-radio-margin, $wot-checkbox-margin) !default; // 多个单选框距离
$wot-radio-label-margin: var(--wot-radio-label-margin, $wot-checkbox-label-margin) !default; // 右侧文字与左侧图标距离
$wot-radio-size: var(--wot-radio-size, 16px) !default; // 左侧图标尺寸
$wot-radio-bg: var(--wot-radio-bg, $wot-color-white) !default; // 左侧图标尺寸
$wot-radio-label-fs: var(--wot-radio-label-fs, $wot-checkbox-label-fs) !default; // 右侧文字字号
$wot-radio-label-color: var(--wot-radio-label-color, $wot-checkbox-label-color) !default; // 右侧文字颜色
$wot-radio-checked-color: var(--wot-radio-checked-color, $wot-checkbox-checked-color) !default; // 选中颜色
$wot-radio-disabled-color: var(--wot-radio-disabled-color, $wot-checkbox-disabled-color) !default; // 禁用颜色
$wot-radio-disabled-label-color: var(--wot-radio-disabled-label-color, $wot-checkbox-disabled-label-color) !default; // 禁用文字颜色

$wot-radio-large-size: var(--wot-radio-large-size, $wot-checkbox-large-size) !default; // 左侧图标尺寸
$wot-radio-large-label-fs: var(--wot-radio-large-label-fs, $wot-checkbox-large-label-fs) !default; // 右侧文字字号

$wot-radio-button-height: var(--wot-radio-button-height, $wot-checkbox-button-height) !default; // 按钮模式复选框高
$wot-radio-button-min-width: var(--wot-radio-button-min-width, 60px) !default; // 按钮模式最小宽
$wot-radio-button-max-width: var(--wot-radio-button-max-width, 144px) !default; // 按钮模式最大宽
$wot-radio-button-radius: var(--wot-radio-button-radius, $wot-checkbox-button-radius) !default; // 按钮圆角大小
$wot-radio-button-bg: var(--wot-radio-button-bg, $wot-checkbox-button-bg) !default; // 按钮模式背景颜色
$wot-radio-button-fs: var(--wot-radio-button-fs, $wot-checkbox-button-font-size) !default; // 按钮模式字号
$wot-radio-button-border: var(--wot-radio-button-border, $wot-checkbox-button-border) !default; // 按钮边框颜色
$wot-radio-button-disabled-border: var(--wot-radio-button-disabled-border, $wot-checkbox-button-disabled-border) !default; // 按钮禁用边框颜色

$wot-radio-dot-size: var(--wot-radio-dot-size, 8px) !default; // 单选dot模式圆点尺寸
$wot-radio-dot-large-size: var(--wot-radio-dot-large-size, 10px) !default; // 单选dot模式大尺寸圆点尺寸
$wot-radio-dot-checked-bg: var(--wot-radio-dot-checked-bg, $wot-color-theme) !default; // 单选dot模式选中背景色
$wot-radio-dot-checked-border-color: var(--wot-radio-dot-checked-border-color, $wot-color-theme) !default; // 单选dot模式选中边框色
$wot-radio-dot-border-color: var(--wot-radio-dot-border-color, #dcdcdc) !default; // 单选dot模式边框色
$wot-radio-dot-disabled-border: var(--wot-radio-dot-disabled-border, #d9d9d9) !default; // 单选dot模式禁用边框颜色
$wot-radio-dot-disabled-bg: var(--wot-radio-dot-disabled-bg, #d9d9d9) !default; // 单选dot模式禁用背景颜色

/* search */
$wot-search-side-padding: var(--wot-search-side-padding, $wot-size-side-padding) !default; // 左右间距
$wot-search-padding: var(--wot-search-padding, 10px 0 10px $wot-search-side-padding) !default; // 不包含取消按钮的间距
$wot-search-input-radius: var(--wot-search-input-radius, 15px) !default; // 输入框圆角大小
$wot-search-input-bg: var(--wot-search-input-bg, $wot-color-bg) !default; // 输入框背景色
$wot-search-input-height: var(--wot-search-input-height, 30px) !default; // 输入框高度
$wot-search-input-padding: var(--wot-search-input-padding, 0 32px 0 42px) !default; // 输入框间距
$wot-search-input-fs: var(--wot-search-input-fs, $wot-fs-content) !default; // 输入框字号
$wot-search-input-color: var(--wot-search-input-color, #262626) !default; // 输入框文字颜色
$wot-search-icon-color: var(--wot-search-icon-color, $wot-color-icon) !default; // 图标颜色
$wot-search-icon-size: var(--wot-search-icon-size, 18px) !default; // 图标大小
$wot-search-clear-icon-size: var(--wot-search-clear-icon-size, $wot-fs-title) !default; // 清除图标大小
$wot-search-placeholder-color: var(--wot-search-placeholder-color, #bfbfbf) !default; // placeholder 颜色
$wot-search-cancel-padding: var(--wot-search-cancel-padding, 0 $wot-search-side-padding 0 10px) !default; // 取消按钮间距
$wot-search-cancel-fs: var(--wot-search-cancel-fs, $wot-fs-title) !default; // 取消按钮字号
$wot-search-cancel-color: var(--wot-search-cancel-color, rgba(0, 0, 0, 0.65)) !default; // 取消按钮颜色
$wot-search-light-bg: var(--wot-search-light-bg, $wot-color-bg) !default; // light 类型的容器背景色

/* slider */
$wot-slider-fs: var(--wot-slider-fs, $wot-fs-content) !default; // 字体大小
$wot-slider-handle-radius: var(--wot-slider-handle-radius, 12px) !default; // 滑块半径
$wot-slider-handle-bg: var(
	--wot-slider-handle-bg,
	resultColor(139deg, $wot-color-theme, 'dark' 'light', #ffffff #f7f7f7, 0% 100%)
) !default; // 滑块背景
$wot-slider-axie-height: var(--wot-slider-axie-height, 3px) !default; // 滑轴高度
$wot-slider-color: var(--wot-slider-color, #333) !default; // 字体颜色
$wot-slider-axie-bg: var(--wot-slider-axie-bg, #e5e5e5) !default; // 滑轴的默认背景色
$wot-slider-line-color: var(
	--wot-slider-line-color,
	resultColor(315deg, $wot-color-theme, 'dark' 'light', #517cf0 #769ef5, 0% 100%)
) !default; // 进度条颜色
$wot-slider-disabled-color: var(--wot-slider-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 禁用状态下字体颜色

/* sort-button */
$wot-sort-button-fs: var(--wot-sort-button-fs, $wot-fs-content) !default; // 字号
$wot-sort-button-color: var(--wot-sort-button-color, $wot-color-content) !default; // 颜色
$wot-sort-button-height: var(--wot-sort-button-height, 48px) !default; // 高度
$wot-sort-button-line-height: var(--wot-sort-button-line-height, 3px) !default; // 下划线高度
$wot-sort-button-line-color: var(--wot-sort-button-line-color, $wot-color-theme) !default; // 下划线颜色

/* steps */
$wot-steps-icon-size: var(--wot-steps-icon-size, 22px) !default; // 图标尺寸
$wot-steps-inactive-color: var(--wot-steps-inactive-color, rgba(0, 0, 0, 0.25)) !default; // 等待状态文字颜色
$wot-steps-finished-color: var(--wot-steps-finished-color, $wot-color-theme) !default; // 完成文字颜色
$wot-steps-icon-text-fs: var(--wot-steps-icon-text-fs, $wot-fs-content) !default; // 数字图标文字字号
$wot-steps-error-color: var(--wot-steps-error-color, $wot-color-danger) !default; // 异常颜色
$wot-steps-title-fs: var(--wot-steps-title-fs, $wot-fs-content) !default; // 标题字号
$wot-steps-title-fw: var(--wot-steps-title-fw, $wot-fw-medium) !default; // 标题字重
$wot-steps-label-fs: var(--wot-steps-label-fs, $wot-fs-secondary) !default; // 描述信息字号
$wot-steps-description-color: var(--wot-steps-description-color, rgba(0, 0, 0, 0.45)) !default; // 描述信息颜色
$wot-steps-is-icon-width: var(--wot-steps-is-icon-width, 30px) !default; // 自定义图标的宽度，给左右留白
$wot-steps-line-color: var(--wot-steps-line-color, rgba(0, 0, 0, 0.15)) !default; // 线条颜色
$wot-steps-dot-size: var(--wot-steps-dot-size, 7px) !default; // 点状大小
$wot-steps-dot-active-size: var(--wot-steps-dot-active-size, 9px) !default; // 点状高亮大小

/* switch */
$wot-switch-size: var(--wot-switch-size, 28px) !default; // switch大小
$wot-switch-width: var(--wot-switch-width, calc(1.8em + 4px)) !default; // 宽度
$wot-switch-height: var(--wot-switch-height, calc(1em + 4px)) !default; // 高度
$wot-switch-circle-size: var(--wot-switch-circle-size, 1em) !default; // 圆点大小
$wot-switch-border-color: var(--wot-switch-border-color, #e5e5e5) !default; // 边框颜色选中状态背景颜色
$wot-switch-active-color: var(--wot-switch-active-color, $wot-color-theme) !default; // 选中状态背景
$wot-switch-active-shadow-color: var(--wot-switch-active-shadow-color, rgba(0, 83, 162, 0.5)) !default; // 选中状态shadow颜色
$wot-switch-inactive-color: var(--wot-switch-inactive-color, #eaeaea) !default; // 非选中背景颜色
$wot-switch-inactive-shadow-color: var(--wot-switch-inactive-shadow-color, rgba(155, 155, 155, 0.5)) !default; // 非选中状态shadow颜色

/* tabs */
$wot-tabs-nav-arrow-fs: var(--wot-tabs-nav-arrow-fs, 18px) !default; // 全部Icon字号
$wot-tabs-nav-arrow-open-fs: var(--wot-tabs-nav-arrow-open-fs, 14px) !default; // 展开Icon字号
$wot-tabs-nav-width: var(--wot-tabs-nav-width, 100vw) !default; // tabs 头部切换宽度
$wot-tabs-nav-height: var(--wot-tabs-nav-height, 42px) !default; // 头部切换高度
$wot-tabs-nav-fs: var(--wot-tabs-nav-fs, $wot-fs-content) !default; // 头部切换文字大小
$wot-tabs-nav-color: var(--wot-tabs-nav-color, rgba(0, 0, 0, 0.85)) !default; // 头部切换文字颜色
$wot-tabs-nav-bg: var(--wot-tabs-nav-bg, $wot-color-white) !default; // 背景颜色
$wot-tabs-nav-active-color: var(--wot-tabs-nav-active-color, $wot-color-theme) !default; // 头部高亮颜色
$wot-tabs-nav-disabled-color: var(--wot-tabs-nav-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 头部禁用颜色
$wot-tabs-nav-line-height: var(--wot-tabs-nav-line-height, 3px) !default; // 高亮边框高度
$wot-tabs-nav-line-width: var(--wot-tabs-nav-line-width, 19px) !default; // 高亮边框宽度
$wot-tabs-nav-line-bg-color: var(--wot-tabs-nav-line-bg-color, $wot-color-theme) !default; // 底部条颜色
$wot-tabs-nav-map-fs: var(--wot-tabs-nav-map-fs, $wot-fs-content) !default; // map 类型按钮字号
$wot-tabs-nav-map-color: var(--wot-tabs-nav-map-color, rgba(0, 0, 0, 0.85)) !default; // map 类型按钮文字颜色
$wot-tabs-nav-map-arrow-color: var(--wot-tabs-nav-map-arrow-color, rgba(0, 0, 0, 0.65)) !default; // map 类型箭头颜色
$wot-tabs-nav-map-btn-before-bg: var(
	--wot-tabs-nav-map-btn-before-bg,
	linear-gradient(270deg, rgba(255, 255, 255, 1) 1%, rgba(255, 255, 255, 0) 100%)
) !default; // 左侧map遮罩阴影
$wot-tabs-nav-map-button-back-color: var(--wot-tabs-nav-map-button-back-color, rgba(0, 0, 0, 0.04)) !default; // map 类型按钮边框颜色
$wot-tabs-nav-map-button-radius: var(--wot-tabs-nav-map-button-radius, 16px) !default; // map 类型按钮圆角大小
$wot-tabs-nav-map-modal-bg: var(--wot-tabs-nav-map-modal-bg, $wot-overlay-bg) !default; // map 类型蒙层背景色

/* tag */
$wot-tag-fs: var(--wot-tag-fs, $wot-fs-secondary) !default; // 字号
$wot-tag-color: var(--wot-tag-color, $wot-color-white) !default; // 字体颜色
$wot-tag-small-fs: var(--wot-tag-small-fs, $wot-fs-aid) !default; // 小尺寸字号
$wot-tag-info-color: var(--wot-tag-info-color, #585858) !default; // info 颜色
$wot-tag-primary-color: var(--wot-tag-primary-color, $wot-color-theme) !default; // 主颜色
$wot-tag-danger-color: var(--wot-tag-danger-color, $wot-color-danger) !default; // danger 颜色
$wot-tag-warning-color: var(--wot-tag-warning-color, $wot-color-warning) !default; // warning 颜色
$wot-tag-success-color: var(--wot-tag-success-color, $wot-color-success) !default; // success 颜色
$wot-tag-info-bg: var(--wot-tag-info-bg, resultColor(49deg, $wot-color-black, 'dark' 'light', #808080 #999999, 0% 100%)) !default; // info 背景颜色
$wot-tag-primary-bg: var(--wot-tag-primary-bg, $wot-color-theme) !default; // 主背景颜色
$wot-tag-danger-bg: var(--wot-tag-danger-bg, $wot-color-danger) !default; // danger 背景颜色
$wot-tag-warning-bg: var(--wot-tag-warning-bg, $wot-color-warning) !default; // warning 背景颜色
$wot-tag-success-bg: var(--wot-tag-success-bg, $wot-color-success) !default; // success 背景颜色
$wot-tag-round-color: var(--wot-tag-round-color, rgba(102, 102, 102, 1)) !default; // round 字体颜色
$wot-tag-round-border-color: var(--wot-tag-round-border-color, rgba(225, 225, 225, 1)) !default; // round 边框颜色
$wot-tag-round-radius: var(--wot-tag-round-radius, 12px) !default; // round 圆角大小
$wot-tag-mark-radius: var(--wot-tag-mark-radius, 6px 2px 6px 2px) !default; // mark 圆角大小
$wot-tag-close-size: var(--wot-tag-close-size, 14px) !default; // 关闭按钮字号
$wot-tag-close-color: var(--wot-tag-close-color, $wot-tag-info-color) !default; // 关闭按钮颜色
$wot-tag-close-active-color: var(--wot-tag-close-active-color, rgba(0, 0, 0, 0.45)) !default; // 关闭按钮 active 颜色

/* toast */
$wot-toast-color: var(--wot-toast-color, $wot-color-white) !default; // 文字颜色
$wot-toast-padding: var(--wot-toast-padding, 16px 24px) !default; // padding
$wot-toast-max-width: var(--wot-toast-max-width, 300px) !default; // 最大宽度
$wot-toast-radius: var(--wot-toast-radius, 8px) !default; // 圆角大小
$wot-toast-bg: var(--wot-toast-bg, $wot-overlay-bg) !default; // 背景色
$wot-toast-fs: var(--wot-toast-fs, $wot-fs-content) !default; // 字号
$wot-toast-line-height: var(--wot-toast-line-height, 20px) !default; // 行高
$wot-toast-with-icon-min-width: var(--wot-toast-with-icon-min-width, 150px) !default; // 有图标的情况下最小宽度
$wot-toast-icon-size: var(--wot-toast-icon-size, 32px) !default; // 图标大小
$wot-toast-icon-margin-right: var(--wot-toast-icon-margin-right, 12px) !default; // 图标右边距
$wot-toast-icon-margin-bottom: var(--wot-toast-icon-margin-bottom, 12px) !default; // 图标下边距
$wot-toast-loading-padding: var(--wot-toast-loading-padding, 10px) !default; // loading状态下的padding
$wot-toast-loading-margin-bottom: var(--wot-toast-loading-margin-bottom, 16px) !default; // loading动画的margin-bottom
$wot-toast-box-shadow: var(--wot-toast-box-shadow, 0px 6px 16px 0px rgba(0, 0, 0, 0.08)) !default; // 外部阴影

/* loading */
$wot-loading-size: var(--wot-loading-size, 32px) !default; // loading 大小

/* tooltip */
$wot-tooltip-bg: var(--wot-tooltip-bg, rgba(38, 39, 40, 0.8)) !default; // 背景色
$wot-tooltip-color: var(--wot-tooltip-color, $wot-color-white) !default; // 文字颜色
$wot-tooltip-radius: var(--wot-tooltip-radius, 8px) !default; // 圆角大小
$wot-tooltip-arrow-size: var(--wot-tooltip-arrow-size, 5px) !default; // 箭头大小
$wot-tooltip-fs: var(--wot-tooltip-fs, $wot-fs-content) !default; // 字号
$wot-tooltip-blur: var(--wot-tooltip-blur, 10px) !default; // 背景高斯模糊效果
$wot-tooltip-padding: var(--wot-tooltip-padding, 9px 20px) !default; // 间距
$wot-tooltip-close-size: var(--wot-tooltip-close-size, 6px) !default; // 背景高斯模糊效果
$wot-tooltip-z-index: var(--wot-tooltip-z-index, 500) !default;
$wot-tooltip-line-height: var(--wot-tooltip-line-height, 18px) !default; // 行高

/* popover */
$wot-popover-bg: var(--wot-popover-bg, $wot-color-white) !default; // 背景色
$wot-popover-color: var(--wot-popover-color, rgba(0, 0, 0, 0.85)) !default; // 文字颜色
$wot-popover-box-shadow: var(--wot-popover-box-shadow, 0px 2px 10px 0px rgba(0, 0, 0, 0.1)) !default; // 阴影颜色
$wot-popover-arrow-box-shadow: var(--wot-popover-arrow-box-shadow, 0px 2px 10px 0px rgba(0, 0, 0, 0.2)) !default; // 阴影颜色
$wot-popover-border-color: var(--wot-popover-border-color, rgba(0, 0, 0, 0.09)) !default; // 阴影颜色
$wot-popover-radius: var(--wot-popover-radius, 4px) !default; // 圆角大小
$wot-popover-arrow-size: var(--wot-popover-arrow-size, 6px) !default; // 箭头大小
$wot-popover-fs: var(--wot-popover-fs, $wot-fs-content) !default; // 字号
$wot-popover-padding: var(--wot-popover-padding, 15px) !default; // 间距
$wot-popover-line-height: var(--wot-popover-line-height, 18px) !default; // 行高
$wot-popover-z-index: var(--wot-popover-z-index, $wot-tooltip-z-index) !default;

/* grid-item */
$wot-grid-item-fs: var(--wot-grid-item-fs, 12px) !default; // 字号
$wot-grid-item-bg: var(--wot-grid-item-bg, $wot-color-white) !default; // 字号
$wot-grid-item-padding: var(--wot-grid-item-padding, 14px 0px) !default; // 内容的 padding
$wot-grid-item-border-color: var(--wot-grid-item-border-color, $wot-color-border-light) !default; // 边框颜色
$wot-grid-item-hover-bg: var(--wot-grid-item-hover-bg, $wot-color-gray-3) !default; // hover背景色
$wot-grid-item-hover-bg-dark: var(--wot-grid-item-hover-bg-dark, $wot-color-gray-7) !default; // 暗黑模式hover背景色

/* statustip */
$wot-statustip-fs: var(--wot-statustip-fs, $wot-fs-content) !default; // 字号
$wot-statustip-color: var(--wot-statustip-color, rgba(0, 0, 0, 0.45)) !default; // 文字颜色
$wot-statustip-line-height: var(--wot-statustip-line-height, 16px) !default; // 文字行高
$wot-statustip-padding: var(--wot-statustip-padding, 5px 10px) !default; // 间距

/* card */
$wot-card-bg: var(--wot-card-bg, $wot-color-white) !default; // 背景色
$wot-card-fs: var(--wot-card-fs, $wot-fs-content) !default; // 卡片字号
$wot-card-padding: var(--wot-card-padding, 0 $wot-size-side-padding) !default; // 内边距
$wot-card-footer-padding: var(--wot-card-footer-padding, 12px 0 16px) !default; // 底部内边距
$wot-card-shadow-color: var(--wot-card-shadow-color, 0px 4px 8px 0px rgba(0, 0, 0, 0.02)) !default; // 阴影
$wot-card-radius: var(--wot-card-radius, 8px) !default; // 圆角大小
$wot-card-line-height: var(--wot-card-line-height, 1.1) !default; // 行高
$wot-card-margin: var(--wot-card-margin, 0 $wot-size-side-padding) !default; // 外边距
$wot-card-title-color: var(--wot-card-title-color, rgba(0, 0, 0, 0.85)) !default; // 标题颜色
$wot-card-title-fs: var(--wot-card-title-fs, $wot-fs-title) !default; //  矩形卡片标题字号
$wot-card-content-border-color: var(--wot-card-content-border-color, rgba(0, 0, 0, 0.09)) !default; // 内容边框
$wot-card-rectangle-title-padding: var(--wot-card-rectangle-title-padding, 15px 15px 12px) !default; // 矩形卡片头部内边距
$wot-card-rectangle-content-padding: var(--wot-card-rectangle-content-padding, 16px 0) !default; // 矩形卡片内容内边距
$wot-card-rectangle-footer-padding: var(--wot-card-rectangle-footer-padding, 12px 0) !default; // 矩形卡片底部内边距
$wot-card-content-color: var(--wot-card-content-color, rgba(0, 0, 0, 0.45)) !default; // 文本内容颜色
$wot-card-content-line-height: var(--wot-card-content-line-height, 1.428) !default; // 文本内容行高
$wot-card-content-margin: var(--wot-card-content-margin, 13px 0 12px) !default; // 内容外边距
$wot-card-content-rectangle-margin: var(--wot-card-content-rectangle-margin, 14px 0 12px) !default; // 矩形卡片内容外边距

/* upload */
$wot-upload-size: var(--wot-upload-size, 80px) !default; // upload的外边框默认尺寸
$wot-upload-evoke-icon-size: var(--wot-upload-evoke-icon-size, 32px) !default; // 唤起项的图标大小
$wot-upload-evoke-bg: var(--wot-upload-evoke-bg, rgba(0, 0, 0, 0.04)) !default; // 唤起项的背景色
$wot-upload-evoke-color: var(--wot-upload-evoke-color, rgba(0, 0, 0, 0.25)) !default; // 唤起项的图标颜色
$wot-upload-evoke-disabled-color: var(--wot-upload-evoke-disabled-color, rgba(0, 0, 0, 0.09)) !default; // 唤起项禁用颜色
$wot-upload-close-icon-size: var(--wot-upload-close-icon-size, 16px) !default; // 移除按钮尺寸
$wot-upload-close-icon-color: var(--wot-upload-close-icon-color, rgba(0, 0, 0, 0.65)) !default; // 移除按钮颜色
$wot-upload-progress-fs: var(--wot-upload-progress-fs, 14px) !default; // 进度文字字号
$wot-upload-file-fs: var(--wot-upload-file-fs, 12px) !default; // 文件名字号
$wot-upload-file-color: var(--wot-upload-file-color, $wot-color-secondary) !default; // 文件名字颜色
$wot-upload-preview-name-fs: var(--wot-upload-preview-name-fs, 12px) !default; // 预览图片名字号
$wot-upload-preview-icon-size: var(--wot-upload-preview-icon-size, 24px) !default; // 预览内部图标尺寸
$wot-upload-preview-name-bg: var(--wot-upload-preview-name-bg, rgba(0, 0, 0, 0.6)) !default; // 预览文件名背景色
$wot-upload-preview-name-height: var(--wot-upload-preview-name-height, 22px) !default; // 预览文件名背景高度
$wot-upload-cover-icon-size: var(--wot-upload-cover-icon-size, 22px) !default; // 视频/文件图标尺寸

/* curtain */
$wot-curtain-content-radius: var(--wot-curtain-content-radius, 24px) !default; // 内容圆角
$wot-curtain-content-close-color: var(--wot-curtain-content-close-color, $wot-color-white) !default; // 关闭按钮颜色
$wot-curtain-content-close-fs: var(--wot-curtain-content-close-fs, $wot-fs-big) !default; // 关闭按钮大小

/* notify */
$wot-notify-text-color: var(--wot-notify-text-color, $wot-color-white) !default;
$wot-notify-padding: var(--wot-notify-padding, 8px 16px) !default;
$wot-notify-font-size: var(--wot-notify-font-size, $wot-fs-content) !default;
$wot-notify-line-height: var(--wot-notify-line-height, 20px) !default;
$wot-notify-primary-background: var(--wot-notify-primary-background, $wot-color-theme) !default;
$wot-notify-success-background: var(--wot-notify-success-background, $wot-color-success) !default;
$wot-notify-danger-background: var(--wot-notify-danger-background, $wot-color-danger) !default;
$wot-notify-warning-background: var(--wot-notify-warning-background, $wot-color-warning) !default;

/* skeleton */
$wot-skeleton-background-color: var(--wot-skeleton-background-color, #eee) !default;
$wot-skeleton-animation-gradient: var(--wot-skeleton-animation-gradient, rgba(0, 0, 0, 0.04)) !default;
$wot-skeleton-animation-flashed: var(--wot-skeleton-animation-flashed, rgba(230, 230, 230, 0.3)) !default;
$wot-skeleton-text-height-default: var(--wot-skeleton-text-height-default, 16px) !default;
$wot-skeleton-rect-height-default: var(--wot-skeleton-rect-height-default, 16px) !default;
$wot-skeleton-circle-height-default: var(--wot-skeleton-circle-height-default, 48px) !default;
$wot-skeleton-row-margin-bottom: var(--wot-skeleton-row-margin-bottom, 16px) !default;
$wot-skeleton-border-radius-text: var(--wot-skeleton-border-radius-text, 2px) !default;
$wot-skeleton-border-radius-rect: var(--wot-skeleton-border-radius-rect, 4px) !default;
$wot-skeleton-border-radius-circle: var(--wot-skeleton-border-radius-circle, 50%) !default;

/* circle */
$wot-circle-text-color: var(--wot-circle-text-color, $wot-color-content) !default; // circle文字颜色

/* swiper */
$wot-swiper-radius: var(--wot-swiper-radius, 8px);
$wot-swiper-item-padding: var(--wot-swiper-item-padding, 0);
$wot-swiper-item-text-color: var(--wot-swiper-item-text-color, #ffffff);
$wot-swiper-item-text-fs: var(--wot-swiper-item-text-fs, $wot-fs-title);

/* swiper-nav */
// dot & dots-bar
$wot-swiper-nav-dot-color: var(--wot-swiper-nav-dot-color, $wot-font-white-2) !default;
$wot-swiper-nav-dot-active-color: var(--wot-swiper-nav-dot-active-color, $wot-font-white-1) !default;
$wot-swiper-nav-dot-size: var(--wot-swiper-nav-dot-size, 12rpx) !default;
$wot-swiper-nav-dots-bar-active-width: var(--wot-swiper-nav-dots-bar-active-width, 40rpx) !default;
// fraction
$wot-swiper-nav-fraction-color: var(--wot-swiper-nav-fraction-color, $wot-font-white-1) !default;
$wot-swiper-nav-fraction-bg-color: var(--wot-swiper-nav-fraction-bg-color, $wot-font-gray-3) !default;
$wot-swiper-nav-fraction-height: var(--wot-swiper-nav-fraction-height, 48rpx) !default;
$wot-swiper-nav-fraction-font-size: var(--wot-swiper-nav-fraction-font-size, 24rpx) !default;
// button
$wot-swiper-nav-btn-color: var(--wot-swiper-nav-btn-color, $wot-font-white-1) !default;
$wot-swiper-nav-btn-bg-color: var(--wot-swiper-nav-btn-bg-color, $wot-font-gray-3) !default;
$wot-swiper-nav-btn-size: var(--wot-swiper-nav-btn-size, 48rpx) !default;

/* segmented */
$wot-segmented-padding: var(--wot-segmented-padding, 4px) !default; // 分段器padding
$wot-segmented-item-bg-color: var(--wot-segmented-item-bg-color, #eeeeee) !default;
$wot-segmented-item-color: var(--wot-segmented-item-color, rgba(0, 0, 0, 0.85)) !default; // 标题文字颜色
$wot-segmented-item-acitve-bg: var(--wot-segmented-item-acitve-bg, #ffffff) !default; // 标题文字颜色
$wot-segmented-item-disabled-color: var(--wot-segmented-item-disabled-color, rgba(0, 0, 0, 0.25)) !default; // 标题文字禁用颜色

/* tabbar */
$wot-tabbar-height: var(--wot-tabbar-height, 50px) !default;
$wot-tabbar-box-shadow: var(
	--wot-tabbar-box-shadow,
	0 6px 30px 5px rgba(0, 0, 0, 0.05),
	0 16px 24px 2px rgba(0, 0, 0, 0.04),
	0 8px 10px -5px rgba(0, 0, 0, 0.08)
) !default; // round类型tabbar阴影

/* tabbar-item */
$wot-tabbar-item-title-font-size: var(--wot-tabbar-item-title-font-size, 10px) !default; // tabbar选项文字大小
$wot-tabbar-item-title-line-height: var(--wot-tabbar-item-title-line-height, initial) !default; // tabbar选项标题文字行高
$wot-tabbar-inactive-color: var(--wot-tabbar-inactive-color, $wot-color-title) !default; // 标题文字和图标颜色
$wot-tabbar-active-color: var(--wot-tabbar-active-color, $wot-color-theme) !default; // 选中文字和图标颜色
$wot-tabbar-item-icon-size: var(--wot-tabbar-item-icon-size, 20px) !default; // tabbar选项图标大小

/* navbar */
$wot-navbar-height: var(--wot-navbar-height, 44px) !default; // navbar高度
$wot-navbar-color: var(--wot-navbar-color, $wot-font-gray-1) !default; // navbar字体颜色
$wot-navbar-background: var(--wot-navbar-background, $wot-color-white) !default; // navbar背景颜色
$wot-navbar-arrow-size: var(--wot-navbar-arrow-size, 24px) !default; // navbar左箭头图标大小
$wot-navbar-desc-font-size: var(--wot-navbar-desc-font-size, 16px); // navbar 左箭头字体大小
$wot-navbar-desc-font-color: var(--wot-navbar-desc-font-color, $wot-font-gray-1) !default; // navbar左右两侧字体颜色
$wot-navbar-title-font-size: var(--wot-navbar-title-font-size, 18px); // navbar title字体大小
$wot-navbar-title-font-weight: var(--wot-navbar-title-font-weight, 600); // navbar title字重
$wot-navbar-disabled-opacity: var(--wot-navbar-disabled-opacity, 0.6) !default; // navbar左右两侧字体禁用
$wot-navbar-hover-color: var(--wot-navbar-hover-color, #eee) !default; // navbar hover样式

/* navbar-capsule */
$wot-navbar-capsule-border-color: var(--wot-navbar-capsule-border-color, #e7e7e7) !default;
$wot-navbar-capsule-border-radius: var(--wot-navbar-capsule-border-radius, 16px) !default;
$wot-navbar-capsule-width: var(--wot-navbar-capsule-width, 88px) !default;
$wot-navbar-capsule-height: var(--wot-navbar-capsule-height, 32px) !default;
$wot-navbar-capsule-icon-size: var(--wot-navbar-capsule-icon-size, 20px) !default; // navbar capsule图标大小

/* table */
$wot-table-color: var(--wot-table-color, $wot-font-gray-1) !default; // 表格字体颜色
$wot-table-bg: var(--wot-table-bg, #ffffff) !default; // 表格背景颜色
$wot-table-stripe-bg: var(--wot-table-stripe-bg, #f3f3f3) !default; // 表格背景颜色
$wot-table-border-color: var(--wot-table-border-color, #ececec) !default; // 表格边框颜色
$wot-table-font-size: var(--wot-table-font-size, 13px) !default; // 表格字体大小

/* sidebar */
$wot-sidebar-bg: var(--wot-sidebar-bg, $wot-color-gray-1) !default; // 侧边栏背景色
$wot-sidebar-width: var(--wot-sidebar-width, 104px) !default; // 侧边栏宽度
$wot-sidebar-height: var(--wot-sidebar-height, 100%) !default; // 侧边栏高度

/* sidebar-item */
$wot-sidebar-color: var(--wot-sidebar-color, $wot-font-gray-1) !default;
$wot-sidebar-item-height: var(--wot-sidebar-item-height, 56px) !default;
$wot-sidebar-item-line-height: var(--wot-sidebar-item-line-height, 24px) !default;
$wot-sidebar-disabled-color: var(--wot-side-bar-disabled-color, $wot-font-gray-4) !default;
$wot-sidebar-active-color: var(--wot-sidebar-active-color, $wot-color-theme) !default; // 激活项字体颜色
$wot-sidebar-active-bg: var(--wot-sidebar-active-bg, $wot-color-white) !default; // 激活项背景颜色
$wot-sidebar-hover-bg: var(--wot-sidebar-hover-bg, $wot-color-gray-2) !default; // 激活项点击背景颜色
$wot-sidebar-border-radius: var(--wot-sidebar-border-radius, 8px) !default;
$wot-sidebar-font-size: var(--wot-sidebar-font-size, 16px) !default;
$wot-sidebar-icon-size: var(--wot-sidebar-icon-size, 20px) !default;
$wot-sidebar-active-border-width: var(--wot-sidebar-active-border-width, 4px) !default;
$wot-sidebar-active-border-height: var(--wot-sidebar-active-border-height, 16px) !default;

/* fab */
$wot-fab-trigger-height: var(--wot-fab-trigger-height, 56px) !default;
$wot-fab-trigger-width: var(--wot-fab-trigger-width, 56px) !default;
$wot-fab-actions-padding: var(--wot-actions-padding, 12px) !default;
$wot-fab-icon-fs: var(--wot-fab-icon-fs, 20px) !default;

/* count-down */
$wot-count-down-text-color: var(--wot-count-down-text-color, $wot-color-gray-8) !default;
$wot-count-down-font-size: var(--wot-count-down-font-size, $wot-fs-content) !default;
$wot-count-down-line-height: var(--wot-count-down-line-height, 20px) !default;

/* keyboard */
$wot-keyboard-key-height: var(--wot-keyboard-key-height, 48px) !default;
$wot-keyboard-key-font-size: var(--wot-keyboard-key-font-size, 28px) !default;
$wot-keyboard-key-background: var(--wot-keyboard-key-background, $wot-color-white) !default;
$wot-keyboard-key-border-radius: var(--wot-keyboard-key-border-radius, 8px) !default;
$wot-keyboard-delete-font-size: var(--wot-keyboard-delete-font-size, 16px) !default;
$wot-keyboard-key-active-color: var(--wot-keyboard-key-active-color, $wot-color-gray-3) !default;
$wot-keyboard-button-text-color: var(--wot-keyboard-button-text-color, $wot-color-white) !default;
$wot-keyboard-button-background: var(--wot-keyboard--button-background, $wot-color-theme) !default;
$wot-keyboard-button-active-opacity: var(--wot-keyboard-button-active-opacity, 0.6) !default;
$wot-keyboard-background: var(--wot-keyboard-background, $wot-color-gray-2) !default;
$wot-keyboard-title-height: var(--wot-keyboard-title-height, 34px) !default;
$wot-keyboard-title-color: var(--wot-keyboard-title-color, $wot-color-gray-7) !default;
$wot-keyboard-title-font-size: var(--wot-keyboard-title-font-size, 16px) !default;
$wot-keyboard-close-padding: var(--wot-keyboard-title-font-size, 0 16px) !default;
$wot-keyboard-close-color: var(--wot-keyboard-close-color, $wot-color-theme) !default;
$wot-keyboard-close-font-size: var(--wot-keyboard-close-font-size, 14px) !default;
$wot-keyboard-icon-size: var(--wot-keyboard-icon-size, 22px) !default;

/* number-keyboard */
$wot-number-keyboard-key-height: var(--wot-number-keyboard-key-height, 48px) !default;
$wot-number-keyboard-key-font-size: var(--wot-number-keyboard-key-font-size, 28px) !default;
$wot-number-keyboard-key-background: var(--wot-number-keyboard-key-background, $wot-color-white) !default;
$wot-number-keyboard-key-border-radius: var(--wot-number-keyboard-key-border-radius, 8px) !default;
$wot-number-keyboard-delete-font-size: var(--wot-number-keyboard-delete-font-size, 16px) !default;
$wot-number-keyboard-key-active-color: var(--wot-number-keyboard-key-active-color, $wot-color-gray-3) !default;
$wot-number-keyboard-button-text-color: var(--wot-number-keyboard-button-text-color, $wot-color-white) !default;
$wot-number-keyboard-button-background: var(--wot-number-keyboard--button-background, $wot-color-theme) !default;
$wot-number-keyboard-button-active-opacity: var(--wot-number-keyboard-button-active-opacity, 0.6) !default;
$wot-number-keyboard-background: var(--wot-number-keyboard-background, $wot-color-gray-2) !default;
$wot-number-keyboard-title-height: var(--wot-number-keyboard-title-height, 34px) !default;
$wot-number-keyboard-title-color: var(--wot-number-keyboard-title-color, $wot-color-gray-7) !default;
$wot-number-keyboard-title-font-size: var(--wot-number-keyboard-title-font-size, 16px) !default;
$wot-number-keyboard-close-padding: var(--wot-number-keyboard-title-font-size, 0 16px) !default;
$wot-number-keyboard-close-color: var(--wot-number-keyboard-close-color, $wot-color-theme) !default;
$wot-number-keyboard-close-font-size: var(--wot-number-keyboard-close-font-size, 14px) !default;
$wot-number-keyboard-icon-size: var(--wot-number-keyboard-icon-size, 22px) !default;

/* passwod-input */
$wot-password-input-height: var(--wot-password-input-height, 50px);
$wot-password-input-margin: var(--wot-password-input-margin, 16px);
$wot-password-input-font-size: var(--wot-password-input-margin, 20px);
$wot-password-input-radius: var(--wot-password-input-radius, 6px);
$wot-password-input-background: var(--wot-password-input-background, #fff);
$wot-password-input-info-color: var(--wot-password-input-info-color, $wot-color-info);
$wot-password-input-info-font-size: var(--wot-password-input-info-font-size, $wot-fs-content);
$wot-password-input-border-color: var(--wot-password-border-color, #ebedf0);
$wot-password-input-error-info-color: var(--wot-password-input-error-info-color, $wot-color-danger);
$wot-password-input-dot-size: var(--wot-password-input-dot-size, 10px);
$wot-password-input-dot-color: var(--wot-password-input-dot-color, $wot-color-gray-8);
$wot-password-input-text-color: var(--wot-password-input-text-color, $wot-color-gray-8);
$wot-password-input-cursor-color: var(--wot-password-input-cursor-color, $wot-color-gray-8);
$wot-password-input-cursor-width: var(--wot-password-input-cursor-width, 1px);
$wot-password-input-cursor-height: var(--wot-password-input-cursor-height, 40%);
$wot-password-input-cursor-duration: var(--wot-password-input-cursor-duration, 1s);

/* form-item */
$wot-form-item-error-message-color: var(--wot-form-item-error-message-color, $wot-color-danger) !default;
$wot-form-item-error-message-font-size: var(--wot-form-item-error-message-font-size, $wot-fs-secondary) !default;
$wot-form-item-error-message-line-height: var(--wot-form-item-error-message-line-height, 24px) !default;

/* backtop */
$wot-backtop-bg: var(--wot-backtop-bg, #e1e1e1) !default;
$wot-backtop-icon-size: var(--wot-backtop-icon-size, 20px) !default;

/* index-bar */
$wot-index-bar-index-font-size: var(--wot-index-bar-index-font-size, $wot-fs-aid) !default;

/* text */
$wot-text-info-color: var(--wot-text-info-color, $wot-color-info) !default;
$wot-text-primary-color: var(--wot-text-primary-color, $wot-color-theme) !default;
$wot-text-error-color: var(--wot-text-error-color, $wot-color-danger) !default;
$wot-text-warning-color: var(--wot-text-warning-color, $wot-color-warning) !default;
$wot-text-success-color: var(--wot-text-success-color, $wot-color-success) !default;

/* video-preview */
$wot-video-preview-bg: var(--wot-video-preview-bg, rgba(0, 0, 0, 0.8)) !default; // 背景色
$wot-video-preview-close-color: var(--wot-video-preview-close-color, #fff) !default; // 图标颜色
$wot-video-preview-close-font-size: var(--wot-video-preview-close-font-size, 20px) !default; // 图标大小

/* img-cropper */
$wot-img-cropper-icon-size: var(--wot-img-cropper-icon-size, $wot-fs-big) !default; // 图标大小
$wot-img-cropper-icon-color: var(--wot-img-cropper-icon-color, #fff) !default; // 图标颜色

/* floating-panel */
$wot-floating-panel-bg: var(--wot-floating-panel-bg, $wot-color-white) !default; // 背景色
$wot-floating-panel-radius: var(--wot-floating-panel-radius, 16px) !default; // 圆角
$wot-floating-panel-z-index: var(--wot-floating-panel-z-index, 99) !default; // 层级
$wot-floating-panel-header-height: var(--wot-floating-panel-header-height, 30px) !default; // 头部高度
$wot-floating-panel-bar-width: var(--wot-floating-panel-bar-width, 20px) !default; // bar 宽度
$wot-floating-panel-bar-height: var(--wot-floating-panel-bar-height, 3px) !default; // bar 高度
$wot-floating-panel-bar-bg: var(--wot-floating-panel-bar-bg, $wot-color-gray-5) !default; // bar 背景色
$wot-floating-panel-bar-radius: var(--wot-floating-panel-bar-radius, 4px) !default; // bar 圆角
$wot-floating-panel-content-bg: var(--wot-floating-panel-content-bg, $wot-color-white) !default; // 内容背景色

/* signature */
$wot-signature-bg: var(--wot-signature-bg, $wot-color-white) !default; // 背景色
$wot-signature-radius: var(--wot-signature-radius, 4px) !default; // 圆角
$wot-signature-border: var(--wot-signature-border, 1px solid $wot-color-gray-5) !default; // 边框圆角
$wot-signature-footer-margin-top: var(--wot-signature-footer-margin-top, 8px) !default; // 底部按钮上边距
$wot-signature-button-margin-left: var(--wot-signature-button-margin-left, 8px) !default; // 底部按钮左边距
