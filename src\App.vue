<script setup lang="ts">
import { useIMStore } from './store/im';
import { useSystemStore } from './store/system';
import { useDark } from './store/index';
import { useI18nSync } from './hooks/useI18nSync';
import { useIframeMessage } from './hooks/useIframeMessage';

// const { checkConnection } = useIMStore();
const systemStore = useSystemStore();
const { restoreSessionState, validateSessionState } = systemStore;

// 初始化国际化
const darkMode = useDark();
const { setLocale } = useI18nSync(); // 禁用内置的iframe消息监听，使用专门的hook处理

// 使用专门的iframe消息处理hook
useIframeMessage({
	onLocaleChange: (locale) => {
		setLocale(locale);
	},
	onThemeChange: (isDark) => {
		darkMode.setDark(isDark);
	},
});

let hasPerformedInitialCheck = false;

onLaunch(async (options) => {
	console.log('onLaunch', options);
	await performInitialLoginCheck();
});

// onShow(() => {
// 	checkConnection();
// });

// 执行初始登录状态检验
async function performInitialLoginCheck() {
	if (hasPerformedInitialCheck) {
		return;
	}
	hasPerformedInitialCheck = true;

	try {
		console.log('开始检验登录状态...');

		// 首先尝试恢复本地存储的登录状态
		const restored = await restoreSessionState();

		if (restored) {
			console.log('恢复本地登录状态成功，开始验证...');
			// 验证登录状态是否有效
			const isValid = await validateSessionState();

			if (isValid && systemStore.sessionInfo) {
				console.log('登录状态有效，跳转到主页面...');
				// 登录状态有效，根据租户类型跳转到对应页面
				redirectToHomePage();
				return;
			}
		}

		console.log('登录状态无效或不存在，跳转到登录页...');
		// 登录状态无效或不存在，跳转到登录页
		uni.reLaunch({ url: '/mod/main/login' });
	} catch (error) {
		console.error('检验登录状态时发生错误:', error);
		// 发生错误时，跳转到登录页
		uni.reLaunch({ url: '/mod/main/login' });
	}
}

// 根据租户类型跳转到对应的主页面
function redirectToHomePage() {
	if (!systemStore.sessionInfo) {
		uni.reLaunch({ url: '/mod/main/login' });
		return;
	}

	uni.reLaunch({ url: '/mod/main/home' });
}
</script>

<style lang="scss">
/* #ifndef APP-NVUE */

@use 'diw.scss';
@use 'ui/wot-design-uni/components/common/abstracts/variable' as *;

page {
	background-color: #f8f8f8;
	color: black;
}

.diw-theme {
	--wot-card-content-border-color: transparent;
	--wot-card-rectangle-content-padding: 0;
	--wot-cell-padding: 0;

	.wd-card__title::before {
		content: ' ';
		background-color: $wot-color-theme;
		width: 4px;
		height: 10px;
		margin-right: 4px;
		display: inline-block;
	}

	.diw-textarea {
		--wot-cell-vertical-top: 0;
		--wot-textarea-cell-padding: 0;
		--wot-textarea-padding: 0;
	}
}

.w-half {
	width: 50%;
}

/* #endif */
</style>
