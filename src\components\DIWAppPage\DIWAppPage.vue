<template>
	<view v-if="sessionLost" :class="customClass" :style="customStyle" @click="clickHandler">
		<DIWLogin />
	</view>
	<template v-else>
		<view v-if="mode === 0" class="diw-app-page" :class="customClass" :style="customStyle" @click="clickHandler">
			<slot name="default" />
		</view>
		<view v-else class="diw-app-page diw-app-page-sv" :class="customClass" :style="customStyle" @click="clickHandler">
			<slot name="appBar" />
			<view class="diw-app-page__main">
				<slot name="default" />
			</view>
			<template v-if="props.hideBottomBar !== true">
				<slot name="bottomBar" />
			</template>
			<slot name="keyboard" />
		</view>
		<!-- 新增可拖拽浮动按钮 -->
		<DIWFloatBall axis="xy" magnetic="x" :gap="20" v-if="floatButton"> <slot name="floatButton" /></DIWFloatBall>
	</template>
</template>

<script setup lang="ts">
import { injectDIWAppPageRoot } from '@/framework/page';
import { isAbortError, throwAbortError } from '@/framework/error';

import { useSystemStore } from '@/store/system';
import { useIMStore } from '@/store/im';
const { protect, sessionInfo, navigateBack } = useFramework();

// #ifdef H5
defineOptions({ inheritAttrs: false });
// #endif

interface DIWAppPageProps {
	mode?: 0 | 1 | '0' | '1';

	// 根结点 class
	customClass?: DIWClassType;

	// 根结点 style
	customStyle?: DIWStyleType;

	// 标题
	title?: string;

	// 页面ID
	id?: string;

	// 为 true 时，试图在离开页面时提示用户确认
	dirty?: boolean;

	// 用户试图离开页面时看到的提示消息
	leaveMessage?: string;

	// 是否显示浮动按钮
	floatButton?: boolean;

	// 浮动按钮初始位置
	floatButtonPosition?: { x: number; y: number };

	// 是否隐藏底部导航栏
	hideBottomBar?: boolean;
}

const props = withDefaults(defineProps<DIWAppPageProps>(), {
	mode: 0,
	dirty: false,
	leaveMessage: '是否放弃当前内容？',
	floatButton: false,
	hideBottomBar: false,
	floatButtonPosition: () => ({ x: 30, y: 300 }),
});

const emit = defineEmits<{
	(e: 'update:floatButtonPosition', position: { x: number; y: number }): void;
}>();

const mode = computed(() => (props.mode === '0' ? 0 : props.mode === '1' ? 1 : props.mode));
const customClass = computed(() => parseClass(props.customClass));
const customStyle = computed(() => parseStyle(props.customStyle));

const sessionLost = ref(false);

let forceLeave = false;

function showLoading() {
	uni.showLoading({ mask: true });
}

function hideLoading() {
	uni.hideLoading();
}

function showMessage(message: string) {
	uni.showToast({ icon: 'success', title: message });
}

function showErrorMessage(message: string) {
	console.error(message);
	// uni.showToast({ icon: 'error', title: message });
	uni.showModal({
		title: '错误',
		content: message,
		showCancel: false,
	});
}

function showError(err: any) {
	if (isAbortError(err)) {
		return;
	}

	if (err) {
		if (typeof err.msg === 'string') {
			showErrorMessage(err.msg);
			return;
		}

		if (err instanceof Error) {
			if ((err as any).$noSession === true) {
				sessionLost.value = true;
				return;
			}

			showErrorMessage(err.message);
			return;
		}

		// #ifdef H5
		if (err instanceof Blob) {
			if (err.type === 'application/json') {
				err
					.text()
					.then((d) => {
						return JSON.parse(d);
					})
					.then((e) => {
						if (typeof e.msg === 'string') {
							showErrorMessage(e.msg);
						}
					});
			}
		}
		// #endif

		console.error(typeof err, err);
	}
}

async function confirm(msg: string) {
	const d = await uni.showModal({ title: '确认', content: msg });
	if (d.cancel) {
		throwAbortError();
	}
}

const interfaceDIWAppPageRoot = injectDIWAppPageRoot();
if (interfaceDIWAppPageRoot) {
	interfaceDIWAppPageRoot.registerAppPageImpl({ showError, showErrorMessage, showMessage, showLoading, hideLoading, confirm });
}

watchEffect(() => {
	if (props.title !== undefined) {
		uni.setNavigationBarTitle({ title: props.title });
	}
});

watchEffect(() => {
	if (sessionInfo.value) {
		sessionLost.value = false;
	}
});

// #ifdef MP-WEIXIN

watchEffect(() => {
	if (props.dirty) {
		uni.enableAlertBeforeUnload({ message: props.leaveMessage });
	} else {
		uni.disableAlertBeforeUnload();
	}
});

// #endif

onBackPress(() => {
	if (!forceLeave && props.dirty) {
		confirm(props.leaveMessage)
			.then(() => {
				nextTick(() => {
					forceLeave = true;
					navigateBack();
				});
			})
			.catch(() => {});
		return true;
	}
});

let onPullDownRefreshCallback: (() => void) | null = null;
let onReachBottomCallback: (() => void) | null = null;

onPullDownRefresh(() => {
	if (onPullDownRefreshCallback) {
		onPullDownRefreshCallback();
	}
});

onReachBottom(() => {
	if (onReachBottomCallback) {
		onReachBottomCallback();
	}
});

provideDIWScrollManager({
	onPulldownRefresh(callback) {
		onPullDownRefreshCallback = callback;
	},

	onReachBottom(callback) {
		onReachBottomCallback = callback;
	},

	startPulldownRefresh() {
		console.log('DIWAppPage startPulldownRefresh');
		uni.startPullDownRefresh();
	},

	stopPulldownRefresh() {
		uni.stopPullDownRefresh();
	},

	scrollToSelector(selector: string) {
		console.log('scrollToSelector', selector);
		uni.pageScrollTo({ selector });
	},
});

let clickHandler = undefined;

if (__CONFIG_IS_DEV_MODE__) {
	clickHandler = (function () {
		let clickCount = 0;
		let clickTime = 0;

		const { hack } = useSystemStore();
		const { close } = useIMStore();

		function showMenu() {
			protect(async () => {
				const isNetError = hack(5) === true;
				const isNetDelay = hack(6) === true;
				const itemList: string[] = [
					'下次网络调用出错',
					isNetError ? '关闭【网络调用持续出错】' : '打开【网络调用持续出错】',
					isNetDelay ? '关闭【模拟网络延迟】' : '打开【模拟网络延迟】',
					'断开 WebSocket',
				];

				const d = await uni.showActionSheet({
					title: '开发工具',
					itemList,
				});
				switch (d.tapIndex) {
					case 0:
						hack(0);
						uni.showToast({ title: '下次接口调用将报错', icon: 'error' });
						break;
					case 1:
						hack(isNetError ? 2 : 1);
						uni.showToast({ title: isNetError ? '网络接口调用恢复正常' : '所有网络接口调用将失败', icon: 'error' });
						break;
					case 2:
						hack(isNetDelay ? 4 : 3);
						uni.showToast({ title: isNetDelay ? '网络延迟模拟已关闭' : '网络延迟模拟已打开', icon: 'error' });
						break;
					case 3:
						close();
						break;
				}
			});
		}

		function handleClick(ev: { timeStamp: any }) {
			if (clickCount++ === 0) {
				clickTime = ev.timeStamp;
			} else {
				if (ev.timeStamp - clickTime <= 1000) {
					if (clickCount >= 7) {
						clickCount = 0;
						showMenu();
					}
				} else {
					clickCount = 0;
				}
			}
		}

		return handleClick;
	})();
}

onShow(() => {});

onHide(() => {});
</script>

<style lang="scss">
.diw-app-page {
	&.diw-app-page-sv {
		position: absolute;
		inset: 0;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	&__main {
		flex: 1;
		position: relative;
	}

	&__float-button {
		position: fixed;
		z-index: 999;
		transition: box-shadow 0.3s ease;

		&--dragging {
			box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.3);
		}
	}
}
</style>
