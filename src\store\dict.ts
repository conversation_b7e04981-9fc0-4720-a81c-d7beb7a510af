import { useSystemStore } from './system';

interface DictItem {
	value: string;
	label: string;
}

export const useDictStore = defineStore('dict', () => {
	const { apiGet } = useSystemStore();

	const cache = new Map<string, DictItem[]>();

	function useDict(...types: string[]) {
		return useDictImpl(undefined, ...types);
	}
	function useDict2(callback: () => void, ...types: string[]) {
		return useDictImpl(callback, ...types);
	}
	function useDictImpl(callback?: () => void, ...types: string[]) {
		const result = ref<Record<string, DictItem[]>>({});

		const missingTypes: string[] = [];

		types.forEach((type) => {
			const d = cache.get(type);
			if (d) {
				result.value[type] = d;
			} else {
				result.value[type] = [];
				missingTypes.push(type);
			}
		});

		if (missingTypes.length > 0) {
			apiGet({ url: 'admin/dict/types', params: { types: missingTypes } })
				.then((d) => {
					Object.keys(d).forEach((key) => {
						const d1 = d[key];
						const result1 = Object.keys(d1).map((e) => ({ value: e, label: d1[e] }));
						cache.set(key, result1);
						result.value[key] = result1;
					});
				})
				.catch((err) => {
					console.error(err);
				})
				.finally(() => {
					if (callback) {
						nextTick(callback);
					}
				});
		} else {
			if (callback) {
				nextTick(callback);
			}
		}

		return toRefs(result.value);
	}
	async function getDictItems(code: string): Promise<DictItem[]> {
		const d = cache.get(code);
		if (d) {
			return d;
		}

		return getDictItemsImpl(code);
	}

	async function getDictItemsImpl(code: string): Promise<DictItem[]> {
		const d = await apiGet({ url: 'admin/dict/types', params: { types: [code] } });
		const d1 = d[code];
		if (d1 && typeof d1 === 'object') {
			const result = Object.keys(d1).map((e) => ({ value: e, label: d1[e] }));
			cache.set(code, result);
			return result;
		}
		return [];
	}

	return { getDictItems, useDict, useDict2 };
});
