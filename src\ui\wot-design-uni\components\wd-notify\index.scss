@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

@include b(notify) {
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: $wot-notify-padding;
	font-size: $wot-notify-font-size;
	line-height: $wot-notify-line-height;
	color: $wot-notify-text-color;

	// allow newline character
	white-space: pre-wrap;
	text-align: center;
	word-wrap: break-word;

	@include m(primary) {
		background: $wot-notify-primary-background;
	}

	@include m(success) {
		background: $wot-notify-success-background;
	}

	@include m(danger) {
		background: $wot-notify-danger-background;
	}

	@include m(warning) {
		background: $wot-notify-warning-background;
	}
}
