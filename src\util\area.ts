export function encodeArea(o: Record<string, any>, n1: string, n2: string, n3: string) {
	return [toString(o[n1]), toString(o[n2]), toString(o[n3])];
}

export function decodeArea(area: any, n1: string, n2: string, n3: string) {
	if (Array.isArray(area)) {
		return {
			[n1]: area[0],
			[n2]: area[1],
			[n3]: area[2],
		};
	}

	return {
		[n1]: undefined,
		[n2]: undefined,
		[n3]: undefined,
	};
}

function toString(s: any) {
	if (s === null || s === undefined) {
		return '';
	}

	if (typeof s === 'string') {
		return s;
	}

	return '' + s;
}
