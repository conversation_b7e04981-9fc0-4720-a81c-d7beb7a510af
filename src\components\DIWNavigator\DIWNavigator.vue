<template>
	<view class="diw-navigator" :class="customClass" :hover-class="hoverClass" :style="customStyle" @click="handleClick">
		<template v-if="$slots.default">
			<slot />
		</template>
		<template v-else>
			<template v-if="props.text">
				<wd-cell is-link :title="props.text" />
			</template>
		</template>
	</view>
</template>

<script setup lang="ts">
interface Props {
	url: string;
	openType?: 'reLaunch' | 'switchTab' | 'redirect' | 'navigate' | 'navigateBack';
	text?: string;
	title?: string;
	customClass?: string;
	hoverClass?: string;
	customStyle?: string;
}

const props = withDefaults(defineProps<Props>(), { openType: 'navigate' });

const customClass = computed(() => parseClass(props.customClass));
const hoverClass = computed(() => parseClass(props.hoverClass));
const customStyle = computed(() => parseStyle(props.customStyle));

const { protect, navigateTo } = useFramework();

function handleClick() {
	protect(async () => {
		switch (props.openType) {
			case 'navigate':
				await navigateTo({ url: props.url, title: props.title });
				break;

			case 'reLaunch':
				await navigateTo({ url: props.url, mode: 'reLaunch' });
				break;

			case 'switchTab':
				await navigateTo({ url: props.url, mode: 'switchTab' });
				break;

			case 'redirect':
				await navigateTo({ url: props.url, mode: 'redirect' });
				break;
		}
	});
}
</script>
