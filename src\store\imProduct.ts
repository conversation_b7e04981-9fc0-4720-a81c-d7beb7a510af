import { useSystemStore } from './system';
import { useMessageStore } from './message';

export const useProductStore = defineStore('diw_product_store', () => {
	const ss = useSystemStore();
	const { apiGet } = ss;
	const messageStore = useMessageStore();

	const productCache = new Map<string, Record<string, any>>();

	// 添加缓存时间戳，用于缓存过期管理
	const productCacheTimestamp = new Map<string, number>();

	// 缓存有效期（5分钟）
	const CACHE_DURATION = 5 * 60 * 1000;

	async function getProductDetail(productId: string): Promise<Record<string, any>> {
		// 检查缓存是否过期
		const cacheTime = productCacheTimestamp.get(productId);
		const now = Date.now();

		if (cacheTime && now - cacheTime > CACHE_DURATION) {
			// 缓存过期，清除
			productCache.delete(productId);
			productCacheTimestamp.delete(productId);
		}

		const cached = productCache.get(productId);
		if (cached) {
			console.log('从缓存获取商品详情:', productId);
			return cached;
		}

		console.log('从API获取商品详情:', productId);
		try {
			const d = await apiGet({ url: `market/home/<USER>/${productId}` });
			productCache.set(productId, d);
			productCacheTimestamp.set(productId, now);
			return d;
		} catch (error) {
			console.error('获取商品详情失败:', productId, error);
			throw error;
		}
	}

	// 清除普通商品缓存中的特定商品
	function clearProductCache(productId: string) {
		if (productCache.has(productId)) {
			productCache.delete(productId);
			productCacheTimestamp.delete(productId);
			console.log('清除商品缓存:', productId);
		}
	}

	// 清除所有普通商品缓存
	function clearAllProductCache() {
		const cacheSize = productCache.size;
		productCache.clear();
		productCacheTimestamp.clear();
		console.log(`清除所有商品缓存，共 ${cacheSize} 项`);
	}

	// 定期清理过期缓存
	function cleanExpiredCache() {
		const now = Date.now();
		let cleanedCount = 0;

		// 清理普通商品缓存
		for (const [productId, timestamp] of productCacheTimestamp.entries()) {
			if (now - timestamp > CACHE_DURATION) {
				productCache.delete(productId);
				productCacheTimestamp.delete(productId);
				cleanedCount++;
			}
		}

		if (cleanedCount > 0) {
			console.log(`清理过期缓存，共 ${cleanedCount} 项`);
		}
	}

	// 启动定期清理任务
	const cleanupTimer = setInterval(cleanExpiredCache, 60 * 1000); // 每分钟清理一次

	// 用于退出登录时清空缓存
	function flush() {
		clearInterval(cleanupTimer);
		productCache.clear();
		productCacheTimestamp.clear();
		console.log('清空所有商品缓存');
	}

	// 获取缓存统计信息
	function getCacheStats() {
		return {
			productCacheSize: productCache.size,
			totalCacheSize: productCache.size,
		};
	}

	// 订阅用户登出消息，清空所有缓存
	messageStore.subscribeMessage('userLogout', () => {
		console.log('用户登出，清空商品缓存');
		flush();
	});

	return {
		getProductDetail,
		clearProductCache,
		clearAllProductCache,
		cleanExpiredCache,
		getCacheStats,
		flush,
	};
});
