import { useSystemStore } from './system';

export const useStatStore = defineStore('stat', () => {
	const ss = useSystemStore();
	const { apiGet } = ss;

	const workbenchStat = ref<Record<string, any> | null>(null);

	async function reloadWorkbenchStat() {
		const d = await apiGet('order/workbench/stat');
		workbenchStat.value = d;
	}

	function clearWorkbenchStat() {
		workbenchStat.value = null;
	}

	return { reloadWorkbenchStat, clearWorkbenchStat, workbenchStat };
});
