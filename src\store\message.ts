import { useSystemStore } from './system';
import { connectHub } from '@/util/transport';

const PING_MESSAGE = JSON.stringify({ type: 'ping' });

// 多久发送一次 PING 包
const PING_INTERVAL = 10 * 1000;

// 多久没收到任何消息则认为是断线了
const MESSAGE_DEAD_INTERVAL = 60 * 1000;

// 检测到断线后，等待多久开始尝试重连
const RECONNECT_DELAY = 3 * 1000;

export const useMessageStore = defineStore('dw_message', () => {
	const ss = useSystemStore();
	const { apiGet, apiPost } = ss;

	let socketTask: IMTransport | null = null;

	let pendingConnection: ReturnType<typeof connectHub> | null = null;

	let keepAliveTimer: number | null = null;
	let reconnectTimer: number | null = null;
	let lastReceivedTime: number | null = null;

	const hasUnreadMessage = ref(false);
	const totalUnreadMessage = ref(0);

	let checkInProgress = false;

	const subscriptions = new Map<string, Array<(msg: Record<string, any>) => void>>();

	const checkMessages = useDebounceFn(() => {
		if (checkInProgress) {
			return;
		}

		checkInProgress = true;
		apiGet({
			url: 'msg/sysMessage/user/page',
			params: {
				size: 1,
				current: 1,
				category: '1',
				readFlag: '0',
			},
		})
			.then((d) => {
				totalUnreadMessage.value = d.total;
				hasUnreadMessage.value = d.total > 0;
			})
			.catch((err) => {
				// 即使出错，也不要出现任何提示
				console.error('检查消息失败:', err);
			})
			.finally(() => {
				checkInProgress = false;
			});
	}, 1000);

	function handleMessage(msg: Record<string, any>) {
		lastReceivedTime = Date.now();

		console.log('收到WebSocket消息:', msg);

		switch (msg.type) {
			case 'notice':
				checkMessages();
				break;

			default:
				const ls = subscriptions.get(msg.type);
				if (ls) {
					ls.forEach((callback) => {
						callback(msg);
					});
				}
				break;
		}
	}

	function subscribeMessage(type: string, callback: (msg: Record<string, any>) => void) {
		const ls = subscriptions.get(type);
		if (ls) {
			if (!ls.includes(callback)) {
				ls.push(callback);
			}
		} else {
			subscriptions.set(type, [callback]);
		}
	}

	function unsubscribeMessage(type: string, callback: (msg: Record<string, any>) => void) {
		const ls = subscriptions.get(type);
		if (ls) {
			const index = ls.indexOf(callback);
			if (index > -1) {
				ls.splice(index, 1);
			}
			if (ls.length === 0) {
				subscriptions.delete(type);
			}
		}
	}

	async function publishMessage(type: string, data: Record<string, any> = {}) {
		const ls = subscriptions.get(type);
		if (ls) {
			ls.forEach((callback) => {
				try {
					callback({ type, ...data });
				} catch (error) {
					console.error('消息发布回调执行失败:', error);
				}
			});
		}
	}

	async function markMessageAsRead(id: string) {
		try {
			await apiPost({ url: 'msg/sysMessage/read?id=' + id });
			checkMessages();
		} catch (error) {
			console.error('标记消息已读失败:', error);
		}
	}

	async function markAllMessagesAsRead() {
		try {
			await apiGet('msg/sysMessage/readBatch');
			checkMessages();
		} catch (error) {
			console.error('批量标记消息已读失败:', error);
		}
	}

	async function restart(accessToken: string, tenantId: string, forceReload = true) {
		await stop();

		if (!accessToken || !tenantId) {
			console.log('WebSocket连接缺少必要参数');
			return;
		}

		console.log('开始连接WebSocket...');
		pendingConnection = connectHub(accessToken, tenantId);

		pendingConnection.task
			.then((ws) => {
				console.log('WebSocket连接成功!', ws);

				keepAliveTimer = setInterval(() => {
					if (socketTask) {
						socketTask.send(PING_MESSAGE);
					}

					if (lastReceivedTime != null && Date.now() - lastReceivedTime > MESSAGE_DEAD_INTERVAL) {
						console.log('检测到WebSocket连接断开，准备重连...');
						if (socketTask) {
							socketTask.close();
						}
					}

					if (!socketTask) {
						reconnectTimer = setTimeout(() => {
							console.log('开始重连WebSocket...');
							restart(accessToken, tenantId, false);
						}, RECONNECT_DELAY);
					}
				}, PING_INTERVAL);

				ws.onClose(() => {
					console.log('WebSocket连接关闭');
					socketTask = null;
				});

				ws.onError(() => {
					console.error('WebSocket连接错误');
					socketTask = null;
				});

				ws.onMessage((data) => {
					if (typeof data === 'string') {
						try {
							const msg = JSON.parse(data);
							handleMessage(msg);
						} catch (err) {
							console.error('解析WebSocket消息失败:', err, '原始数据:', data);
						}
					}
				});

				socketTask = ws;
				lastReceivedTime = Date.now();
			})
			.catch((err) => {
				console.error('WebSocket连接失败:', err);
			});

		checkMessages();
	}

	async function stop() {
		console.log('停止WebSocket连接...');

		if (pendingConnection) {
			pendingConnection.abort();
			pendingConnection = null;
		}

		if (socketTask) {
			socketTask.close();
			socketTask = null;
		}

		if (reconnectTimer) {
			clearTimeout(reconnectTimer);
			reconnectTimer = null;
		}

		if (keepAliveTimer) {
			clearInterval(keepAliveTimer);
			keepAliveTimer = null;
		}

		lastReceivedTime = null;
	}

	return {
		restart,
		stop,
		hasUnreadMessage,
		totalUnreadMessage,
		checkMessages,
		markMessageAsRead,
		markAllMessagesAsRead,
		subscribeMessage,
		unsubscribeMessage,
		publishMessage,
	};
});
