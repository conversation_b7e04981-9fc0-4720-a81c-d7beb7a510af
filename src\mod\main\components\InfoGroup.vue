<template>
	<view class="info-group-card">
		<view class="info-group-header">
			<text class="info-group-title">{{ props.title }}</text>
		</view>
		<view class="info-group-content">
			<slot />
		</view>
	</view>
</template>

<script setup lang="ts">
const props = defineProps<{ title: string }>();

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		styleIsolation: 'shared',
	},
});

// #endif
</script>

<style lang="scss" scoped>
.info-group-card {
	margin: 16rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;

	.info-group-header {
		padding: 24rpx 32rpx 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
		background: #ffffff;

		.info-group-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
		}
	}

	.info-group-content {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		background: #ffffff;

		// 确保子元素能够正确换行
		& > * {
			min-width: 50%;
			max-width: 100%;
		}

		// 处理.w-full类的元素
		& > .w-full {
			min-width: 100%;
		}

		// 处理.w-half类的元素
		& > .w-half {
			min-width: 50%;
			max-width: 50%;
		}
	}
}
</style>
