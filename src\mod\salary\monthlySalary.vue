<template>
	<DIWAppPage title="%monthlySalaryHistory%">
		<DIWScrollView>
			<view class="monthly-salary-container">
				<!-- 顶部月份和工资信息 -->
				<view class="month-salary-header">
					<view class="month-info">
						<text class="month-icon">📅</text>
						<text class="month-text">{{ currentMonthText }}</text>
					</view>
					<view class="salary-info">
						<text class="salary-label">{{ t('monthlyIncome') }}：</text>
						<text class="salary-amount">{{ formatNumber(monthSalary, 2) }}</text>
						<text class="salary-unit">{{ t('yuan') }}</text>
					</view>
				</view>

				<!-- 日工资详细记录 -->
				<view class="daily-details">
					<view class="daily-details__header">
						<view class="header-item">{{ t('date') }}</view>
						<view class="header-item">{{ t('shift') }}</view>
						<view class="header-item">{{ t('currentShiftEstimatedIncome') }}</view>
						<view class="header-item">{{ t('standCount') }}</view>
					</view>

					<view class="daily-details__list">
						<view v-for="(item, index) in dailyDetailRecords" :key="index" class="daily-detail-item">
							<view class="detail-field">{{ item.date }}</view>
							<view class="detail-field">
								<text class="shift-badge" :class="item.shiftType === 'morning' ? 'shift-morning' : 'shift-evening'">
									{{ item.shiftType === 'morning' ? t('morningShift') : t('eveningShift') }}
								</text>
							</view>
							<view class="detail-field amount-field">{{ formatNumber(item.income, 2) }}</view>
							<view class="detail-field">{{ item.standCount }}</view>
						</view>
					</view>
				</view>
				<!-- 确定按钮 -->
				<view class="confirm-section">
					<wd-button type="primary" custom-class="confirm-button" size="large" @click="handleConfirm">{{ t('confirm') }}</wd-button>
				</view>
			</view>
		</DIWScrollView>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { formatNumber } from '@/util/number';

const { usePageQuery, navigateBack } = useFramework();

// 使用国际化
const { t } = useI18n();

// 获取页面参数
const pq = usePageQuery();
const currentYear = ref(parseInt(pq.value.year) || 2025);
const currentMonth = ref(pq.value.month || '06');

// 当前月份显示文本
const currentMonthText = computed(() => {
	return `${currentYear.value}年${parseInt(currentMonth.value)}月`;
});

// 月工资总额
const monthSalary = ref(6985.34);

// 详细日记录数据
const dailyDetailRecords = ref([
	{ date: '6-01', shiftType: 'morning', income: 298.65, standCount: 5 },
	{ date: '6-02', shiftType: 'morning', income: 298.65, standCount: 6 },
	{ date: '6-04', shiftType: 'evening', income: 298.65, standCount: 5 },
	{ date: '6-05', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-06', shiftType: 'evening', income: 298.65, standCount: 5 },
	{ date: '6-08', shiftType: 'morning', income: 298.65, standCount: 6 },
	{ date: '6-09', shiftType: 'morning', income: 298.65, standCount: 5 },
	{ date: '6-10', shiftType: 'evening', income: 298.65, standCount: 3 },
	{ date: '6-12', shiftType: 'morning', income: 298.65, standCount: 5 },
	{ date: '6-13', shiftType: 'morning', income: 298.65, standCount: 6 },
	{ date: '6-14', shiftType: 'evening', income: 298.65, standCount: 5 },
	{ date: '6-15', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-17', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-18', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-19', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-21', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-22', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-23', shiftType: 'morning', income: 298.65, standCount: 4 },
	{ date: '6-24', shiftType: 'morning', income: 298.65, standCount: 4 },
]);

// 根据参数加载对应月份的数据
onMounted(() => {
	loadMonthData();
});

// 加载月份数据
function loadMonthData() {
	// 根据年月参数加载对应的数据
	// 这里可以调用API获取实际数据
	const monthData = getMonthDataByYearMonth(currentYear.value, currentMonth.value);
	if (monthData) {
		monthSalary.value = monthData.salary;
		dailyDetailRecords.value = monthData.records;
	}
}

// 获取指定年月的数据（模拟数据）
function getMonthDataByYearMonth(year: number, month: string) {
	const monthDataMap: Record<string, any> = {
		'202506': {
			salary: 6985.34,
			records: dailyDetailRecords.value,
		},
		'202505': {
			salary: 7245.15,
			records: [
				{ date: '5-01', shiftType: 'morning', income: 298.65, standCount: 5 },
				{ date: '5-02', shiftType: 'evening', income: 298.65, standCount: 4 },
				// ... 更多5月数据
			],
		},
		'202504': {
			salary: 7018.26,
			records: [
				{ date: '4-01', shiftType: 'morning', income: 298.65, standCount: 6 },
				{ date: '4-02', shiftType: 'morning', income: 298.65, standCount: 5 },
				// ... 更多4月数据
			],
		},
	};

	return monthDataMap[`${year}${month}`];
}
// 处理确定按钮点击
function handleConfirm() {
	navigateBack();
}
</script>

<style lang="scss" scoped>
.monthly-salary-container {
	padding: 24rpx;
	background-color: #f5f7fa;
	min-height: 100vh;
}

/* 顶部月份和工资信息样式 */
.month-salary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

	.month-info {
		display: flex;
		align-items: center;

		.month-icon {
			font-size: 32rpx;
			margin-right: 12rpx;
		}

		.month-text {
			font-size: 32rpx;
			font-weight: 600;
			color: #1d2129;
		}
	}

	.salary-info {
		display: flex;
		align-items: baseline;

		.salary-label {
			font-size: 28rpx;
			color: #86909c;
		}

		.salary-amount {
			font-size: 32rpx;
			font-weight: 700;
			color: #1d2129;
			margin-left: 8rpx;
		}

		.salary-unit {
			font-size: 28rpx;
			font-weight: 500;
			color: #1d2129;
			margin-left: 4rpx;
		}
	}
}

/* 日详细记录样式 */
.daily-details {
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;

	&__header {
		display: flex;
		background-color: #f8f9fa;
		padding: 20rpx 24rpx;
		border-bottom: 1rpx solid #e5e7eb;

		.header-item {
			flex: 1;
			text-align: center;
			font-size: 26rpx;
			font-weight: 600;
			color: #374151;
		}
	}
}

.daily-detail-item {
	display: flex;
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #f3f4f6;

	&:last-child {
		border-bottom: none;
	}

	.detail-field {
		flex: 1;
		text-align: center;
		font-size: 24rpx;
		color: #374151;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 60rpx;
	}

	.amount-field {
		font-weight: 600;
		color: #1f2937;
	}
}

.shift-badge {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: 500;
}

.shift-morning {
	background-color: #dbeafe;
	color: #1d4ed8;
}

.shift-evening {
	background-color: #fef3c7;
	color: #d97706;
}

/* 确定按钮样式 */
.confirm-section {
	padding: 45rpx 0 24rpx 0;
}

.confirm-button {
	width: 100%;
	background: linear-gradient(90deg, #2b6fea 0%, #4397fe 100%);
	border-radius: 42rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 32rpx;
	letter-spacing: 0em;
	/* 纯白 */
	color: #ffffff;
}
</style>
