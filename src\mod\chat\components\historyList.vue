<template>
	<DIWScrollView>
		<!-- 搜索头部 -->
		<view class="history-header">
			<wd-search v-model="searchModel.productName" placeholder="搜索商品" @change="handleSearch" @clear="handleSearch" />

			<view v-if="footprintGroups.length > 0" class="mt-2">
				<wd-select-picker
					label="选择日期"
					type="radio"
					:key="`date-picker-${searchModel.thisDate}-${footprintGroups.length}`"
					v-model="searchModel.thisDate"
					checked-color="#ff5000"
					:columns="dateColumns"
					@confirm="handleDateChange"
					:close-on-click-modal="true"
					:default-value="searchModel.thisDate"
				/>
			</view>
		</view>

		<!-- 商品列表 -->
		<DIWListView ref="listView" :meta="listViewMeta">
			<template #default="{ data }">
				<view class="history-item">
					<view class="product-card">
						<view class="product-image">
							<image :src="data.imageUrl" mode="aspectFill" lazy-load />
							<view v-if="data.status !== 1" class="invalid-badge">已下架</view>
						</view>
						<view class="product-content">
							<view class="tag-area">
								<view class="product-tags">
									<wd-tag v-if="data.productType === 1" type="success" size="small">现货</wd-tag>
									<wd-tag v-if="data.brandCode" type="primary" size="small">DIW</wd-tag>
								</view>
							</view>
							<view class="seller-info">
								<text class="seller-name">{{ data.sellerName || '未知卖家' }}</text>
							</view>
							<view class="product-name" :class="{ 'text-gray-400': data.status !== 1 }">
								{{ data.name }}
							</view>
						</view>
					</view>
					<view class="price-inventory">
						<view class="price">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{ formatPrice(data.price) }}</text>
						</view>
						<view v-if="data.inventoryTon" class="inventory">{{ data.inventoryTon }}+ 吨</view>
					</view>
					<view class="seller-action">
						<wd-button size="small" type="primary" custom-class="send-btn" @click.stop="handleSend(data)">发送</wd-button>
					</view>
				</view>
			</template>

			<template #empty>
				<view class="empty-state">
					<text class="text-gray-500">{{ getEmptyDescription() }}</text>
					<wd-button v-if="loadError" type="primary" size="small" class="mt-3" @click="loadFootprintsByDay">重新加载</wd-button>
				</view>
			</template>
		</DIWListView>
	</DIWScrollView>
</template>

<script setup lang="ts">
import { debounce } from '@/ui/wot-design-uni/components/common/util';

const emit = defineEmits<{ select: [Record<string, any>] }>();

const { apiGet, protect, sessionInfo, parseImageFiles } = useFramework();

// 状态变量
const loadError = ref(false);
const footprintGroups = ref<any[]>([]);

// 搜索参数
const searchModel = reactive({
	productName: '',
	current: 1,
	size: 20,
	thisDate: formatDate(new Date()), // 默认为今天
});

const listView = ref();

// 处理搜索
const handleSearch = debounce(() => {
	listView.value?.reload();
}, 300);

// 格式化日期为yyyy-MM-dd格式
function formatDate(date: Date): string {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
}

// 日期选择器数据
const dateColumns = computed(() => {
	const columns = footprintGroups.value.map((group) => ({
		value: group.thisDate,
		label: formatDateTitle(group.thisDate),
	}));

	// 确保当前选中的日期在列表中
	if (searchModel.thisDate && !columns.find((col) => col.value === searchModel.thisDate)) {
		columns.unshift({
			value: searchModel.thisDate,
			label: formatDateTitle(searchModel.thisDate),
		});
	}

	return columns;
});

// 先获取日期列表
function loadFootprintsByDay() {
	if (!sessionInfo.value) return;

	loadError.value = false;

	const dayParams = {
		productName: searchModel.productName,
		endDate: '', // 最后一天，空表示第一次查询
		dayNum: 10, // 一次查询10天
		size: 5, // 每天显示5条
	};

	protect(async () => {
		const response = await apiGet({
			url: 'market/act/footprints/day/page',
			params: dayParams,
		});

		// 处理每个日期组的数据
		if (Array.isArray(response) && response.length > 0) {
			footprintGroups.value = response;

			// 默认选中今天，如果今天没有数据则选择最近的日期
			const todayStr = formatDate(new Date());
			const todayData = response.find((item) => item.thisDate === todayStr);

			if (todayData) {
				searchModel.thisDate = todayStr;
			} else if (response[0] && response[0].thisDate) {
				searchModel.thisDate = response[0].thisDate;
			}

			// 确保选择器能正确显示默认值
			nextTick(() => {
				// 强制更新选择器的显示值
				if (listView.value) {
					listView.value.reload();
				}
			});
		} else {
			// 没有足迹数据，默认设置为今天
			footprintGroups.value = [];
			searchModel.thisDate = formatDate(new Date());
		}
	}).catch((err) => {
		console.error('Failed to load footprints by day:', err);
		loadError.value = true;
		// 即使加载失败，也设置默认日期为今天
		searchModel.thisDate = formatDate(new Date());
	});
}

// DIWListView配置
const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(trackInfo) {
			if (!sessionInfo.value || !searchModel.thisDate) {
				return { hasMore: false, items: [], trackInfo: 1 };
			}

			const size1 = 20;
			const size2 = 10;

			const isReload = trackInfo === undefined;
			let current = isReload ? 1 : trackInfo;
			let size = isReload ? size1 : size2;

			const response = await apiGet({
				url: 'market/act/footprints/page',
				params: {
					...searchModel,
					current,
					size,
				},
			});

			const items = (response.records || []).map((item: any) => {
				const imageFiles = parseImageFiles(item.imageFileIds);
				return {
					...item,
					imageUrl: imageFiles.length > 0 ? imageFiles[0].url : '',
					imageList: imageFiles.map((img: any) => img.url),
				};
			});

			if (isReload) {
				return { hasMore: response.total > size, items, trackInfo: 1 + size1 / size2 };
			}

			return { hasMore: response.total > current * size, items, trackInfo: current + 1 };
		},
	});
});

function formatPrice(price: number | string) {
	if (typeof price === 'undefined') return '0.00';
	return Number(price).toFixed(2);
}

function getEmptyDescription() {
	if (!sessionInfo.value) return '请先登录查看您的浏览记录';
	if (loadError.value) return '加载浏览记录失败';
	if (!searchModel.productName) return '暂无浏览记录';
	return '没有找到匹配的商品';
}

function handleSend(product: Record<string, any>) {
	// 确保发送的是完整的产品信息
	console.log('选择商品:', product);
	emit('select', product);
}

// 格式化日期标题
function formatDateTitle(dateStr: string) {
	const today = new Date();
	const todayStr = formatDate(today);

	const yesterday = new Date(today);
	yesterday.setDate(yesterday.getDate() - 1);
	const yesterdayStr = formatDate(yesterday);

	// 提取月和日
	const parts = dateStr.split('-');
	const monthDay = `${parts[1]}月${parts[2]}日`;

	if (dateStr === todayStr) {
		return `${monthDay} 今天`;
	} else if (dateStr === yesterdayStr) {
		return `${monthDay} 昨天`;
	} else {
		return monthDay;
	}
}

// 处理日期变更
function handleDateChange() {
	listView.value?.reload();
}

// 监听日期数据变化，确保选择器显示正确的默认值
watch(
	() => [footprintGroups.value, searchModel.thisDate],
	() => {
		// 当数据加载完成且有默认日期时，确保选择器显示正确
		if (footprintGroups.value.length > 0 && searchModel.thisDate) {
			nextTick(() => {
				// 触发选择器重新渲染
				const event = new CustomEvent('update-picker');
				document.dispatchEvent(event);
			});
		}
	},
	{ deep: true, immediate: true }
);

// 生命周期钩子 - 只在首次挂载时加载数据
onMounted(() => {
	if (sessionInfo.value) {
		loadFootprintsByDay();
	}
});

// 手动刷新数据的方法
function refresh() {
	if (sessionInfo.value) {
		loadFootprintsByDay();
		listView.value?.reload();
	}
}

// 暴露给父组件的方法
defineExpose({
	refresh,
});
</script>

<style scoped lang="scss">
.history-header {
	background-color: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.history-item {
	background-color: white;
	margin: 16rpx;
	border-radius: 16rpx;
	overflow: hidden;
	border: 1rpx solid #f0f0f0;
}

.product-card {
	display: flex;
	padding: 24rpx 24rpx 0 24rpx;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	position: relative;
	flex-shrink: 0;
	border-radius: 12rpx;
	overflow: hidden;
	margin-right: 24rpx;
	border: 1rpx solid #f5f5f5;

	image {
		width: 100%;
		height: 100%;
	}

	.invalid-badge {
		position: absolute;
		inset: 0;
		background-color: rgba(0, 0, 0, 0.4);
		color: white;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.product-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-width: 0;
}

.tag-area {
	display: flex;
	margin-bottom: 12rpx;
}

.product-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.product-name {
	font-size: 28rpx;
	line-height: 1.4;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	&.text-gray-400 {
		color: #999;
	}
}

.price-inventory {
	padding: 0 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.price {
	display: flex;
	align-items: baseline;

	.price-symbol {
		font-size: 24rpx;
		color: #ff5000;
		font-weight: 500;
	}

	.price-value {
		font-size: 32rpx;
		color: #ff5000;
		font-weight: 600;
	}
}

.inventory {
	font-size: 24rpx;
	color: #666;
}

.seller-action {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-right: 24rpx;
	margin-bottom: 24rpx;
}

.seller-info {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;

	.seller-name {
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 240rpx;
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 32rpx;
	font-size: 28rpx;
}
:deep(.send-btn) {
	background-color: #ff5000 !important;
	border: none !important;
	border-radius: 20rpx !important;
	padding: 0 32rpx !important;
	height: 60rpx !important;
	font-size: 24rpx !important;
	color: #ffffff !important;
}

// 选择器主题色
:deep(.wd-select-picker) {
	.wd-picker__confirm {
		color: #ff5000 !important;
	}

	.wd-picker__cancel {
		color: #666 !important;
	}
}

// 日期选择器样式
:deep(.wd-select-picker__field) {
	border: 1rpx solid #e5e5e5 !important;
	border-radius: 12rpx !important;
	background-color: #f8f9fa !important;

	&:focus {
		border-color: #ff5000 !important;
	}
}
:deep(.wd-select-picker__footer) {
	.wd-button.is-primary {
		background-color: #ff5000 !important;
		border: none !important;
		border-radius: 20rpx !important;
		padding: 0 32rpx !important;
		height: 72rpx !important;
	}
}
</style>
