<template>
	<view class="diw-text" :class="customClass" :style="customStyle">
		<text v-if="type === 'text'" :class="textClass" :style="textStyle">{{ displayText }}</text>
		<text v-else-if="type === 'select'" :class="textClass" :style="textStyle">{{ displayText }}</text>
		<text v-else-if="type === 'area'" :class="textClass" :style="textStyle">{{ displayText }}</text>
		<text v-if="suffix.visible" class="diw-currency__suffix">{{ suffix.text }}</text>
	</view>
</template>

<script setup lang="ts">
const { apiGet, protect } = useFramework();

const props = withDefaults(
	defineProps<{
		type?: 'textarea' | 'text' | 'select' | 'area';
		value: string | number | null | string[] | undefined;
		suffix?: string;
		code?: string;
		options?: any;
		multiple?: boolean;
		valueProp?: string;
		labelProp?: string;
		separator?: string;
		// 根结点 class
		customClass?: DIWClassType;

		// 根结点 style
		customStyle?: DIWStyleType;

		textClass?: DIWClassType;

		textStyle?: DIWStyleType;
	}>(),
	{ type: 'text', valueProp: 'value', labelProp: 'label', separator: ',' }
);

const customClass = computed(() => parseClass(props.customClass));
const customStyle = computed(() => parseStyle(props.customStyle));

const displayText = computed(() => {
	switch (props.type) {
		case 'text':
		case 'textarea':
			return props.value || '';

		case 'select':
			if (Array.isArray(props.options)) {
				if (props.multiple) {
					if (Array.isArray(props.value)) {
						const ls: string[] = [];
						for (const e of props.value) {
							for (const opt of props.options) {
								if (opt && typeof opt === 'object') {
									if (opt[props.valueProp] == e) {
										ls.push(opt[props.labelProp]);
										break;
									}
								}
							}
						}

						return ls.join(props.separator);
					}
				} else {
					for (const opt of props.options) {
						if (opt && typeof opt === 'object') {
							if (opt[props.valueProp] === props.value) {
								return opt[props.labelProp];
							}
						}
					}

					for (const opt of props.options) {
						if (opt && typeof opt === 'object') {
							if (opt[props.valueProp] == props.value) {
								return opt[props.labelProp];
							}
						}
					}
				}
			} else if (props.code) {
				return translatedText.value;
			}
			return '';

		case 'area':
			return translatedText.value || '';

		default:
			return '';
	}
});

const suffix = computed(() => {
	if (props.suffix) {
		return {
			visible: true,
			text: props.suffix,
		};
	}

	return {
		visible: false,
	};
});

const translatedText = ref('');

watchEffect(() => {
	protect(async () => {
		if (props.type === 'select' && props.code && props.value && !Array.isArray(props.value)) {
			switch (props.code) {
				case 'factory': {
					const d = await apiGet('admin/company/factory/' + encodeURI('' + props.value));
					if (d) {
						translatedText.value = d.name;
					}
					break;
				}
				case 'brand': {
					const d = await apiGet('market/product/brand/' + encodeURI('' + props.value));
					if (d) {
						translatedText.value = d.name;
					}
					break;
				}
				case 'seller': {
					const d = await apiGet('admin/sellerInfo/' + encodeURI('' + props.value));
					if (d) {
						translatedText.value = d.name;
					}
					break;
				}
			}
		} else if (props.type === 'area' && props.value && Array.isArray(props.value)) {
			const s = new Set<string>();
			for (const e of props.value) {
				if (typeof e === 'string') {
					const f = e.trim();
					if (f) {
						s.add(f);
					}
				}
			}

			const d = await Promise.all(
				[...s.keys()].map(async (e) => ({ c: e, d: await apiGet('admin/sysArea/details?adcode=' + encodeURIComponent(e)) }))
			);
			const m = new Map<string, string>();
			d.forEach((k) => {
				if (k.d && typeof k.d.name === 'string') {
					m.set(k.c, k.d.name);
				}
			});
			const ls2: string[] = [];
			for (const e of [...s.keys()]) {
				const z = m.get(e);
				if (z) {
					ls2.push(z);
				}
			}

			translatedText.value = ls2.join('');
		}
	});
});
</script>
