<template>
	<DIWAppPage mode="1" :title="title" custom-class="mytheme">
		<DIWScrollView v-if="initOk">
			<wd-form ref="form" :model="model" :rules="rules">
				<wd-gap />
				<wd-cell-group border>
					<template v-if="isViewMode">
						<wd-cell title="司机姓名" title-width="33%" :value="model.name" />
						<wd-cell title="司机电话" title-width="33%" :value="model.phone" />
						<wd-cell title="司机身份证" title-width="33%" :value="model.certId" />
						<wd-cell title="提货车牌号" title-width="33%" :value="model.plateNumber" />
						<wd-cell v-if="model.defaultFlag == 1">
							<template #label>
								<text class="text-orange">已设为默认自提信息</text>
							</template>
						</wd-cell>
					</template>
					<template v-else>
						<wd-input
							label="司机姓名"
							prop="name"
							required
							v-model="model.name"
							@focus="handleInputFocus('name')"
							@keyboardheightchange="handleKeyboardHeightChange"
						/>
						<wd-input
							label="司机电话"
							prop="phone"
							type="digit"
							inputmode="tel"
							required
							v-model="model.phone"
							@focus="handleInputFocus('phone')"
							@keyboardheightchange="handleKeyboardHeightChange"
						/>
						<wd-input
							label="司机身份证"
							prop="certId"
							type="idcard"
							required
							v-model="model.certId"
							:maxlength="18"
							@focus="handleInputFocus('certId')"
							@keyboardheightchange="handleKeyboardHeightChange"
						/>
						<wd-input
							label="提货车牌号"
							prop="plateNumber"
							inputmode="none"
							required
							v-model="model.plateNumber"
							@focus="handleInputFocus('plateNumber')"
							@keyboardheightchange="handleKeyboardHeightChange"
						/>

						<wd-cell title="设为默认">
							<wd-switch v-model="model.defaultFlag" :active-value="1" :inactive-value="0" :size="20" />
						</wd-cell>
					</template>
				</wd-cell-group>

				<wd-gap />
				<wd-cell-group border>
					<wd-cell title="备注" vertical>
						<wd-textarea
							v-model="model.remark"
							:readonly="isViewMode"
							:maxlength="100"
							show-word-limit
							@focus="handleInputFocus('remark')"
							@keyboardheightchange="handleKeyboardHeightChange"
						/>
					</wd-cell>
				</wd-cell-group>
				<wd-gap />
			</wd-form>
		</DIWScrollView>
		<template #bottomBar>
			<DIWAuth v-if="initOk" :auth="['admin_pickup_info_edit', 'admin_pickup_info_del']">
				<DIWBottomBar>
					<view class="flex flex-row items-center gap-4 mx-auto line-height-0">
						<template v-if="isViewMode">
							<DIWAuth auth="admin_pickup_info_edit">
								<wd-button @click="isViewMode1 = false">修改</wd-button>
							</DIWAuth>
							<DIWAuth auth="admin_pickup_info_del">
								<wd-button type="error" @click="deleteItem">删除</wd-button>
							</DIWAuth>
						</template>
						<template v-else>
							<wd-button @click="save">保存</wd-button>
							<wd-button type="info" @click="cancelEdit">取消</wd-button>
						</template>
					</view>
				</DIWBottomBar>
			</DIWAuth>
		</template>
		<template #keyboard>
			<wd-keyboard v-model:visible="certIdFocused" extra-key="X" v-model="model.certId" />
			<wd-keyboard v-model:visible="plateNumberFocused" mode="car" v-model="model.plateNumber" />
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { protect, protectOp, validateForm, apiGet, apiPut, apiPost, apiDelete, usePageQuery, usePageResult, sessionInfo, navigateBack } =
	useFramework();

const pq = usePageQuery();
const { finishPage, setPageResult } = usePageResult();

const isViewMode1 = ref(true);
const initOk = ref(false);

const isViewMode = computed(() => {
	if (pq.value.id) {
		return isViewMode1.value;
	}

	return false;
});

const title = computed(() => {
	if (pq.value.id) {
		return isViewMode.value ? '自提信息详情' : '修改自提信息';
	}

	return '添加自提信息';
});

const form = ref();

const rules = {
	name: [{ required: true, message: '请输入司机姓名' }],
	phone: [{ required: true, message: '请输入司机电话' }],
	certId: [{ required: true, message: '请输入司机身份证' }],
	plateNumber: [{ required: true, message: '请输入提货车牌号' }],
};

const model = ref({
	name: '',
	phone: '',
	certId: '',
	plateNumber: '',
	remark: '',
	defaultFlag: 0,
});

const plateNumberFocused = ref(false);
const certIdFocused = ref(false);

onMounted(() => {
	if (!sessionInfo.value) {
		// 没有登录时触发 DIWAppPage 的被动技能
		restartViewMode();
	}
});

watchEffect(() => {
	if (sessionInfo.value) {
		// 一旦用户登录成功，重新加载数据
		restartViewMode();
	}
});

function handleInputFocus(field: string) {
	plateNumberFocused.value = field === 'plateNumber';

	// #ifdef APP-PLUS
	certIdFocused.value = field === 'certId';
	// #endif
}

function handleKeyboardHeightChange(ev: { height: number }) {
	if (ev.height > 0) {
		if (plateNumberFocused.value || certIdFocused.value) {
			uni.hideKeyboard();
		}
	}
}

function reload() {
	protect(async () => {
		if (pq.value.id) {
			const d = await apiGet('admin/pickup/info/' + encodeURI(pq.value.id));
			model.value.name = d.name;
			model.value.phone = d.phone;
			model.value.certId = d.certId;
			model.value.plateNumber = d.plateNumber;
			model.value.defaultFlag = d.defaultFlag;
			model.value.remark = d.remark;
		}
		initOk.value = true;
	});
}

function restartViewMode() {
	initOk.value = false;
	isViewMode1.value = true;
	reload();
}

function cancelEdit() {
	if (pq.value.id) {
		restartViewMode();
	} else {
		navigateBack();
	}
}

function deleteItem() {
	protectOp('继续操作将删除数据', async () => {
		await apiDelete({ url: 'admin/pickup/info', data: [pq.value.id] });
		finishPage();
	});
}

function save() {
	protectOp(async () => {
		await validateForm(form);

		if (pq.value.id) {
			await apiPut({
				url: 'admin/pickup/info',
				data: Object.assign({}, model.value, {
					id: pq.value.id,
				}),
			});

			// 已经成功提交，不管是以何种方式返回上级页面，都需要刷新
			setPageResult();

			restartViewMode();
		} else {
			await apiPost({
				url: 'admin/pickup/info',
				data: Object.assign({}, model.value, {}),
			});

			finishPage();
		}
	});
}
</script>

<style scoped lang="scss">
.mytheme {
	--wot-textarea-cell-padding: 0;
	--wot-textarea-padding: 0;
}
</style>
