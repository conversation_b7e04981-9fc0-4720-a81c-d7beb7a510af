<template>
	<DIWAppPage :title="title">
		<DIWScrollView v-if="pq.mode === 'demoHome1'">
			<DemoHome1 />
		</DIWScrollView>
		<List1 v-else-if="pq.mode === 'list1'" />
		<template v-else-if="pq.mode === 'form1'">
			<DIWScrollView v-if="pq.sv === '1'">
				<Form1 />
			</DIWScrollView>
			<Form1 v-else />
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
import DemoHome1 from './components/demoHome1.vue';
import List1 from './components/list1.vue';
import Form1 from './components/form1.vue';

const props = defineProps<{ mode: string }>();

const { usePageQuery } = useFramework();

const pq = usePageQuery();

const title = computed(() => {
	switch (pq.value.mode) {
		case 'demoHome1':
			return '首页示例';

		case 'list1':
			return '列表';

		case 'form1':
			return '简单表单';
	}
});
</script>
