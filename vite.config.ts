import { defineConfig } from 'vite';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import UnoCSS from 'unocss/vite';
import { resolve } from 'node:path';

import { createRequire } from 'node:module';
const myRequire = createRequire(import.meta.url);
const uni = myRequire('@dcloudio/vite-plugin-uni');

import manifest from './src/manifest.json';

export default defineConfig((config) => {
	const shipping = manifest.h5.devServer.proxy['^/api'].target.indexOf('www.diwyun.cn') >= 0;
	console.log('当前版本为', shipping ? '正式版' : '测试版');

	return {
		css: {
			preprocessorOptions: {
				scss: {
					silenceDeprecations: ['legacy-js-api'],
				},
			},
		},
		resolve: {
			alias: {
				'@': resolve(import.meta.url, 'src'),
			},
		},
		define: {
			// 是否正式版？
			__SHIPPING__: shipping,
			__CONFIG_IS_DEV_MODE__: JSON.stringify(config.mode === 'development'),
		},
		optimizeDeps: {
			exclude: process.env.UNI_PLATFORM === 'h5' && process.env.NODE_ENV === 'development' ? ['wot-design-uni'] : [],
		},
		plugins: [
			AutoImport({
				// 限制扫描的范围
				dirs: [{ glob: 'src/framework/index.ts' }],
				imports: [
					'vue',
					'pinia',
					{
						'@dcloudio/uni-app': [
							'onReady',
							'onLoad',
							'onUnload',
							'onShow',
							'onHide',
							'onLaunch',
							'onPullDownRefresh',
							'onResize',
							'onReachBottom',
							'onPageScroll',
							'onBackPress',
						],
					},
				],
				dts: 'auto-imports.d.ts',
			}),
			Components({
				// 限制扫描的范围
				globs: ['src/components/**/*.vue', 'src/ui/wot-design-uni/components/wd-*/wd-*.vue'],
				dts: 'components.d.ts',
			}),
			UnoCSS(),
			uni.default(),
		],
	};
});
