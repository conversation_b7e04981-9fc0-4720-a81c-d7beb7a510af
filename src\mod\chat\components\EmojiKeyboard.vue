<template>
	<view class="flex flex-col w-full">
		<view class="relative w-full h-260px">
			<scroll-view class="absolute inset-0" scroll-y>
				<view class="flex flex-row flex-wrap gap-2 mt-4">
					<view v-for="c in fixedList" :key="c" class="emoji-key" @click="handleKeyClick(c)">
						<text class="emoji-text">{{ c }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="flex flex-row justify-end h-40px items-center">
			<wd-button size="small" type="info" @click="handleCloseClick">关闭</wd-button>
			<view class="flex-1" />
			<wd-button type="icon" size="small" icon="close-outline" @click="handleBackClick()"></wd-button>
		</view>
		<wd-gap safe-area-bottom height="0" />
	</view>
</template>

<script setup lang="ts">
const emit = defineEmits<{ keyClick: [string]; backClick: []; closeClick: [] }>();

const fixedList = ref([
	'😀',
	'😃',
	'😄',
	'😁',
	'😆',
	'😅',
	'😂',
	'🤣',
	'😭',
	'😉',
	'😗',
	'😙',
	'😚',
	'😘',
	'🥰',
	'😍',
	'🤩',
	'🥳',
	'🫠',
	'🙃',
	'🙂',
	'🥲',
	'🥹',
	'😊',
	'☺️',
	'😌',
	'🙂‍↕️',
	'🙂‍↔️',
	'😏',
	'🤤',
	'😋',
	'😛',
	'😝',
	'😜',
	'🤪',
	'🥴',
	'😔',
	'🥺',
	'😬',
	'😑',
	'😐',
	'😶',
	'😶‍🌫️',
	'🫥',
	'🤐',
	'🫡',
	'🤔',
	'🤫',
	'🫢',
	'🤭',
	'🥱',
	'🤗',
	'🫣',
	'😱',
	'🤨',
	'🧐',
	'😒',
	'🙄',
	'😮‍💨',
	'😤',
	'😠',
	'😡',
	'🤬',
	'😞',
	'😓',
	'😟',
	'😥',
	'😢',
	'☹️',
	'🙁',
	'🫤',
	'😕',
	'😰',
	'😨',
	'😧',
	'😦',
	'😮',
	'😯',
	'😲',
	'😳',
	'🤯',
	'😖',
	'😣',
	'😩',
	'😫',
	'😵',
	'😵‍💫',
	'🫨',
	'🥶',
	'🥵',
	'🤢',
	'🤮',
	'😴',
	'😪',
	'🤧',
	'🤒',
	'🤕',
	'😷',
	'🤥',
	'😇',
	'🤠',
	'🤑',
	'🤓',
	'😎',
	'🥸',
	'🤡',
	'💩',
	'😈',
	'👿',
	'👻',
	'💀',
	'☠️',
	'👹',
	'👺',
	'☃️',
	'⛄',
	'🎃',
	'🤖',
	'👽',
	'👾',
	'🌚',
	'🌝',
	'🌞',
	'🌛',
	'🌜',
	'😺',
	'😸',
	'😹',
	'😻',
	'😼',
	'😽',
	'🙀',
	'😿',
	'😾',
	'🙈',
	'🙉',
	'🙊',
	'💫',
	'⭐',
	'🌟',
	'✨',
	'⚡',
	'💥',
	'💢',
	'💨',
	'💦',
	'💤',
	'🕳️',
	'🔥',
	'💯',
	'🎉',
	'🎊',
	'❤️',
	'🧡',
	'💛',
	'💚',
	'🩵',
	'💙',
	'💜',
	'🤎',
	'🖤',
	'🩶',
	'🤍',
	'🩷',
	'💘',
	'💝',
	'💖',
	'💗',
	'💓',
	'💞',
	'💕',
	'👏',
	'👍',
	'👎',
	'🫶',
	'👌',
	'👉',
	'👈',
	'☝️',
	'👆',
	'👇',
	'🤝',
	'🙏',
]);

function handleKeyClick(c: string) {
	emit('keyClick', c);
}

function handleBackClick() {
	emit('backClick');
}

function handleCloseClick() {
	emit('closeClick');
}
</script>

<style scoped lang="scss">
.emoji-key {
	width: 32px;
	height: 32px;
}

.emoji-text {
	font-size: 2rem;
}
</style>
