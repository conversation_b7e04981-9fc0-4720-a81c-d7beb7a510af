//
// style.ts
// 用于解析组件的 class 和 style 属性
//

function parseClassFromObject(o: Record<string, any>): string[] {
	return Object.keys(o)
		.map((key) => ({ key, value: o[key] }))
		.filter((e) => !!e.value)
		.map((e) => e.key);
}

function parseClassFromArray(o: Array<DIWClassType>): string[] | null {
	const z = o
		.map((e) => {
			if (Array.isArray(e)) {
				return parseClassFromArray(e);
			} else {
				switch (typeof e) {
					case 'string':
						return [e];

					case 'object':
						if (e) {
							return parseClassFromObject(e);
						}
						break;
				}
			}
		})
		.filter((e) => Array.isArray(e));

	const result: string[] = [];
	z.forEach((e) => {
		result.push(...e);
	});

	return result;
}

export function parseClass(clz: DIWClassType | undefined | null): string | undefined {
	if (clz !== undefined && clz !== null) {
		if (Array.isArray(clz)) {
			const z = parseClassFromArray(clz);
			if (z) {
				return z.join(' ');
			}
		} else if (typeof clz === 'string') {
			return clz;
		} else {
			return parseClassFromObject(clz).join(' ');
		}
	}
}

function parseStyleFromArray(o: Array<DIWStyleType>): Record<string, string> {
	let result: Record<string, string> = {};
	const z = o
		.map((e) => {
			if (Array.isArray(e)) {
				return parseStyleFromArray(e);
			} else if (typeof e === 'string') {
				return parseStyleFromString(e);
			} else {
				return parseStyleFromObject(e);
			}
		})
		.filter((e) => !!e);

	z.forEach((e) => {
		result = Object.assign(result, e);
	});

	return result;
}

function parseStyleFromString(o: string): Record<string, string> {
	const result: Record<string, string> = {};
	o.split(';').forEach((e) => {
		const index = e.indexOf(':');
		if (index > 0) {
			const key = e.substring(0, index).trim();
			const value = e.substring(index + 1);
			if (key && value) {
				result[key] = value;
			}
		}
	});
	return result;
}

function parseStyleFromObject(o: Record<string, any>): Record<string, string> {
	const result: Record<string, string> = {};
	Object.keys(o).forEach((key) => {
		const value = '' + o[key];
		result[transformStyleName(key)] = value;
	});
	return result;
}

const RE_TRANSFORM_STYLE_NAME = /([A-Z])/g;

function transformStyleName(s: string) {
	return s.replace(RE_TRANSFORM_STYLE_NAME, '-$1').toLowerCase();
}

function formatStyle(o: Record<string, string>) {
	const keys = Object.keys(o);
	if (keys.length > 0) {
		return keys.map((key) => `${key}: ${o[key]}`).join(';');
	}
}

export function parseStyle(style: DIWStyleType | undefined | null): string | undefined {
	if (style !== undefined && style !== null) {
		if (Array.isArray(style)) {
			return formatStyle(parseStyleFromArray(style));
		} else if (typeof style === 'string') {
			return formatStyle(parseStyleFromString(style));
		} else {
			return formatStyle(parseStyleFromObject(style));
		}
	}
}
