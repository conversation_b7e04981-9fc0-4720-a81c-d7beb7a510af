@use './../common/abstracts/variable' as *;
@use './../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(text) {
		@include when(default) {
			color: $wot-dark-color;
		}
	}
}

@include b(text) {
	@include when(bold) {
		font-weight: bold;
	}

	@for $i from 1 through 5 {
		&.is-lines-#{$i} {
			@include multiEllipsis($i);
		}
	}

	@include when(default) {
		color: $wot-text-info-color;
	}

	@include when(primary) {
		color: $wot-text-primary-color;
	}

	@include when(error) {
		color: $wot-text-error-color;
	}

	@include when(warning) {
		color: $wot-text-warning-color;
	}

	@include when(success) {
		color: $wot-text-success-color;
	}
}
