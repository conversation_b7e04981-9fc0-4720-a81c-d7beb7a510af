<template>
	<DIWAppPage :title="t('%productionHistoryTitle%')">
		<view class="production-history">
			<!-- 顶部Tab -->
			<view class="tab-container">
				<view class="tab-buttons">
					<view v-for="(tab, index) in tabs" :key="index" class="tab-button" :class="{ active: activeTab === index }" @click="switchTab(index)">
						<text class="tab-text">{{ tab }}</text>
					</view>
				</view>
			</view>

			<!-- 月份选择器 -->
			<view class="month-selector">
				<view class="month-picker" @click="showMonthPicker">
					<wd-icon name="calendar" size="32rpx" color="#1C64FD" />
					<text class="month-text">{{ selectedMonth }}</text>
					<wd-icon name="arrow-down" size="24rpx" color="#1C64FD" />
				</view>
			</view>

			<!-- 统计概览 -->
			<view class="stats-overview">
				<view class="stat-card">
					<text class="stat-value">{{ monthlyStats.totalOutput }}</text>
					<text class="stat-label">{{ t('%monthlyTotalOutput%') }}</text>
				</view>
				<view class="stat-card">
					<text class="stat-value rank">{{ monthlyStats.rank }}</text>
					<text class="stat-label">{{ t('%monthlyRank%') }}</text>
				</view>
				<view class="stat-card">
					<text class="stat-value">{{ monthlyStats.averagePlatforms }}</text>
					<text class="stat-label">{{ t('%monthlyAverageStands%') }}</text>
				</view>
			</view>

			<!-- 历史记录列表 -->
			<DIWScrollView>
				<DIWListView ref="listView" :meta="listViewMeta">
					<template #default="{ data }">
						<view class="history-record" :class="{ highlighted: data.isToday }">
							<view class="record-header">
								<view class="date-info">
									<text class="date">{{ data.date }}</text>
									<view class="shift-badge" :class="data.shiftType">
										{{ data.shiftName }}
									</view>
								</view>
							</view>

							<view class="record-content">
								<view class="summary-info">
									<view class="summary-item">
										<text class="summary-label">{{ t('%shiftOutput%') }}</text>
										<text class="summary-value">{{ data.shiftOutput }}{{ t('%unit_kg%') }}</text>
									</view>
									<view class="summary-item">
										<text class="summary-label">{{ t('%platformCount%') }}</text>
										<text class="summary-value">{{ data.platformCount }}{{ t('%unit_platform%') }}</text>
									</view>
								</view>

								<!-- 细纱机详情 -->
								<view class="machine-details">
									<text class="details-title">{{ t('%spindleMachines%') }}</text>
									<view class="machine-list">
										<view v-for="(machine, index) in data.machines" :key="index" class="machine-item">
											<text class="machine-id">{{ machine.id }}</text>
											<text class="machine-output">{{ machine.output }}{{ t('%unit_kg%') }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</DIWListView>
			</DIWScrollView>

			<!-- 月份选择弹窗 -->
			<wd-popup v-model="showPicker" position="bottom" :lock-scroll="true">
				<wd-datetime-picker v-model="pickerValue" type="year-month" @confirm="confirmMonth" @cancel="showPicker = false" />
			</wd-popup>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { protect } = useFramework();

// 使用国际化
const { t } = useI18n();

// Tab选项
const tabs = computed(() => [t('%fineSpinning%'), t('%bobbin%')]);
const activeTab = ref(0);

// 月份选择
const selectedMonth = ref(`2025${t('%year%')}7${t('%month%')}`);
const showPicker = ref(false);
const pickerValue = ref('2025-07');

// 统计数据
const monthlyStats = ref({
	totalOutput: '8869.48',
	rank: '3',
	averagePlatforms: '5.5',
});

// 列表视图
const listView = ref();

// 列表元数据
const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(pageIndex) {
			const size1 = 20;
			const size2 = 5;
			const isReload = pageIndex === undefined;
			const current = isReload ? 1 : pageIndex;
			const size = isReload ? size1 : size2;

			// 模拟数据
			const mockData = generateMockData(current, size);

			return {
				hasMore: current * size < 100, // 假设总共100条记录
				items: mockData,
				trackInfo: isReload ? 1 + size1 / size2 : current + 1,
			};
		},
	});
});

// 切换Tab
function switchTab(index: number) {
	activeTab.value = index;
	// 重新加载数据
	if (listView.value) {
		listView.value.reload();
	}
}

// 显示月份选择器
function showMonthPicker() {
	showPicker.value = true;
}

// 确认月份选择
function confirmMonth(value: string) {
	const [year, month] = value.split('-');
	selectedMonth.value = `${year}${t('%year%')}${parseInt(month)}${t('%month%')}`;
	showPicker.value = false;

	// 重新加载数据
	if (listView.value) {
		listView.value.reload();
	}
}

// 生成模拟数据
function generateMockData(page: number, size: number) {
	const data = [];
	const startIndex = (page - 1) * size;

	for (let i = 0; i < size; i++) {
		const dayIndex = startIndex + i + 1;
		const isEveningShift = dayIndex % 2 === 0;

		data.push({
			id: `record_${dayIndex}`,
			date: `2025-07-${String(20 - Math.floor(dayIndex / 2)).padStart(2, '0')}`,
			isToday: dayIndex === 1,
			shiftType: isEveningShift ? 'evening' : 'morning',
			shiftName: isEveningShift ? t('%eveningShift%') : t('%morningShift%'),
			shiftOutput: '155.26',
			platformCount: '5',
			machines: [
				{ id: '001', output: '24.6' },
				{ id: '002', output: '20.2' },
				{ id: '003', output: '18.64' },
				{ id: '004', output: '24.9' },
				{ id: '005', output: '30.8' },
			],
		});
	}

	return data;
}

// 页面加载时获取统计数据
onMounted(() => {
	loadMonthlyStats();
});

// 加载月度统计数据
async function loadMonthlyStats() {
	protect(async () => {
		try {
			// 这里应该调用实际的API
			// const res = await apiGet({
			//   url: 'production/monthly-stats',
			//   params: {
			//     month: selectedMonth.value,
			//     type: activeTab.value
			//   }
			// });
			// monthlyStats.value = res;
		} catch (error) {
			console.error('加载月度统计失败', error);
		}
	});
}
</script>

<style lang="scss" scoped>
.production-history {
	background-color: #f5f7fa;
	min-height: 100vh;
}

// Tab容器
.tab-container {
	background-color: #ffffff;
	padding: 0 32rpx;
	border-bottom: 2rpx solid #e5e6eb;
}

.tab-buttons {
	display: flex;
	height: 88rpx;
}

.tab-button {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;

	&.active {
		.tab-text {
			color: #1c64fd;
			font-weight: 600;
		}

		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 60rpx;
			height: 6rpx;
			background-color: #1c64fd;
			border-radius: 3rpx;
		}
	}
}

.tab-text {
	font-size: 30rpx;
	color: #86909c;
	transition: all 0.3s ease;
}

// 月份选择器
.month-selector {
	background-color: #ffffff;
	padding: 24rpx 32rpx;
	border-bottom: 2rpx solid #e5e6eb;
}

.month-picker {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16rpx 24rpx;
	background-color: #f8f9fc;
	border-radius: 12rpx;
	border: 2rpx solid #e5e8f2;
}

.month-text {
	font-size: 28rpx;
	color: #1c64fd;
	font-weight: 500;
	margin: 0 16rpx;
}

// 统计概览
.stats-overview {
	display: flex;
	background-color: #ffffff;
	padding: 32rpx;
	margin-bottom: 24rpx;
	border-radius: 0 0 24rpx 24rpx;
}

.stat-card {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;

	&:not(:last-child) {
		border-right: 2rpx solid #e5e6eb;
	}
}

.stat-value {
	font-size: 48rpx;
	font-weight: 600;
	color: #1d2129;
	margin-bottom: 8rpx;

	&.rank {
		color: #f56c6c;
	}
}

.stat-label {
	font-size: 24rpx;
	color: #86909c;
}

// 历史记录
.history-record {
	background-color: #ffffff;
	margin: 0 32rpx 24rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);

	&.highlighted {
		border: 4rpx solid #1c64fd;
		box-shadow: 0 8rpx 24rpx rgba(28, 100, 253, 0.15);
	}
}

.record-header {
	padding: 24rpx 32rpx;
	border-bottom: 2rpx solid #e5e6eb;
}

.date-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.date {
	font-size: 32rpx;
	font-weight: 600;
	color: #1d2129;
}

.shift-badge {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;

	&.evening {
		background-color: #fff8e6;
		color: #ff7d00;
	}

	&.morning {
		background-color: #e8ffea;
		color: #00b42a;
	}
}

.record-content {
	padding: 24rpx 32rpx;
}

.summary-info {
	display: flex;
	margin-bottom: 24rpx;
}

.summary-item {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;

	&:not(:last-child) {
		border-right: 2rpx solid #e5e6eb;
		padding-right: 32rpx;
		margin-right: 32rpx;
	}
}

.summary-label {
	font-size: 26rpx;
	color: #86909c;
}

.summary-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #1d2129;
}

.machine-details {
	background-color: #f8f9fc;
	padding: 24rpx;
	border-radius: 16rpx;
}

.details-title {
	font-size: 26rpx;
	color: #86909c;
	margin-bottom: 16rpx;
	display: block;
}

.machine-list {
	display: flex;
	flex-wrap: wrap;
	gap: 24rpx 32rpx;
}

.machine-item {
	display: flex;
	align-items: center;
	min-width: 160rpx;
}

.machine-id {
	font-size: 26rpx;
	color: #1d2129;
	margin-right: 16rpx;
	font-weight: 500;
}

.machine-output {
	font-size: 26rpx;
	color: #86909c;
}
</style>
