<template>
	<view class="login-page-container">
		<!-- 背景图片 -->
		<image src="@/static/icons/login/bg3.png" class="background-image" mode="widthFix"></image>

		<!-- 顶部logo区域 -->
		<view class="logo-section">
			<view class="logo-title"><image src="@/static/icons/login/logo.png" class="logo-image" mode="aspectFit"></image></view>
			<view class="logo-subtitle">数智云</view>
		</view>

		<!-- 登录卡片 -->
		<view class="login-card">
			<!-- 卡片标题 -->
			<view class="card-header">
				<view class="card-header-item" @click="switchTab('1')">
					<text :class="type == '1' ? 'card-title' : 'card-title card-title-primary'">中文登录</text>
					<view v-if="type == '1'" class="title-underline"></view>
				</view>
				<view class="card-header-item" @click="switchTab('2')">
					<text :class="type == '2' ? 'card-title-l' : 'card-title-l card-title-primary'">خەنزۇ تىلى</text>
					<view v-if="type == '2'" class="title-underline"></view>
				</view>
			</view>

			<!-- 登录表单 -->
			<view class="login-form">
				<!-- 短信登录表单 -->
				<wd-form v-if="loginType === 'mobile'" ref="mobileFormRef" :model="mobileModel">
					<!-- 手机号输入框 -->
					<view class="input-wrapper">
						<image src="@/static/icons/login/userr.png" class="input-icon" mode="aspectFit"></image>
						<wd-input
							v-model="mobileModel.mobile"
							:placeholder="t('mobile_number_placeholder')"
							:disabled="inProgress"
							:maxlength="11"
							type="number"
							class="form-input"
							no-border
						/>
					</view>

					<!-- 验证码输入框 -->
					<view class="verification-input-wrapper mt-[40rpx]">
						<view class="input-wrapper">
							<image src="@/static/icons/login/password.png" class="input-icon" mode="aspectFit"></image>
							<wd-input
								v-model="mobileModel.code"
								:placeholder="t('verification_code_placeholder')"
								:disabled="inProgress"
								class="form-input verification-input"
								type="number"
								no-border
							/>
						</view>
						<view class="verification-code-button" @click="getVerificationCode">
							<text class="code-button-text" v-if="countDown <= 0">{{ t('get_verification_code') }}</text>
							<text class="code-button-text" v-else>{{ countDown }}s</text>
						</view>
					</view>
				</wd-form>

				<!-- 密码登录表单 -->
				<wd-form v-else ref="loginFormRef" :model="loginModel">
					<!-- 用户名输入框 -->
					<view class="input-wrapper">
						<wd-input
							v-model="loginModel.username"
							:placeholder="t('username_placeholder')"
							:disabled="inProgress"
							:readonly="!!lockedUsername"
							class="form-input"
							no-border
							clearable
						>
							<template #prefix><image src="@/static/icons/login/userr.png" class="input-icon" mode="aspectFit"></image></template>
						</wd-input>
					</view>

					<!-- 密码输入框 -->
					<view class="input-wrapper mt-[40rpx]">
						<wd-input
							v-model="loginModel.password"
							:placeholder="t('password_placeholder')"
							:disabled="inProgress"
							class="form-input"
							show-password
							no-border
							clearable
						>
							<template #prefix><image src="@/static/icons/login/password.png" class="input-icon" mode="aspectFit"></image></template>
						</wd-input>
					</view>
					<!-- 验证码输入框（密码登录时显示） 暂时不需要-->
					<view class="verification-input-wrapper mt-[40rpx]">
						<view class="input-wrapper">
							<wd-input
								v-model="captchaCode"
								:placeholder="t('verification_code_input_placeholder')"
								:disabled="inProgress"
								class="form-input verification-input"
								no-border
								clearable
							>
								<template #prefix><view class="input-icon"> </view></template>
							</wd-input>
						</view>
						<view class="verification-code-image"> 7084 </view>
					</view>
				</wd-form>
				<!-- 快速登录账号 -->
				<!-- <view v-if="!lockedUsername && savedAccounts.length > 0 && loginType === 'password'" class="mt-5">
					<view class="flex flex-row flex-wrap gap-2">
						<wd-tag v-for="account in savedAccounts" :key="account.username" round class="account-tag" @click="loginAccount(account)">
							{{ account.username }}
						</wd-tag>
					</view>
				</view> -->

				<!-- 忘记密码 -->
				<view class="forgot-password">
					<text class="forgot-password-text" @click="forgotPassword">{{ t('forgot_password') }}</text>
				</view>

				<!-- 登录按钮 -->
				<wd-button :loading="inProgress" @click="loginType === 'mobile' ? doMobileLogin() : doLogin()" custom-class="login-button">
					{{ t('login') }}
				</wd-button>
			</view>
		</view>
		<!-- 协议同意 -->
		<view class="agreement-section">
			<wd-checkbox size="24rpx" v-model="isAgreed" checked-color="#0082f0"> </wd-checkbox>
			<text class="agreement-normal">{{ t('agreement_read_and_agree') }}</text>
			<text class="agreement-link" @click="showUserAgreement">{{ t('user_agreement') }}</text>
			<text class="agreement-normal"> {{ t('and') }} </text>
			<text class="agreement-link" @click="showPrivacyPolicy">{{ t('privacy_policy') }}</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();

const { protect, login, loginMobile, apiRequest } = useFramework();
interface Account {
	username: string;
	password: string;
}
const lockedUsername = ref<string | null>(null);
const savedAccounts = ref<Account[]>(
	(() => {
		if (__CONFIG_IS_DEV_MODE__) {
			return [
				{ username: '***********', password: 'a111111' },
				{ username: '***********', password: 'a111111' },
			];
		}

		return [];
	})()
);

const type = ref('1');
const loginType = ref('password'); // 'mobile' 或 'password'
const countDown = ref(0);
const timer = ref<any>(null);
const isAgreed = ref(false);
const mobileFormRef = ref();
const loginFormRef = ref();
const isPhoneRegistered = ref(false); // 手机号是否已注册
const captchaCode = ref(''); // 验证码输入

// 用户名密码登录
const loginModel = ref({
	username: '',
	password: '',
});

// 手机验证码登录
const mobileModel = ref({
	mobile: '',
	code: '',
});

const isMobileValid = computed(() => {
	return /^1[3-9]\d{9}$/.test(mobileModel.value.mobile);
});

function switchTab(tab: string) {
	type.value = tab;
}

function loginAccount(account: Account) {
	loginModel.value.username = account.username;
	loginModel.value.password = account.password;
	isAgreed.value = true;
	doLogin();
}

// 验证手机号是否已注册
const checkMobileRegistered = async () => {
	if (!isMobileValid.value) return false;

	try {
		const res = await apiRequest({
			url: 'admin/register/exist',
			method: 'GET',
			parse: 0,
			params: { mobile: mobileModel.value.mobile },
		});

		if (res.data) {
			isPhoneRegistered.value = true;
			return true;
		} else {
			uni.showToast({
				title: `${mobileModel.value.mobile} ${t('mobile_not_registered')}`,
				icon: 'none',
			});
			isPhoneRegistered.value = false;
			return false;
		}
	} catch (error) {
		isPhoneRegistered.value = false;
		return false;
	}
};

const inProgress = ref(false);

// 获取验证码
function getVerificationCode() {
	if (!isMobileValid.value || countDown.value > 0) return;

	// 先验证手机号是否已注册
	protect(async () => {
		const isRegistered = await checkMobileRegistered();
		if (!isRegistered) return;

		// 开始倒计时
		countDown.value = 60;
		timer.value = setInterval(() => {
			if (countDown.value > 0) {
				countDown.value--;
			} else {
				clearInterval(timer.value);
				timer.value = null;
			}
		}, 1000);

		// 调用发送验证码的接口
		try {
			const data = await apiRequest({
				url: 'msg/sysMessage/send/sendSmsCodeNoToken',
				method: 'GET',
				parse: 0,
				params: { mobile: mobileModel.value.mobile, type: 'SMS' },
			});

			if (data.code === 0 && data.ok) {
				uni.showToast({
					title: t('verification_code_sent'),
					icon: 'none',
				});
			} else {
				clearInterval(timer.value);
				countDown.value = 0;
				uni.showToast({
					title: data.msg || t('verification_code_send_failed'),
					icon: 'none',
				});
			}
		} catch (error) {
			clearInterval(timer.value);
			countDown.value = 0;
			uni.showToast({
				title: t('verification_code_send_failed'),
				icon: 'none',
			});
		}
	});
}

// 手机号登录
function doMobileLogin() {
	// 检查是否同意协议
	if (!isAgreed.value) {
		uni.showToast({
			title: t('please_read_and_agree_agreement'),
			icon: 'none',
		});
		return;
	}

	if (!isMobileValid.value) {
		uni.showToast({
			title: t('please_enter_correct_mobile'),
			icon: 'none',
		});
		return;
	}

	if (!mobileModel.value.code) {
		uni.showToast({
			title: t('please_enter_verification_code'),
			icon: 'none',
		});
		return;
	}

	protect(async () => {
		try {
			inProgress.value = true;

			// 调用手机验证码登录接口
			await loginMobile(mobileModel.value.mobile, mobileModel.value.code);

			uni.reLaunch({ url: '/mod/main/home' });
		} finally {
			inProgress.value = false;
		}
	});
}
// 用户名密码登录
function doLogin() {
	// 检查是否同意协议;
	if (!isAgreed.value) {
		uni.showToast({
			title: t('please_read_and_agree_agreement'),
			icon: 'none',
		});
		return;
	}

	if (!loginModel.value.username) {
		uni.showToast({
			title: t('please_enter_username'),
			icon: 'none',
		});
		return;
	}

	if (!loginModel.value.password) {
		uni.showToast({
			title: t('please_enter_password'),
			icon: 'none',
		});
		return;
	}

	protect(async () => {
		try {
			inProgress.value = true;
			await login(loginModel.value.username, loginModel.value.password);
			uni.reLaunch({ url: '/mod/main/home' });
		} catch (err) {
			console.log(err);
		} finally {
			inProgress.value = false;
		}
	});
}

// 忘记密码
function forgotPassword() {
	uni.showModal({
		title: t('forgot_password'),
		content: t('forgot_password_tip'),
		showCancel: false,
		confirmText: t('confirm'),
	});
}

// 显示用户协议
function showUserAgreement() {
	// TODO: 跳转到用户协议页面
	console.log('显示用户协议');
}

// 显示隐私政策
function showPrivacyPolicy() {
	// TODO: 跳转到隐私政策页面
	console.log('显示隐私政策');
}

// 组件卸载时清除定时器
onUnmounted(() => {
	if (timer.value) {
		clearInterval(timer.value);
		timer.value = null;
	}
});
</script>

<style lang="scss" scoped>
.login-page-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
}

.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

.logo-section {
	font-weight: bold;
	font-size: 46rpx;
	color: #1d2129;
	line-height: 75rpx;
	position: relative;
	z-index: 1;
	margin: 132rpx auto 0;
	align-self: flex-start;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.logo-title {
		width: 136rpx;
		height: 136rpx;
		// background: rgba(0, 130, 240, 0.2);
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		.logo-image {
			width: 100%;
			height: 100%;
		}
	}
	.logo-subtitle {
		margin-top: 16rpx;
		font-weight: bold;
		font-size: 32rpx;
		color: #0082f0;
		line-height: 32rpx;
	}
}

.login-card {
	width: 690rpx;
	margin: 95rpx auto auto;
	position: relative;
	z-index: 2;
	border-radius: 36rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	flex: 1;
	max-width: 90vw;
}

.card-header {
	width: 384rpx;
	display: flex;
	align-items: start;
	justify-content: space-between;
}

.card-header-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content:start;
}

.card-title {
	display: flex;
	flex-wrap: nowrap;
	font-weight: bold;
	font-size: 39rpx;
	color: #0082f0;
	line-height: 39rpx;
}

.card-title-l {
	display: flex;
	flex-wrap: nowrap;
	font-weight: bold;
	font-size: 33rpx;
	color: #0082f0;
	line-height: 39rpx;
}

.card-title-primary {
	font-weight: normal;
	color: #4b5563;
}

.title-underline {
	margin-top: 18rpx;
	width: 59rpx;
	height: 6rpx;
	background: #0082f0;
	border-radius: 3rpx;
}

.login-form {
	width: 100%;
	margin-top: 63rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.input-wrapper {
	width: 617rpx;
	height: 92rpx;
	background: #EEF0F2;
	border-radius: 46rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	.input-icon {
		width: 31rpx;
		height: 37rpx;
		margin-right: 23rpx;
	}
}

.form-input {
	margin: 0 34rpx 0 37rpx;
	flex: 1;
	background: transparent;
	:deep(.wd-input__prefix) {
		display: flex;
	}

	:deep(.wd-input__inner) {
		border: none;
		background: transparent !important;
		padding: 0;
		font-size: 30rpx;
		color: #333333;
		height: 100rpx;
		line-height: 100rpx;

		&::placeholder {
			color: #999999;
		}
	}

	:deep(.wd-input__suffix) {
		background: transparent;
	}
	:deep(.wd-input__suffix) {
		display: flex;
		flex-wrap: nowrap;
	}
	:deep(.wd-input__clear),
	:deep(.wd-icon-eye-close),
	:deep(.wd-icon-view) {
		background: #f4f8fb;
		border-radius: 50%;
		color: #c5e1f9;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	:deep(.wd-input__icon) {
		background: transparent;
	}
}

.verification-input-wrapper {
	width: 617rpx;
	height: 100rpx;
	background: transparent;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 17rpx;
	.input-wrapper {
		flex: 1;
	}
}

.verification-code-image {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 219rpx;
	height: 101rpx;
	background: #ffffff;
	border-radius: 50rpx;
	border: 1rpx solid #f4f8fb;
}

.verification-code-button {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 219rpx;
	height: 101rpx;
	background: linear-gradient(90deg, #047ee5, #4dc9fd);
	border-radius: 50rpx;
	cursor: pointer;
	&:active {
		opacity: 0.8;
	}
}

.code-button-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #ffffff;
	letter-spacing: 2rpx;
}

.code-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #ffffff;
	text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
	letter-spacing: 4rpx;
}

.forgot-password {
	width: 617rpx;
	display: flex;
	justify-content: flex-start;
	// margin-top: 25.6rpx;
	margin-top: 15rpx;
}

.forgot-password-text {
	font-size: 28rpx;
	color: #2196f3;
}

.login-button {
	width: 617rpx !important;
	height: 100rpx !important;
	background: linear-gradient(90deg, #047ee5, #4dc9fd) !important;
	box-shadow: 0rpx 15rpx 10rpx 0rpx rgba(12,124,219,0.15) !important;
	border-radius: 50rpx !important;
	font-weight: bold !important;
	font-size: 38rpx !important;
	// margin-top: 73rpx;
	margin-top: 68rpx;
	color: #ffffff !important;
}

.agreement-section {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	z-index: 1;
	margin: 20rpx auto max(20rpx, 3vh);
	padding: 0 30rpx;
}
:deep(.wd-checkbox__shape) {
	border: 2rpx solid #0082f0 !important;
}

.agreement-normal {
	font-weight: 400;
	font-size: 24rpx;
	color: #1a1a1a;
	line-height: 44rpx;
}

.agreement-link {
	font-size: 24rpx;
	color: #0082f0;
	font-weight: 400;
	line-height: 44rpx;
}

/* 针对小屏幕设备优化 */
@media screen and (max-height: 667px) {
	.logo-section {
		margin-top: max(120rpx, 8vh);
		font-size: 40rpx;
		line-height: 65rpx;
	}

	.login-card {
		min-height: 55vh;
		margin-top: 200rpx;
		margin-bottom: 20rpx;
		width: 650rpx;
		background-size: 650rpx 770rpx;
	}

	.card-header {
		margin-top: 30rpx;
	}

	.login-form {
		margin-top: 35rpx;
	}

	.input-wrapper {
		height: 90rpx;
		width: 580rpx;
	}

	.verification-input-wrapper {
		width: 580rpx;
		height: 90rpx;
	}
	.forgot-password {
		width: 580rpx;
	}

	.form-input :deep(.wd-input__inner) {
		height: 90rpx;
		line-height: 90rpx;
		font-size: 30rpx;
	}

	.login-button {
		width: 580rpx !important;
		height: 90rpx !important;
		margin-top: 35rpx;
		font-size: 36rpx !important;
	}

	.agreement-section {
		margin: 15rpx auto 20rpx;
	}

	.verification-code-image,
	.verification-code-button {
		width: 200rpx;
		height: 90rpx;
	}
}
</style>
