import CryptoJS from 'crypto-js';
import { appconfig } from '@/config/appconfig';

function encryption(src: string, keyWord: string) {
	const key = CryptoJS.enc.Utf8.parse(keyWord);
	var encrypted = CryptoJS.AES.encrypt(src, key, {
		iv: key,
		mode: CryptoJS.mode.CFB,
		padding: CryptoJS.pad.NoPadding,
	});
	return encrypted.toString();
}

export function encryptPassword(password: string) {
	return encryption(password, appconfig.passwordEncryptionKey);
}
