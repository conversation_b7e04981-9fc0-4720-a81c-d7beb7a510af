<!-- 个人中心 -->
<template>
	<DIWAppPage>
		<view class="me-page">
			<view class="header-section">
				<!-- 公司名称 -->
				<view class="company-header">
					<text class="company-name">{{ t('shuZhiShiJieJiTuan') }}</text>
					<wd-icon name="arrow-down" size="24rpx" color="white" />
				</view>

				<!-- 用户信息卡片 -->
				<view class="user-card">
					<view class="avatar-container">
						<image class="avatar" :src="userInfo?.oaAvatar || '/static/icons/default-avatar.png'" mode="aspectFill" />
					</view>
					<view class="user-info">
						<text class="username">{{ userInfo?.name }}</text>
						<view class="user-details">
							<view class="level-badge">{{ userInfo?.level || 'L1级' }}</view>
							<view class="work-duration">
								<wd-icon name="clock-o" size="24rpx" color="rgba(255,255,255,0.8)" />
								<text class="duration-text">{{ t('ruZhiShiChang') }}: 1{{ t('nianGeYue') }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 功能列表区域 -->
			<view class="content-section">
				<!-- 语言切换 -->
				<view class="menu-item" @click="showLanguageSwitch = true">
					<view class="menu-left">
						<view class="menu-icon translate-icon">
							<text class="icon-text">A</text>
						</view>
						<text class="menu-title">{{ t('yuYanQieHuan') }}</text>
					</view>
					<view class="menu-right">
						<view class="language-switch">
							<text class="language-option" :class="{ active: currentLang === 'zh-CN' }" @click.stop="switchLanguage('zh-CN')">{{ t('hanYu') }}</text>
							<text class="language-option" :class="{ active: currentLang === 'ug-CN' }" @click.stop="switchLanguage('ug-CN')">{{ t('weiYu') }}</text>
						</view>
					</view>
				</view>

				<!-- 意见反馈 -->
				<view class="menu-item" @click="handleFeedback">
					<view class="menu-left">
						<view class="menu-icon feedback-icon">
							<wd-icon name="chat-o" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('yiJianFanKui') }}</text>
					</view>
					<view class="menu-right">
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>

				<!-- 关于我们 -->
				<view class="menu-item" @click="handleAboutUs">
					<view class="menu-left">
						<view class="menu-icon about-icon">
							<wd-icon name="info-o" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('guanYuWoMen') }}</text>
					</view>
					<view class="menu-right">
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>

				<!-- 我的二维码 -->
				<view class="menu-item" @click="handleMyQRCode">
					<view class="menu-left">
						<view class="menu-icon qrcode-icon">
							<wd-icon name="qr-code" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('woDeErWeiMa') }}</text>
					</view>
					<view class="menu-right">
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>

				<!-- 更改密码 -->
				<view class="menu-item" @click="handleChangePassword">
					<view class="menu-left">
						<view class="menu-icon password-icon">
							<wd-icon name="lock" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('gengGaiMiMa') }}</text>
					</view>
					<view class="menu-right">
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>

				<!-- 设置 -->
				<view class="menu-item" @click="handleSettings">
					<view class="menu-left">
						<view class="menu-icon settings-icon">
							<wd-icon name="setting" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('sheZhi') }}</text>
					</view>
					<view class="menu-right">
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>

				<!-- 注销账号 -->
				<view class="menu-item" @click="handleDeleteAccount">
					<view class="menu-left">
						<view class="menu-icon delete-icon">
							<wd-icon name="warning" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('zhuXiaoZhangHao') }}</text>
					</view>
					<view class="menu-right">
						<text class="menu-subtitle">{{ t('qingJinShenCaoZuo') }}</text>
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>

				<!-- 版本升级 -->
				<view class="menu-item" @click="handleVersionUpgrade">
					<view class="menu-left">
						<view class="menu-icon version-icon">
							<wd-icon name="refresh" size="32rpx" color="#4A90E2" />
						</view>
						<text class="menu-title">{{ t('banBenShengJi') }}</text>
					</view>
					<view class="menu-right">
						<text class="menu-subtitle">{{ t('dangQianBanBen') }}1.0</text>
						<wd-icon name="arrow-right" size="24rpx" color="#C8C8C8" />
					</view>
				</view>
			</view>

			<!-- 退出登录按钮 -->
			<view class="logout-section">
				<text class="logout-btn" @click="handleLogout">{{ t('tuiChuDengLu') }}</text>
			</view>

			<!-- 语言切换弹出层 -->
			<wd-action-sheet
				v-model="showLanguageSwitch"
				:actions="languageActions"
				:cancel-text="t('qu-xiao')"
				:title="t('yuYanQieHuan')"
				@select="handleLanguageSelect"
			/>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useSystemStore } from '@/store/system';
import { useI18n } from 'vue-i18n';
import { useI18nSync } from '@/hooks/useI18nSync';

const { sessionInfo, navigateTo } = useFramework();

const systemStore = useSystemStore();

const userInfo = computed(() => {
	return sessionInfo.value?.userInfo;
});

// 使用国际化
const { t } = useI18n();
const { setLocale, currentLang } = useI18nSync();

// 语言切换选项
const languageActions = computed(() => [
	{
		name: t('hanYu') + ' 🇨🇳',
		color: currentLang.value === 'zh-CN' ? '#0083ff' : '',
	},
	{
		name: t('weiYu') + ' 🇺',
		color: currentLang.value === 'ug-CN' ? '#0083ff' : '',
	},
]);

// 控制语言切换弹出层的显示
const showLanguageSwitch = ref(false);
const jumpFlag = ref(false);

// 处理语言选择
const handleLanguageSelect = ({ index }: { index: number }) => {
	const locale = index === 0 ? 'zh-CN' : 'ug-CN';
	switchLanguage(locale);
	showLanguageSwitch.value = false;
};

// 切换语言
const switchLanguage = (locale: string) => {
	setLocale(locale);
};

// 处理版本升级
const handleVersionUpgrade = () => {
	uni.showToast({
		title: '当前已是最新版本',
		icon: 'none',
	});
};

// 处理意见反馈
const handleFeedback = () => {
	// TODO: 导航到意见反馈页面
	uni.showToast({
		title: '功能开发中',
		icon: 'none',
	});
};

// 处理关于我们
const handleAboutUs = () => {
	// TODO: 导航到关于我们页面
	uni.showToast({
		title: '功能开发中',
		icon: 'none',
	});
};

// 处理我的二维码
const handleMyQRCode = () => {
	// TODO: 显示用户二维码
	uni.showToast({
		title: '功能开发中',
		icon: 'none',
	});
};

// 处理更改密码
const handleChangePassword = () => {
	// TODO: 导航到更改密码页面
	uni.showToast({
		title: '功能开发中',
		icon: 'none',
	});
};

// 处理设置
const handleSettings = () => {
	// TODO: 导航到设置页面
	uni.showToast({
		title: '功能开发中',
		icon: 'none',
	});
};

// 处理注销账号
const handleDeleteAccount = () => {
	uni.showModal({
		title: '确认注销',
		content: '注销账号后将无法恢复，确定要继续吗？',
		confirmText: '确认注销',
		cancelText: '取消',
		success: (res) => {
			if (res.confirm) {
				// TODO: 执行注销账号逻辑
				uni.showToast({
					title: '功能开发中',
					icon: 'none',
				});
			}
		},
	});
};

// 处理退出登录
const handleLogout = () => {
	uni.showModal({
		title: t('queRenTuiChu'),
		content: t('queRenTuiChuDengLu'),
		confirmText: t('tuiChu'),
		cancelText: t('qu-xiao'),
		success: async (res) => {
			if (res.confirm) {
				try {
					// 执行退出登录逻辑，清除token和session信息
					await systemStore.logout();

					uni.showToast({
						title: t('tuiChuChengGong'),
						icon: 'success',
					});

					// 跳转到登录页
					setTimeout(() => {
						navigateTo('/mod/main/login');
					}, 1000);
				} catch (error) {
					console.error('退出登录失败:', error);
					// 即使退出登录API失败，也要清除本地数据并跳转
					uni.showToast({
						title: t('tuiChuChengGong'),
						icon: 'success',
					});
					setTimeout(() => {
						navigateTo('/mod/main/login');
					}, 1000);
				}
			}
		},
	});
};

onShow(() => {
	if (!sessionInfo.value) {
		if (!jumpFlag.value) {
			jumpFlag.value = true;
			nextTick(() => {
				navigateTo('/mod/main/login');
			});
		} else {
			jumpFlag.value = false;
			navigateTo({ url: '/mod/mall/home', mode: 'switchTab' });
		}
	} else {
		jumpFlag.value = true;
	}
});

const visible = ref(false);
onShow(() => {
	visible.value = true;
});

onHide(() => {
	visible.value = false;
});

watchEffect(() => {
	if (visible.value) {
	}
});
</script>

<style lang="scss" scoped>
.me-page {
	background-image: url('/static/icons/<EMAIL>');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	position: relative;
}

.header-section {
	padding-top: 60rpx;
	padding-bottom: 60rpx;

	// 确保内容在覆盖层之上
	> * {
		position: relative;
		z-index: 2;
	}
}

.company-header {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 60rpx;

	.company-name {
		color: white;
		font-size: 36rpx;
		font-weight: 500;
		margin-right: 12rpx;
	}
}

.user-card {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 0 60rpx;
}

.avatar-container {
	width: 118rpx;
	height: 118rpx;
	border-radius: 50%;
	background: #d8d8d8;
	margin-right: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	display: flex;
	justify-content: center;
	align-items: center;
}

.avatar {
	width: 109rpx;
	height: 109rpx;
	border-radius: 54rpx;
	background: #f0f0f0;
}

.user-info {
	flex: 1;
}

.username {
	color: white;
	font-size: 36rpx;
	font-weight: 500;
	margin-bottom: 12rpx;
}

.user-details {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.level-badge {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	font-size: 24rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.work-duration {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.duration-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}

.content-section {
	margin: -30rpx 24rpx 0;
	background: white;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f8f8f8;
	}
}

.menu-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.menu-icon {
	width: 72rpx;
	height: 72rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;

	&.translate-icon {
		background: #0082f0;

		.icon-text {
			color: white;
			font-size: 28rpx;
			font-weight: bold;
		}
	}

	&.feedback-icon,
	&.about-icon,
	&.qrcode-icon,
	&.password-icon,
	&.settings-icon,
	&.delete-icon,
	&.version-icon {
		background: rgba(74, 144, 226, 0.1);
	}
}

.menu-title {
	color: #333;
	font-size: 32rpx;
	font-weight: 500;
}

.menu-right {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.menu-subtitle {
	color: #999;
	font-size: 28rpx;
}

.language-switch {
	display: flex;
	background: #f5f5f5;
	border-radius: 20rpx;
	padding: 4rpx;
	margin-right: 16rpx;
}

.language-option {
	padding: 8rpx 20rpx;
	font-size: 28rpx;
	color: #666;
	border-radius: 16rpx;
	transition: all 0.3s ease;

	&.active {
		background: #0082f0;
		color: white;
	}
}

.logout-section {
	padding: 60rpx 24rpx 120rpx;
	display: flex;
	justify-content: center;
}

.logout-btn {
	color: #ff4757;
	font-size: 32rpx;
	font-weight: 500;
	padding: 24rpx 0;
}
</style>
