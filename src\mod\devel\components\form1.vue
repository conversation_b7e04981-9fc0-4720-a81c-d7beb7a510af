<template>
	<wd-form ref="form" :model="model" :rules="rules">
		<DIWSection title="企业信息">
			<wd-cell-group border>
				<wd-cell vertical label="公司名称" prop="name">
					<wd-input v-model="model.name" />
				</wd-cell>
				<wd-cell vertical label="身份证号或统一社会信用代码" prop="sn">
					<wd-input v-model="model.sn" />
				</wd-cell>
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="法人信息">
			<wd-cell-group border>
				<wd-input label="法人名称" prop="legalName" v-model="model.legalName" />
				<wd-cell vertical label="证件类型" prop="idType">
					<wd-radio-group v-model="model.idType" inline>
						<wd-radio value="1">身份证</wd-radio>
						<wd-radio value="3">港澳回乡证</wd-radio>
						<wd-radio value="5">台胞证</wd-radio>
						<wd-radio value="19">外国护照</wd-radio>
					</wd-radio-group>
				</wd-cell>
				<wd-input label="法人证件号码" prop="legarIdCard" v-model="model.legalIdCard" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="法人信息">
			<wd-cell-group border>
				<wd-input label="法人名称" prop="legalName" v-model="model.legalName" />
				<wd-cell vertical label="证件类型" prop="idType">
					<wd-radio-group v-model="model.idType" inline>
						<wd-radio value="1">身份证</wd-radio>
						<wd-radio value="3">港澳回乡证</wd-radio>
						<wd-radio value="5">台胞证</wd-radio>
						<wd-radio value="19">外国护照</wd-radio>
					</wd-radio-group>
				</wd-cell>
				<wd-input label="法人证件号码" prop="legarIdCard" v-model="model.legalIdCard" />
			</wd-cell-group>
		</DIWSection>

		<DIWSection title="法人信息">
			<wd-cell-group border>
				<wd-input label="法人名称" prop="legalName" v-model="model.legalName" />
				<wd-cell vertical label="证件类型" prop="idType">
					<wd-radio-group v-model="model.idType" inline>
						<wd-radio value="1">身份证</wd-radio>
						<wd-radio value="3">港澳回乡证</wd-radio>
						<wd-radio value="5">台胞证</wd-radio>
						<wd-radio value="19">外国护照</wd-radio>
					</wd-radio-group>
				</wd-cell>
				<wd-input label="法人证件号码" prop="legarIdCard" v-model="model.legalIdCard" />
			</wd-cell-group>
		</DIWSection>

		<view class="mt-8 text-center">
			<wd-button @click="submit">提交</wd-button>
		</view>

		<wd-gap safe-area-bottom height="0" />
	</wd-form>
</template>

<script setup lang="ts">
const { protectOp, validateForm } = useFramework();

const form = ref();

const model = ref({
	name: '',
	sn: '',
	legalName: '',
	idType: '3',
	legalIdCard: '',
});

const rules = {
	name: [{ required: true, message: '请输入公司名称' }],
	sn: [{ required: true, message: '请输入身份证号或统一社会信用代码' }],
	legarIdCard: [{ required: true, message: '请输入法人证件号码' }],
};

function submit() {
	protectOp(async () => {
		await validateForm(form);
	});
}
</script>
