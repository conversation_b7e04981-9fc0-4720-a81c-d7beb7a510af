@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(pager) {
		background-color: $wot-dark-background;
		@include e(message) {
			color: $wot-dark-color3;
		}
	}
}

@include b(pager) {
	user-select: none;
	background-color: #fff;

	@include edeep(icon) {
		font-size: $wot-pagination-icon-size;
	}

	@include e(content) {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: $wot-pagination-content-padding;
	}
	@include e(message) {
		text-align: center;
		color: $wot-pagination-message-color;
		font-size: $wot-pagination-message-fs;
		padding: $wot-pagination-message-padding;
	}
	@include edeep(nav) {
		min-width: $wot-pagination-nav-width;
		@include m(active) {
			color: rgba(0, 0, 0, 0.65);
		}
		@include m(disabled) {
			color: rgba(0, 0, 0, 0.15);
		}
	}
	@include e(size) {
		flex: 1;
		text-align: center;
		font-size: $wot-pagination-nav-content-fs;
	}
	@include e(separator) {
		padding: $wot-pagination-nav-sepatator-padding;
	}
	@include edeep(left) {
		transform: rotate(180deg) translateY(1px);
		display: inline-block;
	}
	@include e(current) {
		color: $wot-pagination-nav-current-color;
	}
}
