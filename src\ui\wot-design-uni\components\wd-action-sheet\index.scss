@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;
.wot-theme-dark {
	@include b(action-sheet) {
		background-color: $wot-dark-background2;
		color: $wot-dark-color;

		@include e(action) {
			color: $wot-dark-color;
			background: $wot-dark-background2;

			&:not(.wd-action-sheet__action--disabled):not(.wd-action-sheet__action--loading):active {
				background: $wot-dark-background4;
			}

			@include m(disabled) {
				color: $wot-dark-color-gray;
			}
		}

		@include e(subname) {
			color: $wot-dark-color3;
		}

		@include e(cancel) {
			color: $wot-dark-color;
			background: $wot-dark-background4;

			&:active {
				background: $wot-dark-background5;
			}
		}

		:deep(.wd-action-sheet__close) {
			color: $wot-dark-color3;
		}

		@include e(panel-title) {
			color: $wot-dark-color;
		}

		@include e(header) {
			color: $wot-dark-color;
		}
	}
}

:deep(.wd-action-sheet__popup) {
	border-radius: $wot-action-sheet-radius $wot-action-sheet-radius 0 0;
}

@include b(action-sheet) {
	background-color: $wot-color-white;
	padding-bottom: 1px;

	@include edeep(popup) {
		border-radius: $wot-action-sheet-radius $wot-action-sheet-radius 0 0;
	}

	@include e(actions) {
		padding: 8px 0;
		max-height: 50vh;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
	}

	@include e(action) {
		position: relative;
		display: block;
		width: 100%;
		height: $wot-action-sheet-action-height;
		line-height: $wot-action-sheet-action-height;
		color: $wot-action-sheet-color;
		font-size: $wot-action-sheet-fs;
		text-align: center;
		border: none;
		background: $wot-action-sheet-bg;
		outline: none;

		&:after {
			display: none;
		}

		&:not(&--disabled):not(&--loading):active {
			background: $wot-action-sheet-active-color;
		}

		@include m(disabled) {
			color: $wot-action-sheet-disabled-color;
			cursor: not-allowed;
		}

		@include m(loading) {
			display: flex;
			align-items: center;
			justify-content: center;
			line-height: initial;
		}
	}

	@include edeep(action-loading) {
		width: $wot-action-sheet-loading-size;
		height: $wot-action-sheet-loading-size;
	}

	@include e(name) {
		display: inline-block;
	}

	@include e(subname) {
		display: inline-block;
		margin-left: 4px;
		font-size: $wot-action-sheet-subname-fs;
		color: $wot-action-sheet-subname-color;
	}

	@include e(cancel) {
		display: block;
		width: calc(100% - 48px);
		line-height: $wot-action-sheet-cancel-height;
		padding: 0;
		color: $wot-action-sheet-cancel-color;
		font-size: $wot-action-sheet-fs;
		text-align: center;
		border-radius: $wot-action-sheet-cancel-radius;
		border: none;
		background: $wot-action-sheet-cancel-bg;
		outline: none;
		margin: 0 auto 24px;
		font-weight: $wot-action-sheet-weight;

		&:active {
			background: $wot-action-sheet-active-color;
		}

		&:after {
			display: none;
		}
	}

	@include e(header) {
		color: $wot-action-sheet-color;
		position: relative;
		height: $wot-action-sheet-title-height;
		line-height: $wot-action-sheet-title-height;
		text-align: center;
		font-size: $wot-action-sheet-title-fs;
		font-weight: $wot-action-sheet-weight;
	}

	@include edeep(close) {
		position: absolute;
		top: $wot-action-sheet-close-top;
		right: $wot-action-sheet-close-right;
		color: $wot-action-sheet-close-color;
		font-size: $wot-action-sheet-close-fs;
		transform: rotate(-45deg);
		line-height: 1.1;
	}

	@include e(panels) {
		height: 84px;
		overflow-y: hidden;

		&:first-of-type {
			margin-top: 20px;
		}

		&:last-of-type {
			margin-bottom: 12px;
		}
	}

	@include e(panels-content) {
		display: flex;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
	}

	@include e(panel) {
		width: 88px;
		flex: 0 0 auto;
		display: inline-block;
		padding: $wot-action-sheet-panel-padding;
	}

	@include e(panel-img) {
		display: block;
		width: $wot-action-sheet-panel-img-fs;
		height: $wot-action-sheet-panel-img-fs;
		margin: 0 auto;
		margin-bottom: 7px;
		border-radius: $wot-action-sheet-panel-img-radius;
	}

	@include e(panel-title) {
		font-size: $wot-action-sheet-subname-fs;
		line-height: 1.2;
		text-align: center;
		color: $wot-action-sheet-color;
		@include lineEllipsis;
	}
}
