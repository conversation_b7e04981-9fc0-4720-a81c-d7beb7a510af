export function getFileExtensionLowerCase(fileName: string) {
	const sep = fileName.lastIndexOf('.');
	if (sep >= 0) {
		return fileName.substring(sep + 1).toLowerCase();
	}
}

export function detectFileTypeFromFileName(fileName: string): DIWFileType {
	const ext = getFileExtensionLowerCase(fileName);
	switch (ext) {
		case 'jpg':
		case 'jpeg':
		case 'png':
		case 'gif':
		case 'bmp':
			return 'image';

		case 'mp4':
		case 'mov':
		case 'avi':
			return 'video';
	}

	return 'file';
}
