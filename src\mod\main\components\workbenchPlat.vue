<!-- 工作台 - 平台 -->

<template>
	<view>
		<template v-if="workbenchStat">
			<InfoGroup title="客户认证">
				<InfoBlock
					class="w-half"
					title="待认证"
					:value="workbenchStat.customerStat?.pendingAuth"
					unit="单"
					icon="iconfont icon-dairenzheng"
					bg="bg-blue-50"
					fg="text-blue-600"
				/>
				<InfoBlock
					class="w-half"
					title="认证不通过"
					:value="workbenchStat.customerStat?.authFail"
					unit="单"
					icon="iconfont icon-renzhengbutongguo"
					bg="bg-red-50"
					fg="text-red-600"
				/>
				<InfoBlock
					class="w-half"
					title="资料未填写"
					:value="workbenchStat.customerStat?.noInfo"
					unit="单"
					icon="iconfont icon-weitianxie"
					bg="bg-orange-50"
					fg="text-orange-600"
				/>
				<InfoBlock
					class="w-half"
					title="待分配服务商"
					:value="workbenchStat.countWithoutServerBuyer"
					unit="单"
					icon="iconfont icon-jishufuwushang"
					bg="bg-purple-50"
					fg="text-purple-600"
				/>
			</InfoGroup>

			<InfoGroup title="商家认证">
				<InfoBlock
					class="w-half"
					title="待认证"
					:value="workbenchStat.sellerStat?.pendingAuth"
					unit="单"
					icon="iconfont icon-dairenzheng"
					bg="bg-blue-50"
					fg="text-blue-600"
				/>
				<InfoBlock
					class="w-half"
					title="认证不通过"
					:value="workbenchStat.sellerStat?.authFail"
					unit="单"
					icon="iconfont icon-renzhengbutongguo"
					bg="bg-red-50"
					fg="text-red-600"
				/>
				<InfoBlock
					class="w-half"
					title="资料未填写"
					:value="workbenchStat.sellerStat?.noInfo"
					unit="单"
					icon="iconfont icon-weitianxie"
					bg="bg-orange-50"
					fg="text-orange-600"
				/>
			</InfoGroup>

			<InfoGroup title="服务商认证">
				<InfoBlock
					class="w-half"
					title="待认证"
					:value="workbenchStat.serverStat?.pendingAuth"
					unit="单"
					icon="iconfont icon-dairenzheng"
					bg="bg-blue-50"
					fg="text-blue-600"
				/>
				<InfoBlock
					class="w-half"
					title="认证不通过"
					:value="workbenchStat.serverStat?.authFail"
					unit="单"
					icon="iconfont icon-renzhengbutongguo"
					bg="bg-red-50"
					fg="text-red-600"
				/>
				<InfoBlock
					class="w-half"
					title="资料未填写"
					:value="workbenchStat.serverStat?.noInfo"
					unit="单"
					icon="iconfont icon-weitianxie"
					bg="bg-orange-50"
					fg="text-orange-600"
				/>
			</InfoGroup>

			<InfoGroup title="意见反馈">
				<InfoBlock
					class="w-half"
					title="未解决"
					:value="workbenchStat.complaintStat?.nonResolve"
					unit="条"
					icon="iconfont icon-weijiejue"
					bg="bg-yellow-50"
					fg="text-yellow-600"
				/>
				<InfoBlock
					class="w-half"
					title="已解决"
					:value="workbenchStat.complaintStat?.resolved"
					unit="条"
					icon="iconfont icon-yijiejue"
					bg="bg-green-50"
					fg="text-green-600"
				/>
			</InfoGroup>
		</template>

		<!--
		<text v-if="workbenchStat" class="whitespace-pre-wrap break-all">{{ JSON.stringify(workbenchStat, null, 4) }}</text>
		-->
	</view>
</template>

<script setup lang="ts">
import InfoGroup from './InfoGroup.vue';
import InfoBlock from './InfoBlock.vue';
import { useStatStore } from '@/store/stat';
import { isDef } from './common';

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		styleIsolation: 'shared',
	},
});

// #endif

const { workbenchStat } = storeToRefs(useStatStore());
</script>
