interface DIWAppPageImpl {
	showError(err: any): void;
	showErrorMessage(message: string): void;
	showMessage(message: string): void;
	showLoading(): void;
	hideLoading(): void;
	confirm(msg: string): Promise<any>;
}

interface DIWAppPageRoot {
	registerAppPageImpl(impl: DIWAppPageImpl);
	showError(err: any): void;
	showErrorMessage(message: string): void;
	showMessage(message: string): void;
	showLoading(): void;
	hideLoading(): void;
	confirm(msg: string): Promise<any>;
}
