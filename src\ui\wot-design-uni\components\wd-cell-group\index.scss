@use '../common/abstracts/variable.scss' as *;
@use '../common/abstracts/_mixin.scss' as *;

.wot-theme-dark {
	@include b(cell-group) {
		background-color: $wot-dark-background2;

		@include when(border) {
			.wd-cell-group__title {
				@include halfPixelBorder('bottom', 0, $wot-dark-border-color);
			}
		}

		@include e(title) {
			background: $wot-dark-background2;
			color: $wot-dark-color;
		}

		@include e(right) {
			color: $wot-dark-color3;
		}

		@include e(body) {
			background: $wot-dark-background2;
		}
	}
}

@include b(cell-group) {
	background-color: $wot-color-white;

	@include when(border) {
		.wd-cell-group__title {
			@include halfPixelBorder;
		}
	}
	@include e(title) {
		position: relative;
		display: flex;
		justify-content: space-between;
		padding: $wot-cell-group-padding;
		background: $wot-color-white;
		font-size: $wot-cell-group-title-fs;
		color: $wot-cell-group-title-color;
		font-weight: $wot-fw-medium;
		line-height: 1.43;
	}
	@include e(right) {
		color: $wot-cell-group-value-color;
		font-size: $wot-cell-group-value-fs;
	}
	@include e(body) {
		background: $wot-color-white;
	}
}
