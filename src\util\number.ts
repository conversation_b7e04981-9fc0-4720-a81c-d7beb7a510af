import { format } from 'date-fns';

export function newDate(s: any): Date {
	return new Date(s);
}

export function formatDateTime(date: Date | number, fmt: string) {
	return format(date, fmt);
}

export function formatNumber(value: any, precision: number, prefix?: string) {
	if (typeof value === 'number') {
		const s = value.toFixed(precision);
		if (prefix) {
			return s + prefix;
		}

		return s;
	}

	return '';
}

interface CurrencyOptions {
	precision?: number;
	suffix?: string;
	prefix?: string;
}

function formatCurrencyImpl(value: number, suffixOrOptions?: string | CurrencyOptions, options?: CurrencyOptions) {
	let suffix: string | undefined;
	let precision: number | undefined;
	let prefix: string | undefined;

	// 处理新的调用方式
	if (typeof suffixOrOptions === 'object') {
		suffix = suffixOrOptions.suffix;
		precision = suffixOrOptions.precision;
		prefix = suffixOrOptions.prefix;
	} else {
		// 原有的调用方式
		suffix = suffixOrOptions;
		precision = options?.precision;
		prefix = options?.prefix;
	}

	let s1 = '';

	// #ifdef H5
	const formatter = new Intl.NumberFormat('zh-CN', {
		style: 'currency',
		currency: 'CNY',
		minimumFractionDigits: precision || 2, // 强制保留至少两位小数
		maximumFractionDigits: precision || 2, // 最多保留指定位数小数
	});
	s1 = formatter.format(value).substring(1);
	// #endif
	// #ifndef H5
	s1 = value.toFixed(precision || 2);
	let s2 = '';
	const len = s1.length +2 - (precision || 2)
	for (let i = 0, j = len % 3; i < s1.length; i = j, j += 3) {
		if (s2.length > 0 && j < len) {
			s2 += ',';
		}
		s2 += s1.substring(i, j);
	}
	s1 = s2;
	// #endif

	if (prefix !== undefined) {
		s1 = `${prefix}${s1}`;
	}

	if (suffix !== undefined) {
		s1 = `${s1}${suffix}`;
	}

	return s1;
}

// 函数重载签名
export function formatCurrency(value: any, suffix: string, options?: CurrencyOptions): string;
export function formatCurrency(value: any, options?: CurrencyOptions): string;
export function formatCurrency(value: any, suffixOrOptions?: string | CurrencyOptions, options?: CurrencyOptions): string {
	if (typeof value === 'string') {
		let value1 = parseNumber(value);
		if (value1 !== null) {
			return formatCurrencyImpl(value1, suffixOrOptions, options);
		} else {
			return value;
		}
	} else if (typeof value === 'number') {
		return formatCurrencyImpl(value, suffixOrOptions, options);
	}

	return '';
}

const RE_NUMBER1 = /^\d+(\.(\d+)?)?$/;
const RE_NUMBER2 = /^\.\d+$/;

export function parseNumber(s: any) {
	if (typeof s === 'string') {
		const s1 = s.trim();
		if (RE_NUMBER1.test(s1) || RE_NUMBER2.test(s1)) {
			const k = parseFloat(s1);
			if (!isNaN(k)) {
				return k;
			}
		}
	} else if (typeof s === 'number') {
		return s;
	}

	return null;
}
