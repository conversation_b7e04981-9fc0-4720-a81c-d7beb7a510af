@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(popover) {
		@include e(pos) {
			background: rgb(75, 76, 77);
			color: $wot-dark-color;
			box-shadow: 0px 2px 10px 0px rgba(75, 76, 77, 0.1);
		}

		@include e(menu) {
			background: rgb(75, 76, 77);
		}

		@include e(inner) {
			background-color: rgb(75, 76, 77);
		}

		@include e(menu-inner) {
			@include halfPixelBorder('top', 0, $wot-dark-border-color);
		}

		@include squareArrow($wot-popover-arrow-size, rgb(75, 76, 77), $wot-popover-z-index - 1, $wot-popover-arrow-box-shadow);
	}
}

@include b(popover) {
	position: relative;
	display: inline-block;

	@include edeep(icon) {
		vertical-align: middle;
		font-size: 18px;
		margin-right: 5px;
	}

	@include e(menu-inner) {
		position: relative;
		padding: $wot-popover-padding 0;
		display: flex;
		align-items: center;

		@include halfPixelBorder('top', 0, $wot-popover-border-color);

		&:first-child::after {
			display: none;
		}
	}

	@include e(menu) {
		display: inline-block;
		padding: 0 $wot-popover-padding;
		white-space: nowrap;
		z-index: $wot-popover-z-index;
		position: relative;
		background: $wot-popover-bg;
		border-radius: $wot-popover-radius;
	}

	@include edeep(pos) {
		position: absolute;
		box-sizing: border-box;
		background-clip: padding-box;
		text-align: center;
		min-height: 36px;
		z-index: $wot-popover-z-index;
		line-height: $wot-popover-line-height;
		font-size: $wot-popover-fs;
		border-radius: $wot-popover-radius;
		transition: opacity 0.2s;
		background: $wot-popover-bg;
		box-shadow: $wot-popover-box-shadow;
		color: $wot-popover-color;
	}

	// @include edeep(transition) {
	//   position: relative;
	//   z-index: $wot-popover-z-index;
	// }

	@include e(hidden) {
		left: -100vw;
		visibility: hidden;
	}

	@include e(container) {
		position: relative;
		line-height: $wot-tooltip-line-height;
		font-size: $wot-tooltip-fs;
	}

	@include e(inner) {
		position: relative;
		white-space: nowrap;
		padding: $wot-popover-padding;
		line-height: $wot-popover-line-height;
		z-index: $wot-popover-z-index;
		background-color: $wot-popover-bg;
		border-radius: $wot-popover-radius;
	}

	@include edeep(close-icon) {
		font-size: 12px;
		position: absolute;
		right: -8px;
		top: -10px;
		transform: scale(0.5);
		padding: 10px;
	}

	@include squareArrow($wot-popover-arrow-size, $wot-popover-bg, $wot-popover-z-index - 1, $wot-popover-arrow-box-shadow);
}
