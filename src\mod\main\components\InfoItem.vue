<template>
	<view class="info-item" @click="handleClick">
		<view v-if="props.title" class="info-item-title">
			<text>{{ props.title }}</text>
		</view>
		<view v-if="!props.custom" class="info-item-content">
			<view class="info-item-value">
				<text class="value-text">{{ displayText }}</text>
				<text v-if="props.unit" class="unit-text">{{ props.unit }}</text>
			</view>
			<template v-if="iconClass">
				<wd-badge :model-value="props.badgeValue" :max="99" :hidden="props.badgeValue === undefined">
					<view class="info-item-icon" :class="props.bg">
						<text class="icon-text" :class="iconClass" />
					</view>
				</wd-badge>
			</template>
		</view>
		<view v-else class="info-item-custom">
			<slot />
		</view>
	</view>
</template>

<script setup lang="ts">
import { isDef } from './common';

const props = withDefaults(
	defineProps<{ title?: string; value?: any; unit?: string; custom?: boolean; icon?: string; bg?: string; fg?: string; badgeValue?: number }>(),
	{
		custom: false,
	}
);

const emit = defineEmits<{ click: [] }>();

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		styleIsolation: 'shared',
	},
});

// #endif

const displayText = computed(() => {
	if (isDef(props.value)) {
		return props.value;
	}

	return '0';
});

const iconClass = computed(() => {
	const ar = [];
	if (props.icon) {
		ar.push(props.icon);
	}

	if (props.fg) {
		ar.push(props.fg);
	}

	if (ar.length > 0) {
		return ar.join(' ');
	}

	return null;
});

function handleClick() {
	emit('click');
}
</script>

<style scoped lang="scss">
.info-item {
	flex: 1;
	padding: 16rpx;

	.info-item-title {
		margin-bottom: 8rpx;

		text {
			font-size: 26rpx;
			color: #666666;
			line-height: 1.4;
		}
	}

	.info-item-content {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.info-item-value {
			display: flex;
			align-items: baseline;

			.value-text {
				font-size: 48rpx;
				font-weight: bold;
				color: #333333;
				line-height: 1.2;
			}

			.unit-text {
				font-size: 24rpx;
				color: #999999;
				margin-left: 4rpx;
			}
		}

		.info-item-icon {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;

			.icon-text {
				font-size: 48rpx;
			}
		}
	}

	.info-item-custom {
		// 自定义内容样式
	}
}
</style>
