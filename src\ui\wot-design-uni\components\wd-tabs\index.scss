@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(tabs) {
		background: $wot-dark-background2;

		@include e(nav) {
			background: $wot-dark-background2;
		}

		@include e(nav-item) {
			color: $wot-dark-color3;

			@include when(active) {
				font-weight: 600;
				color: $wot-dark-color;
			}

			@include when(disabled) {
				color: $wot-dark-color-gray;
			}
		}

		@include e(map-nav-btn) {
			background-color: $wot-dark-background4;
			color: $wot-dark-color3;

			@include when(active) {
				color: $wot-dark-color;
				border: 1px solid $wot-tabs-nav-active-color;
				background-color: $wot-dark-background;
			}

			@include when(disabled) {
				color: $wot-dark-color-gray;
				border-color: #f4f4f4;
			}
		}

		@include e(map-btn) {
			background: $wot-dark-background2;
			color: $wot-dark-color3;
		}

		@include e(map-header) {
			background: $wot-dark-background2;
			color: $wot-dark-color;

			&::after {
				background: $wot-dark-background4;
			}
		}

		@include e(map-body) {
			background: $wot-dark-background2;
		}
	}
}

@include b(tabs) {
	position: relative;
	background: #fff;
	width: 100%;

	@include e(nav) {
		left: 0;
		right: 0;
		height: $wot-tabs-nav-height;
		background-color: #fff;
		width: 100%;
		position: relative;

		@include m(wrap) {
			height: 100%;
			overflow: hidden;
		}

		@include m(sticky) {
			width: 100vw;
		}
	}

	@include e(nav-container) {
		position: relative;
		display: flex;
		user-select: none;
	}

	@include e(nav-item) {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		flex: 1;
		min-width: 0;
		height: $wot-tabs-nav-height;
		font-size: $wot-tabs-nav-fs;
		color: $wot-tabs-nav-color;
		transition: color 0.3s;

		@include when(active) {
			font-weight: 600;
		}

		@include when(disabled) {
			color: $wot-tabs-nav-disabled-color;
		}
	}

	@include e(nav-item-text) {
		@include lineEllipsis();
	}

	@include edeep(nav-item-badge) {
		display: flex;
		align-items: center;
		justify-content: center;
		max-width: 100%;
		min-width: 0;
	}

	@include e(line) {
		position: absolute;
		bottom: 4px;
		left: 0;
		z-index: 1;
		height: $wot-tabs-nav-line-height;
		width: $wot-tabs-nav-line-width;
		background: $wot-tabs-nav-line-bg-color;
		border-radius: calc($wot-tabs-nav-line-height / 2);

		@include m(inner) {
			left: 50%;
			transform: translateX(-50%);
		}
	}

	@include e(container) {
		overflow: hidden;
	}

	@include e(body) {
		position: relative;
		width: 100%;
		height: 100%;

		@include when(animated) {
			display: flex;
			transition-property: left;
		}
	}

	@include e(map) {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1;
	}

	@include e(map-btn) {
		position: absolute;
		right: 0;
		top: 0;
		width: $wot-tabs-nav-height;
		height: $wot-tabs-nav-height;
		line-height: $wot-tabs-nav-height;
		text-align: center;
		color: $wot-tabs-nav-map-arrow-color;
		z-index: 1;
		background: $wot-tabs-nav-bg;
		-webkit-tap-highlight-color: transparent;

		&::before {
			position: absolute;
			content: '';
			top: 0;
			left: -24px;
			width: 24px;
			height: $wot-tabs-nav-height - 1;
			background: $wot-tabs-nav-map-btn-before-bg;
		}
	}

	@include e(map-arrow) {
		display: block;
		transition: transform 0.3s;

		@include when(open) {
			transform: rotate(180deg);
		}
	}

	@include e(map-header) {
		position: relative;
		padding-left: 17px;
		height: $wot-tabs-nav-height;
		line-height: $wot-tabs-nav-height;
		font-size: $wot-tabs-nav-map-fs;
		color: $wot-tabs-nav-map-color;
		transition: opacity 0.3s;
		background: #fff;
		opacity: 0;

		@include halfPixelBorder;

		&::after {
			z-index: 3;
		}
	}

	@include e(map-body) {
		display: flex;
		flex-wrap: wrap;
		padding: 20px 15px 10px;
		background: #fff;
		transition: transform 0.3s;
		transform: scaleY(0);
		transform-origin: center top;

		@include when(open) {
			transform: scaleY(1);
		}
	}

	@include e(map-nav-item) {
		flex-basis: 33%;

		&:nth-child(3n + 2) {
			text-align: center;
		}

		&:nth-child(3n + 3) {
			text-align: right;
		}
	}

	@include e(map-nav-btn) {
		@include buttonClear;
		@include lineEllipsis;
		display: inline-block;
		width: 107px;
		height: 32px;
		line-height: 32px;
		background-color: $wot-tabs-nav-map-button-back-color;
		border-color: transparent;
		margin-bottom: 10px;
		border-radius: $wot-tabs-nav-map-button-radius;
		color: $wot-tabs-nav-map-color;
		font-size: $wot-tabs-nav-map-fs;
		text-align: center;
		transition:
			color 0.3s,
			border-color 0.3s;

		@include when(active) {
			color: $wot-tabs-nav-active-color;
			border: 1px solid $wot-tabs-nav-active-color;
			background-color: $wot-tabs-nav-bg;
		}

		@include when(disabled) {
			color: $wot-tabs-nav-disabled-color;
			border-color: #f4f4f4;
		}
	}

	@include e(mask) {
		position: absolute;
		top: $wot-tabs-nav-height;
		left: 0;
		right: 0;
		bottom: 0;
		background: $wot-tabs-nav-map-modal-bg;
		opacity: 0;
		transition: opacity 0.3s;
	}

	@include when(slide) {
		.wd-tabs__nav-item {
			flex: 0 0 auto;
			padding: 0 17px;
		}
	}

	@include when(map) {
		.wd-tabs__nav--wrap {
			padding-right: 40px;
		}
	}
}

@media screen and (max-width: 330px) {
	.wd-tabs__map-nav-btn {
		width: 90px;
	}
}
