<template>
	<view class="diw-currency" :class="customClass" :style="customStyle">
		<text v-if="prefix.visible" class="diw-currency__prefix">{{ prefix.text }}</text>
		<text class="diw-currency__number" :class="textClass" :style="textStyle">{{ displayText }}</text>
		<text v-if="suffix.visible" class="diw-currency__suffix">{{ suffix.text }}</text>
	</view>
</template>

<script setup lang="ts">
const props = withDefaults(
	defineProps<{
		value: number | string;
		suffix?: string;
		prefix?: string;

		// 根结点 class
		customClass?: DIWClassType;

		// 根结点 style
		customStyle?: DIWStyleType;

		textClass?: DIWClassType;

		textStyle?: DIWStyleType;

		precision?: number;
	}>(),
	{ suffix: '元', prefix: '', precision: 2 }
);

const customClass = computed(() => parseClass(props.customClass));
const customStyle = computed(() => parseStyle(props.customStyle));
const textClass = computed(() => parseClass(props.textClass));
const textStyle = computed(() => parseStyle(props.textStyle));

const displayText = computed(() => {
	return formatCurrency(props.value, { precision: props.precision });
});

const suffix = computed(() => {
	if (props.suffix) {
		return {
			visible: true,
			text: props.suffix,
		};
	}

	return {
		visible: false,
	};
});

const prefix = computed(() => {
	if (props.prefix) {
		return {
			visible: true,
			text: props.prefix,
		};
	}

	return {
		visible: false,
	};
});
</script>
