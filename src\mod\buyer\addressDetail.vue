<template>
	<DIWAppPage mode="1" :title="title">
		<DIWScrollView v-if="initOk">
			<wd-form ref="form" :model="model" :rules="rules">
				<wd-gap />
				<wd-cell-group border>
					<template v-if="isViewMode">
						<wd-cell title="收货人" title-width="33%" :value="model.name" />
						<wd-cell title="收货地址" title-width="33%" :value="model.addressDetail" />
						<wd-cell title="手机号码" title-width="33%" :value="model.phone" />
						<wd-cell title="固定号码" title-width="33%" :value="model.tel" />
						<wd-cell title="邮编" title-width="33%" :value="model.zipCode" />
						<wd-cell v-if="model.defaultFlag == 1">
							<template #label>
								<text class="text-orange">已设为默认收货地址</text>
							</template>
						</wd-cell>
					</template>
					<template v-else>
						<wd-input label="收货人" prop="name" required :maxlength="20" v-model="model.name" />
						<DIWSelect mode="area" v-model="model.area">
							<template #default="{ displayText }">
								<wd-cell title="收货地区" title-width="33%" prop="area" required is-link :value="displayText" custom-class="area-picker" />
							</template>
						</DIWSelect>
						<wd-input label="详细地址" prop="address" required :maxlength="50" v-model="model.address" />
						<wd-input label="手机号码" prop="phone" type="digit" :maxlength="20" inputmode="tel" required v-model="model.phone" />
						<wd-input label="固定号码" prop="tel" type="number" :maxlength="20" inputmode="tel" v-model="model.tel" />
						<wd-input label="邮编" prop="zipCode" type="digit" :maxlength="6" v-model="model.zipCode" />

						<wd-cell title="设为默认收货地址">
							<wd-switch v-model="model.defaultFlag" :active-value="1" :inactive-value="0" :size="20" />
						</wd-cell>
					</template>
				</wd-cell-group>
			</wd-form>
		</DIWScrollView>
		<template #bottomBar>
			<DIWAuth v-if="initOk" :auth="['admin_address_info_edit', 'admin_address_info_del']">
				<DIWBottomBar>
					<view class="flex flex-row items-center gap-4 mx-auto line-height-0">
						<template v-if="isViewMode">
							<DIWAuth auth="admin_address_info_edit">
								<wd-button @click="isViewMode1 = false">修改</wd-button>
							</DIWAuth>
							<DIWAuth auth="admin_address_info_del">
								<wd-button type="error" @click="deleteItem">删除</wd-button>
							</DIWAuth>
						</template>
						<template v-else>
							<wd-button @click="save">保存</wd-button>
							<wd-button type="info" @click="cancelEdit">取消</wd-button>
						</template>
					</view>
				</DIWBottomBar>
			</DIWAuth>
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
const {
	protect,
	protectOp,
	validateForm,
	apiGet,
	apiPut,
	apiPost,
	apiDelete,
	usePageQuery,
	usePageResult,
	sessionInfo,
	navigateBack,
	encodeArea,
	decodeArea,
} = useFramework();

const pq = usePageQuery();
const { finishPage, setPageResult } = usePageResult();

const isViewMode1 = ref(true);
const initOk = ref(false);

const isViewMode = computed(() => {
	if (pq.value.id) {
		return isViewMode1.value;
	}

	return false;
});

const title = computed(() => {
	if (pq.value.id) {
		return isViewMode.value ? '收货地址详情' : '修改收货地址';
	}

	return '添加收货地址';
});

const form = ref();

const rules = {
	name: [{ required: true, message: '请输入收货人' }],
	area: [{ required: true, message: '请选择收货地区' }],
	address: [{ required: true, message: '请输入详细地址' }],
	phone: [{ required: true, message: '请输入手机号码' }],
	tel: [{ required: false, message: '请输入固定号码' }],
	zipCode: [{ required: false, message: '请输入邮编' }],
};

const model = ref({
	name: '',
	phone: '',
	tel: '',
	zipCode: '',
	area: <string[] | undefined>undefined,
	address: '',
	addressDetail: '',
	defaultFlag: 0,
});

onMounted(() => {
	if (!sessionInfo.value) {
		restartViewMode();
	}
});

watchEffect(() => {
	if (sessionInfo.value) {
		restartViewMode();
	}
});

function reload() {
	protect(async () => {
		if (pq.value.id) {
			const d = await apiGet('admin/address/info/' + encodeURI(pq.value.id));
			model.value.name = d.name;
			model.value.phone = d.phone;
			model.value.tel = d.tel;
			model.value.zipCode = d.zipCode;
			model.value.address = d.address;
			model.value.addressDetail = d.addressDetail;
			model.value.defaultFlag = d.defaultFlag;
			model.value.area = encodeArea(d, 'province', 'city', 'district');
		}
		initOk.value = true;
	});
}

function restartViewMode() {
	initOk.value = false;
	isViewMode1.value = true;
	reload();
}

function cancelEdit() {
	if (pq.value.id) {
		restartViewMode();
	} else {
		navigateBack();
	}
}

function deleteItem() {
	protectOp('继续操作将删除数据', async () => {
		await apiDelete({ url: 'admin/address/info', data: [pq.value.id] });
		finishPage();
	});
}

function save() {
	protectOp(async () => {
		await validateForm(form);

		const data = Object.assign({}, model.value, {
			addressDetail: undefined,
			area: undefined,
			...decodeArea(model.value.area, 'province', 'city', 'district'),
		});

		if (pq.value.id) {
			await apiPut({
				url: 'admin/address/info',
				data: Object.assign({}, data, {
					id: pq.value.id,
				}),
			});

			setPageResult();

			restartViewMode();
		} else {
			await apiPost({
				url: 'admin/address/info',
				data,
			});

			finishPage();
		}
	});
}
</script>

<style scoped lang="scss">
:deep() .area-picker {
	.wd-cell__value {
		text-align: left;
	}
}
</style>
