@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(select-picker) {
		:deep(.wd-select-picker__arrow),
		:deep(.wd-select-picker__close),
		:deep(.wd-select-picker__clear) {
			color: $wot-dark-color;
		}

		:deep(.wd-select-picker__cell--placeholder) {
			.wd-cell__value {
				color: $wot-dark-color-gray;
			}
		}
	}
}

@include b(select-picker) {
	@include edeep(cell) {
		@include when(disabled) {
			.wd-cell__value {
				color: $wot-input-disabled-color;
				cursor: not-allowed;
			}
		}
		@include when(error) {
			.wd-cell__value {
				color: $wot-input-error-color;
			}
			.wd-select-picker__arrow {
				color: $wot-input-error-color;
			}
		}

		@include m(placeholder) {
			.wd-cell__value {
				color: $wot-input-placeholder-color;
			}
		}

		@include when(large) {
			.wd-select-picker__arrow,
			.wd-select-picker__clear {
				font-size: $wot-cell-icon-size-large;
			}
		}
	}

	@include edeep(arrow, clear) {
		display: block;
		font-size: $wot-cell-icon-size;
		color: $wot-cell-arrow-color;
		line-height: $wot-cell-line-height;
	}

	@include edeep(clear) {
		color: $wot-cell-clear-color;
	}

	@include e(loading) {
		position: absolute;
		display: flex;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		align-items: center;
		justify-content: center;
		z-index: 3;
		background: $wot-picker-loading-bg;
	}

	@include edeep(header) {
		height: 72px;
		line-height: 72px;
	}
	@include e(wrapper) {
		padding: 0 10px;
		position: relative;
		max-height: 356px;
		box-sizing: border-box;
		overflow: auto;

		@include when(filterable) {
			height: 314px;
			max-height: 314px;
		}

		@include when(loading) {
			overflow: hidden;
		}
	}
	@include e(text-active) {
		color: $wot-color-theme;
	}

	@include e(footer) {
		padding: 24px 15px;
	}
}
