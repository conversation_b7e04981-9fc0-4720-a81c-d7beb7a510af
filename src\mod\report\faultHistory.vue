<!-- 故障历史记录页面 -->
<template>
	<DIWAppPage>
		<template #navbar>
			<wd-navbar :title="t('historyData')" left-arrow @click-left="navigateBack()" :border="false" :fixed="true" placeholder />
		</template>

		<view class="history-page">
			<!-- 状态筛选标签 -->
			<view class="filter-tabs">
				<view
					v-for="tab in filterTabs"
					:key="tab.value"
					class="filter-tab"
					:class="{ active: currentFilter === tab.value }"
					@click="changeFilter(tab.value)"
				>
					{{ tab.label }}
				</view>
			</view>

			<DIWScrollView>
				<DIWListView ref="listViewRef" :meta="listViewMeta">
					<template #default="{ data }">
						<view class="history-container">
							<view class="history-item" @click="viewDetail(data)">
								<view class="item-header">
									<view class="machine-info">
										<text class="machine-number">{{ t('machineNumber') }}：{{ data.machineNumber }}</text>
										<view class="status-badge" :class="getStatusClass(data.status)">
											{{ getStatusText(data.status) }}
										</view>
									</view>
								</view>

								<view class="item-content">
									<view class="fault-info">
										<text class="fault-type">{{ t('faultType') }}：{{ data.faultType }}</text>
									</view>
									<view class="fault-desc">
										<text class="desc-text">{{ t('faultDescription') }}：{{ data.faultDescription }}</text>
									</view>
									<view class="item-time">
										<wd-icon name="time" size="24rpx" color="#999" />
										<text class="time-text">{{ formatDateTime(data.createTime, 'YYYY-MM-DD HH:mm:ss') }}</text>
									</view>
								</view>
							</view>
						</view>
					</template>
				</DIWListView>
			</DIWScrollView>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { formatDateTime } from '@/util/number';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { navigateTo, navigateBack } = useFramework();

// 列表引用
const listViewRef = ref();

// 筛选状态
const currentFilter = ref('all');

// 筛选标签
const filterTabs = computed(() => [
	{ label: t('allStatus'), value: 'all' },
	{ label: t('pending'), value: 'pending' },
	{ label: t('processing'), value: 'processing' },
	{ label: t('completed'), value: 'completed' },
]);

// 模拟数据
const getMockData = () => {
	const allData = [
		{
			id: '1',
			machineNumber: '细纱机-001',
			faultType: '机台异常停车',
			faultDescription: '主轴转速不稳定，有异响',
			status: 'pending',
			createTime: '2025-07-17 10:24:38',
			processTime: null,
		},
		{
			id: '2',
			machineNumber: '细纱机-002',
			faultType: '机台异常停车',
			faultDescription: '主轴转速不稳定，有异响',
			status: 'completed',
			createTime: '2025-07-17 10:24:38',
			processTime: '2025-07-17 11:30:00',
		},
		{
			id: '3',
			machineNumber: '细纱机-003',
			faultType: '机台异常停车',
			faultDescription: '主轴转速不稳定，有异响',
			status: 'processing',
			createTime: '2025-07-17 10:24:38',
			processTime: null,
		},
		{
			id: '4',
			machineNumber: '细纱机-004',
			faultType: '机台异常停车',
			faultDescription: '主轴转速不稳定，有异响',
			status: 'completed',
			createTime: '2025-07-17 10:24:38',
			processTime: '2025-07-17 12:15:30',
		},
	];

	// 根据筛选条件过滤数据
	const filteredData = currentFilter.value === 'all' ? allData : allData.filter((item) => item.status === currentFilter.value);

	return {
		items: filteredData,
		total: filteredData.length,
	};
};

// DIWListView meta配置
const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		rowKey: 'id',
		async loadData(_pageIndex) {
			// 模拟API调用
			return new Promise((resolve) => {
				setTimeout(() => {
					const mockData = getMockData();
					resolve({
						hasMore: false, // 简单示例，不分页
						items: mockData.items,
						trackInfo: 1,
					});
				}, 300);
			});
		},
	});
});

// 切换筛选条件
const changeFilter = (value: string) => {
	currentFilter.value = value;
	// 重新加载数据
	nextTick(() => {
		if (listViewRef.value) {
			listViewRef.value.reload2(true);
		}
	});
};

// 获取状态样式
const getStatusClass = (status: string) => {
	switch (status) {
		case 'completed':
			return 'success';
		case 'processing':
			return 'warning';
		case 'pending':
			return 'error';
		default:
			return '';
	}
};

// 获取状态文本
const getStatusText = (status: string) => {
	switch (status) {
		case 'completed':
			return t('completed');
		case 'processing':
			return t('processing');
		case 'pending':
			return t('pending');
		default:
			return t('unknown');
	}
};

// 查看详情
const viewDetail = (item: any) => {
	navigateTo(`/mod/report/faultDetail?id=${item.id}`);
};
</script>

<style lang="scss" scoped>
.history-page {
	background: #f5f5f5;
	min-height: 100vh;
}

.filter-tabs {
	display: flex;
	padding: 24rpx 32rpx;
	gap: 24rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-tab {
	padding: 12rpx 24rpx;
	border-radius: 32rpx;
	font-size: 28rpx;
	color: #666;
	background: #f8f9fa;
	transition: all 0.3s ease;

	&.active {
		background: #1c64fd;
		color: #fff;
	}
}

.history-container {
	padding: 32rpx;
}

.history-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.machine-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}

.machine-number {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.item-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.fault-info {
	display: flex;
	align-items: center;
}

.fault-type {
	font-size: 28rpx;
	color: #666;
}

.fault-desc {
	display: flex;
	align-items: flex-start;
}

.desc-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.item-time {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.time-text {
	font-size: 26rpx;
	color: #999;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;

	&.success {
		background: #f6ffed;
		color: #52c41a;
		border: 1rpx solid #b7eb8f;
	}

	&.warning {
		background: #fff7e6;
		color: #fa8c16;
		border: 1rpx solid #ffd591;
	}

	&.error {
		background: #fff2f0;
		color: #ff4d4f;
		border: 1rpx solid #ffccc7;
	}
}
</style>
