@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(drop-item) {
		color: $wot-dark-color;

		@include e(tip) {
			color: $wot-dark-color3;
		}
	}
}

@include b(drop-item) {
	position: fixed;
	right: 0;
	left: 0;
	overflow: hidden;
	font-size: $wot-drop-menu-item-fs;
	color: $wot-drop-menu-item-color;
	width: 100%;
	z-index: 101;

	@include e(popup) {
		position: absolute;
		max-height: 80%;
	}

	@include e(option) {
		display: flex;
		height: $wot-drop-menu-item-height;
		line-height: $wot-drop-menu-item-height;
		padding: 0 $wot-drop-menu-side-padding;
		justify-content: space-between;
		align-items: center;
		transition: color 0.2s;

		@include when(active) {
			color: $wot-drop-menu-item-color-active;
		}
	}

	@include e(title) {
		display: block;
	}

	@include e(tip) {
		display: inline-block;
		color: $wot-drop-menu-item-color-tip;
		font-size: $wot-drop-menu-item-fs-tip;
		margin-left: 2px;
	}

	@include edeep(icon) {
		display: block;
		font-size: $wot-drop-menu-option-check-size;
	}

	@include e(modal) {
		position: fixed;
		left: 0;
		right: 0;
		background: rgba(0, 0, 0, 0.7);
		height: 100%;
	}
}
