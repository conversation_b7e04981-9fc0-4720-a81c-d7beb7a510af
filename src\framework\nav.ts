import { makeAbortError } from './error';

const PAGE_RESULT_PROP = '$dwNavResult';
const EVENT_SEND_INIT_DATA = '$eventSendInitData';
const EVENT_INIT_DATA = '$eventInitData';

async function navigateToImpl(options: DIWNavigateToOptions) {
	let url = options.url;
	if (url.startsWith('https://') || url.startsWith('http://')) {
		url = '/framework/pages/browser?url=' + encodeURIComponent(url);
		if (options.title !== undefined) {
			url += '&title=';
			url += encodeURIComponent(options.title);
		}

		return await uni.navigateTo({ url });
	}

	if (options.params) {
		const qp = Object.keys(options.params)
			.map((key) => {
				let value = options.params![key];
				if (typeof value !== 'string') {
					if (value === null || value === undefined) {
						value = '';
					} else {
						value = '' + value;
					}
				}
				return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
			})
			.join('&');

		if (url.indexOf('?') >= 0) {
			url = `${url}&${qp}`;
		} else {
			url = `${url}?${qp}`;
		}
	}

	if (options.mode === 'switchTab') {
		return await uni.switchTab({ url });
	}

	if (options.mode === 'reLaunch') {
		return await uni.reLaunch({ url });
	}

	if (options.mode === 'redirect') {
		return await uni.redirectTo({ url });
	}

	return await uni.navigateTo({ url });
}

export async function navigateTo(options: string | DIWNavigateToOptions) {
	if (typeof options === 'string') {
		return await navigateToImpl({
			url: options,
		});
	}

	return await navigateToImpl(options);
}

export async function invokeTo<T>(options: string | DIWInvokeToOptions): Promise<DIWInvokeToResult<T>> {
	if (typeof options === 'string') {
		return await invokeToImpl({
			url: options,
		});
	}

	return await invokeToImpl(options);
}

async function invokeToImpl<T>(options: DIWInvokeToOptions): Promise<DIWInvokeToResult<T>> {
	let url = options.url;

	if (options.params) {
		const qp = Object.keys(options.params)
			.map((key) => {
				let value = options.params![key];
				if (typeof value !== 'string') {
					if (value === null || value === undefined) {
						value = '';
					} else {
						value = '' + value;
					}
				}
				return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
			})
			.join('&');

		if (url.indexOf('?') >= 0) {
			url = `${url}&${qp}`;
		} else {
			url = `${url}?${qp}`;
		}
	}

	const d = await uni.navigateTo({ url });

	return new Promise((resolve, reject) => {
		d.eventChannel.once(PAGE_RESULT_PROP, (result) => {
			if (result && result.hasResult === true) {
				resolve({ result: result.result });
			} else {
				reject(makeAbortError());
			}
		});

		if (options.data) {
			d.eventChannel.once(EVENT_SEND_INIT_DATA, () => {
				d.eventChannel.emit(EVENT_INIT_DATA, options.data);
			});
		}
	});
}

export async function navigateBack() {
	await uni.navigateBack();
}

export function usePageResult() {
	const inst = getCurrentInstance()!.proxy!;
	let ch: UniApp.EventChannel | undefined = (inst as any).getOpenerEventChannel();
	let hasResult = false;
	let result1: any;

	onMounted(() => {
		if (!ch) {
			ch = (inst as any).getOpenerEventChannel();
		}
	});

	onUnload(() => {
		if (ch) {
			ch.emit(PAGE_RESULT_PROP, { hasResult, result: result1 });
		}
	});

	function setPageResult(result?: any) {
		hasResult = true;
		result1 = result;
	}

	async function finishPage(result?: any) {
		hasResult = true;
		result1 = result;
		return await navigateBack();
	}

	return { setPageResult, finishPage };
}

export function usePageQuery() {
	const result = ref<Record<string, string>>({});

	onLoad((query) => {
		if (query) {
			const v: Record<string, string> = {};
			Object.keys(query).forEach((e) => {
				let value = query[e];
				// #ifdef MP-WEIXIN || APP-PLUS
				value = decodeURIComponent(value);
				// #endif
				v[e] = value;
			});

			result.value = v;
		}
	});

	return result;
}

export function usePageInitData() {
	const inst = getCurrentInstance()!.proxy!;
	let ch: UniApp.EventChannel | undefined = (inst as any).getOpenerEventChannel();

	const initData = ref<Record<string, any> | null>(null);

	onMounted(() => {
		if (!ch) {
			ch = (inst as any).getOpenerEventChannel();
		}

		if (ch) {
			ch.once(EVENT_INIT_DATA, (data: Record<string, any>) => {
				initData.value = data;
			});
			ch.emit(EVENT_SEND_INIT_DATA);
		}
	});

	return initData;
}
