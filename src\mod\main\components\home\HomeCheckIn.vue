<template>
	<view class="checkin-section">
		<view class="checkin-card">
			<view class="checkin-header">
				<view class="title-wrapper">
					<view class="title-wrapper-inner">
						<view class="title-indicator"></view>
						<text class="title">{{ t('punchCard') }}</text>
					</view>
					<wd-icon name="arrow-right" size="24rpx" color="#1D2129" class="ml-16rpx" />
				</view>

				<!-- 筛选出当前日期 -->
				<wd-picker :columns="shiftScheduleColumns" v-model="shiftSchedule" @confirm="onChangeTime" />
				<view class="query-btn">
					<text>{{ t('askForLeave') }}</text>
				</view>
			</view>

			<view class="checkin-content">
				<!-- 上班打卡 -->
				<view class="checkin-item left">
					<view class="checkin-icon-item">
						<view class="checkin-icon on-duty">
							<wd-icon name="time" size="32rpx" color="#4A90E2" />
						</view>
						<view class="checkin-info">
							<text class="checkin-label">{{ t('workPunchTime') }}</text>
							<text class="checkin-time">{{ checkinData.onDutyTime || '--:--' }}</text>
						</view>
					</view>
					<view class="checkin-status" :class="checkinData.onDutyStatus || 'normal'">
						<text>{{ getStatusText(checkinData.onDutyStatus || 'normal') }}</text>
					</view>
				</view>

				<!-- 下班打卡 -->
				<view class="checkin-item right">
					<view class="checkin-icon-item">
						<view class="checkin-icon off-duty">
							<wd-icon name="time" size="32rpx" color="#4A90E2" />
						</view>
						<view class="checkin-info">
							<text class="checkin-label">{{ t('offWorkPunchTime') }}</text>
							<text class="checkin-time">{{ checkinData.offDutyTime || '--:--' }}</text>
						</view>
					</view>
					<view class="checkin-status" :class="checkinData.offDutyStatus || 'normal'">
						<text>{{ getStatusText(checkinData.offDutyStatus || 'normal') }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();
const { apiGet, protect } = useFramework();

const props = defineProps<{
	shiftScheduleList: {
		planStartTime: string;
		shiftCodeName: string;
	}[];
	needRefresh: boolean;
}>(); //筛选出当前日期

const checkinData = ref({
	date: '2024-01-15',
	shift: '',
	onDutyTime: '08:30',
	offDutyTime: '17:30',
	onDutyStatus: 'normal', // normal, late, absent
	offDutyStatus: 'normal', // normal, early, overtime
});

const { shiftScheduleList, needRefresh } = toRefs(props);

const shiftSchedule = ref('');

async function loadData() {
	await protect(async () => {
		const d = await apiGet('mesmultidata/DiwShiftSchedule/hisToTodaylist');
		checkinData.value = d || [];
	});
}

watch(
	() => needRefresh,
	async (newVal) => {
		if (newVal) {
			await loadData();
		}
	}
);

const shiftScheduleColumns = computed(() => {
	if (!shiftScheduleList.value || shiftScheduleList.value.length === 0) {
		return [];
	}
	return shiftScheduleList.value.map((item) => ({
		label: item.planStartTime + ' ' + item.shiftCodeName,
	}));
});

// 监听 shiftScheduleList 的变化，设置默认值
watch(
	shiftScheduleList,
	(newList) => {
		if (newList && newList.length > 0 && !checkinData.value.date) {
			checkinData.value.date = newList[0].planStartTime;
			shiftSchedule.value = newList[0].planStartTime;
		}
	},
	{ immediate: true }
);

onMounted(async () => {
	await loadData();
});

// 状态文本映射
const getStatusText = (status: string) => {
	const statusMap: Record<string, string> = {
		normal: t('normal'),
		late: t('late'),
		early: t('earlyLeave'),
		absent: t('absent'),
		overtime: t('overtime'),
	};
	return statusMap[status] || '未知';
};

const onChangeTime = (value: any) => {
	console.log(`Selected item: ${value}`);
};
</script>

<style lang="scss" scoped>
.checkin-section {
	margin-top: 26rpx;
	width: 702rpx;
	height: 190rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 702rpx 190rpx;
	background-repeat: no-repeat;
	background-position: 0 0;
}

.checkin-card {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin: 20rpx 36rpx 0 36rpx;
}

.checkin-header {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title-wrapper {
		display: flex;
		align-items: center;
		.title-wrapper-inner {
			display: flex;
			align-items: center;

			.title-indicator {
				width: 8rpx;
				height: 32rpx;
				background: #0082f0;
				border-radius: 4rpx;
				margin-right: 16rpx;
			}

			.title {
				font-size: 30rpx;
				font-weight: normal;
				line-height: 24rpx;
				text-align: center;
				letter-spacing: 0rpx;
				color: #1d2129;
			}
		}
	}

	.date-selector {
		display: flex;
		align-items: center;
		gap: 8rpx;

		.date-selector-inner,
		.evening-selector-inner {
			display: flex;
			align-items: center;
		}

		.date-text,
		.evening-text {
			font-size: 24rpx;
			font-weight: normal;
			line-height: 32rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #4b5563;
		}
	}

	.query-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8rpx 12rpx;
		font-size: 20rpx;
		font-weight: normal;
		line-height: 18rpx;
		letter-spacing: 0rpx;
		color: #ffffff;
		background: linear-gradient(90deg, #2b6fea 0%, #4397fe 100%);
		border-radius: 18rpx;
	}
}

.checkin-content {
	width: 100%;
	margin-top: 20rpx;
	display: flex;
	flex-direction: row;
	gap: 18rpx;

	.checkin-item {
		width: 50%;
		background: #f6f7ff;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		position: relative;
		padding: 4rpx 16rpx;
		position: relative;

		.checkin-icon-item {
			display: flex;
			justify-content: center;
			align-items: flex-start;
			gap: 12rpx;

			.checkin-icon {
				width: 28rpx;
				height: 28rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: rgba(74, 144, 226, 0.1);
			}

			.checkin-info {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				justify-content: center;
				margin-top: -4rpx;

				.checkin-label {
					font-size: 24rpx;
					font-weight: normal;
					line-height: 40rpx;
					letter-spacing: 0rpx;
					color: #4b5563;
				}

				.checkin-time {
					font-size: 28rpx;
					font-weight: normal;
					line-height: 40rpx;
					letter-spacing: 0rpx;
					color: #1d2129;
				}
			}
		}
		.checkin-status {
			position: absolute;
			right: 0;
			top: 0;
			padding: 8rpx 16rpx;
			border-radius: 0px 16rpx 0px 16rpx;

			font-size: 20rpx;
			font-weight: normal;
			line-height: 10rpx;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0rpx;

			&.normal {
				background: #0082f0;
				color: #ffffff;
			}

			&.early {
				background: #ef4444;
				color: #ffffff;
			}

			&.late {
				background: #ffc107;
				color: #ffffff;
			}

			&.absent {
				background: #f56565;
				color: #ffffff;
			}

			&.overtime {
				background: #38a169;
				color: #ffffff;
			}
		}
	}
}
:deep(.wd-picker) {
	.wd-cell {
		padding: 0;
		background-color: transparent;
		.wd-cell__wrapper {
			padding: 0;
		}
	}
}
</style>
