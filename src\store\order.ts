import { useSystemStore } from './system';

export const useOrderStore = defineStore('diw_order_store', () => {
	const ss = useSystemStore();
	const { apiGet } = ss;

	const orderCache = new Map<string, Record<string, any>>();

	async function getOrderDetail(orderId: string): Promise<Record<string, any>> {
		const o = orderCache.get(orderId);
		if (o) {
			return o;
		}

		const d = await apiGet({ url: 'order/order/detail', params: { orderId } });
		orderCache.set(orderId, d);
		return d;
	}

	// 用于退出登录时清空缓存
	function flush() {
		orderCache.clear();
	}

	return { getOrderDetail, flush };
});
