@use '../common/abstracts/variable.scss' as *;
@use '../common/abstracts/_mixin.scss' as *;

.wot-theme-dark {
	@include b(message-box) {
		@include e(body) {
			background-color: $wot-dark-background2;
		}

		@include e(title) {
			color: $wot-dark-color;
		}

		@include e(content) {
			color: $wot-dark-color3;

			&::-webkit-scrollbar-thumb {
				background: $wot-dark-border-color;
			}
		}
	}
}

:deep(.wd-message-box) {
	border-radius: $wot-message-box-radius;
	overflow: hidden;
}

@include b(message-box) {
	border-radius: $wot-message-box-radius;
	overflow: hidden;

	@include e(container) {
		width: $wot-message-box-width;
		box-sizing: border-box;
	}

	@include e(body) {
		background-color: $wot-message-box-bg;
		padding: $wot-message-box-padding;

		@include when(no-title) {
			padding: 25px 24px 0px;
		}
	}

	@include e(title) {
		text-align: center;
		font-size: $wot-message-box-title-fs;
		color: $wot-message-box-title-color;
		line-height: 20px;
		font-weight: 500;
		padding-top: 5px;
		padding-bottom: 10px;
	}

	@include e(content) {
		max-height: $wot-message-box-content-max-height;
		color: $wot-message-box-content-color;
		font-size: $wot-message-box-content-fs;
		text-align: center;
		overflow: auto;
		line-height: 20px;

		&::-webkit-scrollbar {
			width: $wot-message-box-content-scrollbar-width;
		}

		&::-webkit-scrollbar-thumb {
			width: $wot-message-box-content-scrollbar-width;
			background: $wot-message-box-content-scrollbar-color;
			border-radius: calc($wot-message-box-content-scrollbar-width / 2);
		}
	}

	@include e(input-error) {
		min-height: 18px;
		margin-top: 2px;
		color: $wot-message-box-input-error-color;
		text-align: left;

		@include when(hidden) {
			visibility: hidden;
		}
	}

	@include e(actions) {
		padding: 24px;
	}

	@include edeep(actions-btn) {
		&:not(:last-child) {
			margin-right: 16px;
		}
	}

	@include e(flex) {
		display: flex;
	}

	@include e(block) {
		display: block;
	}

	@include e(cancel) {
		margin-right: 16px;
	}
}
