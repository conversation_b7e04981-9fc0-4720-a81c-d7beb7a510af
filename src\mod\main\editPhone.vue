<template>
	<DIWAppPage>
		<DIWProtect>
			<template v-if="step === 0">
				<view class="flex flex-col p-4 gap-2">
					<text>手机号码可以用于短信验证码登录</text>
					<text>
						当前绑定的手机号码为
						<text class="text-blue-500">{{ sessionInfo!.phone }}</text>
					</text>
					<text>如有需要，您可以解绑当前的手机号码</text>
					<wd-button v-if="mode === 0" type="error" @click="preUnbind()">解绑</wd-button>
					<text>
						解绑手机号码后，您仍然可以通过用户名和密码登录，
						<text class="text-orange-500">但是无法通过短信验证码登录</text>
					</text>
					<template v-if="mode === 1">
						<text class="mt-8">验证码已发送至手机，请在下面的输入框中输入验证码，并点击解绑按钮</text>
						<wd-form ref="unbindForm" :model="unbindModel">
							<wd-cell-group border>
								<wd-input
									label="验证码"
									prop="code"
									v-model="unbindModel.code"
									inputmode="numeric"
									type="number"
									:maxlength="4"
									required
									:rules="[{ required: true, message: '请输入验证码' }]"
								/>
							</wd-cell-group>
							<view class="flex flex-row gap-4 mt-4 justify-center">
								<wd-button @click="unbind()">解绑</wd-button>
								<wd-button type="info" @click="navigateBack()">取消</wd-button>
							</view>
						</wd-form>
					</template>
				</view>
			</template>
			<template v-else>
				<view class="flex flex-col p-4 gap-2">
					<text>手机号码可以用于短信验证码登录</text>
					<wd-form ref="bindForm" :model="bindModel">
						<wd-cell-group border>
							<wd-input
								label="手机号码"
								prop="phone"
								v-model="bindModel.phone"
								inputmode="numeric"
								type="number"
								:maxlength="11"
								required
								:rules="[{ required: true, message: '请输入手机号码' }]"
							/>
							<wd-input
								label="验证码"
								prop="code"
								v-model="bindModel.code"
								inputmode="numeric"
								type="number"
								:maxlength="4"
								required
								:rules="[{ required: true, message: '请输入验证码' }]"
							>
								<template #suffix>
									<view class="line-height-0">
										<wd-button :disabled="!isValidMobile" size="small" @click="preBind()">发送验证码</wd-button>
									</view>
								</template>
							</wd-input>
						</wd-cell-group>
						<view class="flex flex-row gap-4 mt-4 justify-center">
							<wd-button @click="bind()">绑定</wd-button>
							<wd-button type="info" @click="navigateBack()">取消</wd-button>
						</view>
					</wd-form>
				</view>
			</template>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { sessionInfo, navigateBack, protectOp, apiGet, apiPut, validateForm, reloadSessionInfo } = useFramework();
const mode = ref(0);

const unbindForm = ref();

const unbindModel = ref({
	code: '',
});

const bindForm = ref();

const bindModel = ref({ phone: '', code: '' });

const step = ref(0);

function preUnbind() {
	protectOp(async () => {
		await apiGet({ url: 'msg/sysMessage/send/smsCode', params: { mobile: sessionInfo.value!.phone, type: 'UNBIND' } });
		mode.value = 1;
	}, '验证码已发送');
}

const isValidMobile = computed(() => {
	return bindModel.value.phone.length === 11;
});

function unbind() {
	protectOp(async () => {
		await validateForm(unbindForm);
		await apiPut({
			url: 'admin/user/unbindPhone',
			data: { code: unbindModel.value.code },
			header: { 'Content-Type': 'application/x-www-form-urlencoded' },
		});
		unbindModel.value.code = '';
		step.value = 1;
		// await reloadSessionInfo();
	});
}

function preBind() {
	protectOp(async () => {
		await apiGet({ url: 'msg/sysMessage/send/sendSmsCodeMethod', params: { mobile: bindModel.value.phone, type: 'BIND' } });
	}, '验证码已发送');
}

function bind() {
	protectOp(async () => {
		await validateForm(unbindForm);
		await apiPut({
			url: 'admin/user/bindPhone',
			data: { phone: bindModel.value.phone, code: bindModel.value.code },
			header: { 'Content-Type': 'application/x-www-form-urlencoded' },
		});
		bindModel.value.phone = '';
		bindModel.value.code = '';
		await reloadSessionInfo();
		navigateBack();
	});
}
</script>
