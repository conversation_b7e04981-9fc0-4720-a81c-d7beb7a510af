@use '../common/abstracts/variable.scss' as *;
@use '../common/abstracts/_mixin.scss' as *;
.wot-theme-dark {
	@include b(card) {
		background-color: $wot-dark-background2;

		@include when(rectangle) {
			.wd-card__content {
				@include halfPixelBorder('top', 0, $wot-dark-border-color);
			}
			.wd-card__footer {
				@include halfPixelBorder('top', 0, $wot-dark-border-color);
			}
		}

		@include e(title-content) {
			color: $wot-dark-color;
		}
		@include e(content) {
			color: $wot-dark-color3;
		}
	}
}

@include b(card) {
	padding: $wot-card-padding;
	background-color: $wot-card-bg;
	line-height: $wot-card-line-height;
	margin: $wot-card-margin;
	border-radius: $wot-card-radius;
	box-shadow: $wot-card-shadow-color;
	font-size: $wot-card-fs;
	margin-bottom: 12px;

	@include when(rectangle) {
		margin-left: 0;
		margin-right: 0;
		border-radius: 0;
		box-shadow: none;

		.wd-card__title-content {
			font-size: $wot-card-fs;
		}
		.wd-card__content {
			position: relative;
			padding: $wot-card-rectangle-content-padding;

			@include halfPixelBorder('top', 0, $wot-card-content-border-color);
		}
		.wd-card__footer {
			position: relative;
			padding: $wot-card-rectangle-footer-padding;

			@include halfPixelBorder('top', 0, $wot-card-content-border-color);
		}
	}
	@include e(title-content) {
		padding: 16px 0;
		color: $wot-card-title-color;
		font-size: $wot-card-title-fs;
	}
	@include e(content) {
		color: $wot-card-content-color;
		line-height: $wot-card-content-line-height;
	}
	@include e(footer) {
		padding: $wot-card-footer-padding;
		text-align: right;
	}
}
