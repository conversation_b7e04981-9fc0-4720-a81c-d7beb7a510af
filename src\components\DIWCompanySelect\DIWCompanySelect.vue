<template>
	<view class="company-select-container">
		<view class="company-select-input" @click="openPopup">
			<view class="company-select-value">
				<text v-if="value">{{ value }}</text>
				<text v-else class="placeholder">{{ placeholder }}</text>
			</view>
			<view class="company-select-arrow">
				<wd-icon name="arrow-down" size="12px"></wd-icon>
			</view>
		</view>

		<wd-popup v-model="popupVisible" position="bottom" :close-on-click-modal="true" @close="handleClose">
			<view class="company-select-popup">
				<view class="company-select-header">
					<view class="company-select-title">选择企业</view>
					<view class="company-select-close" @click="handleClose">
						<wd-icon name="close"></wd-icon>
					</view>
				</view>

				<view class="company-select-search">
					<wd-input v-model="searchText" placeholder="请输入关键字搜索" clearable @clear="handleClear" @input="handleSearchInput">
						<template #prefix>
							<wd-icon name="search"></wd-icon>
						</template>
					</wd-input>
				</view>

				<view class="company-select-content">
					<wd-loading v-if="loading" color="#e43a3d" />
					<scroll-view v-else scroll-y class="company-list-scroll" :style="{ height: '400rpx' }">
						<view v-if="companies.length === 0" class="company-empty">
							<text>暂无企业数据</text>
						</view>
						<view v-else class="company-list">
							<view v-for="(item, index) in companies" :key="index" class="company-item" @click="handleCompanySelect(item)">
								<view class="company-item-name">{{ item.name }}</view>
								<view class="company-item-code">{{ item.credit_no }}</view>
							</view>
						</view>
					</scroll-view>
				</view>

				<view class="company-select-footer">
					<view class="pagination-container">
						<view class="pagination-prev" @click="prevPage" :class="{ disabled: currentPage <= 1 }">
							<wd-icon name="arrow-left"></wd-icon>
						</view>
						<view class="pagination-info">{{ currentPage }}/{{ totalPages }}</view>
						<view class="pagination-next" @click="nextPage" :class="{ disabled: currentPage >= totalPages }">
							<wd-icon name="arrow-right"></wd-icon>
						</view>
						<view class="pagination-total">总共 {{ total }} 条</view>
					</view>
				</view>
			</view>
		</wd-popup>
	</view>
</template>

<script setup lang="ts">
const { apiRequest, protect } = useFramework();

const props = defineProps({
	placeholder: {
		type: String,
		default: '请选择工商企业',
	},
});

const emit = defineEmits(['select', 'close']);

const value = ref('');
const popupVisible = ref(true);
const searchText = ref('');
const loading = ref(false);
const companies = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const totalPages = computed(() => {
	return Math.ceil(total.value / pageSize.value) || 1;
});

// 打开弹窗
function openPopup() {
	popupVisible.value = true;
	loadCompanies();
}

// 搜索输入处理
function handleSearchInput() {
	currentPage.value = 1;
	loadCompanies();
}

// 清除搜索文本
function handleClear() {
	searchText.value = '';
	currentPage.value = 1;
	loadCompanies();
}

// 关闭弹窗
function handleClose() {
	emit('close');
}

// 加载企业数据
async function loadCompanies() {
	loading.value = true;

	try {
		const skip = (currentPage.value - 1) * pageSize.value;
		const res = await apiRequest({
			url: 'admin/userCompanyInfo/getCompanyInfoPageByName',
			method: 'GET',
			params: {
				keyword: searchText.value,
				skip: skip,
			},
		});

		if (res && res.data) {
			companies.value = res.data.items || [];
			total.value = res.data.total || 0;
		} else {
			companies.value = [];
			total.value = 0;
		}
	} catch (error) {
		console.error('加载企业数据失败', error);
		companies.value = [];
		total.value = 0;
	} finally {
		loading.value = false;
	}
}

// 选择企业
async function handleCompanySelect(company: any) {
	protect(async () => {
		try {
			const res = await apiRequest({
				url: 'admin/userCompanyInfo/getCompanyInfoByName',
				method: 'GET',
				params: {
					keyword: company.name,
				},
			});

			let extraData = undefined;
			if (res && res.data) {
				extraData = res.data;
			}

			value.value = company.name;
			popupVisible.value = false;
			emit('select', { ...company, ...extraData });
			emit('close');
		} catch (error) {
			console.error('获取企业详情失败', error);
		}
	});
}

// 上一页
function prevPage() {
	if (currentPage.value > 1) {
		currentPage.value--;
		loadCompanies();
	}
}

// 下一页
function nextPage() {
	if (currentPage.value < totalPages.value) {
		currentPage.value++;
		loadCompanies();
	}
}
</script>

<style lang="scss" scoped>
.company-select-container {
	position: relative;
	width: 100%;
}

.company-select-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	height: 100%;
	padding: 0 4rpx;
}

.company-select-value {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.placeholder {
	color: #999;
}

.company-select-arrow {
	margin-left: 10rpx;
	color: #666;
}

.company-select-popup {
	background-color: #fff;
	border-radius: 16rpx 16rpx 0 0;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	max-height: 80vh;
}

.company-select-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.company-select-title {
	font-size: 34rpx;
	font-weight: 500;
	color: #333;
}

.company-select-close {
	padding: 10rpx;
}

.company-select-search {
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.company-select-content {
	flex: 1;
	overflow: hidden;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.company-list-scroll {
	width: 100%;
}

.company-empty {
	padding: 40rpx 0;
	text-align: center;
	color: #999;
	font-size: 28rpx;
}

.company-list {
	padding: 0 30rpx;
}

.company-item {
	padding: 30rpx 0;
	border-bottom: 1px solid #f5f5f5;
}

.company-item:last-child {
	border-bottom: none;
}

.company-item-name {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.company-item-code {
	font-size: 26rpx;
	color: #999;
}

.company-select-footer {
	padding: 20rpx 30rpx;
	border-top: 1px solid #f0f0f0;
}

.pagination-container {
	display: flex;
	align-items: center;
	justify-content: center;
}

.pagination-prev,
.pagination-next {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #333;
}

.pagination-prev.disabled,
.pagination-next.disabled {
	color: #ccc;
}

.pagination-info {
	margin: 0 20rpx;
	color: #666;
	font-size: 28rpx;
}

.pagination-total {
	margin-left: 40rpx;
	color: #999;
	font-size: 26rpx;
}
</style>
