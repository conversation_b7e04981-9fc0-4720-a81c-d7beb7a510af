@use '../common/abstracts/variable.scss' as *;
@use '../common/abstracts/_mixin.scss' as *;

.wot-theme-dark {
	@include b(cell) {
		background-color: $wot-dark-background2;
		color: $wot-dark-color;

		@include e(value) {
			color: $wot-dark-color;
		}

		@include e(label) {
			color: $wot-dark-color3;
		}

		@include when(hover) {
			background-color: $wot-dark-background4;
		}

		@include when(border) {
			.wd-cell__wrapper {
				@include halfPixelBorder('top', 0, $wot-dark-border-color);
			}
		}

		:deep(.wd-cell__arrow-right) {
			color: $wot-dark-color;
		}
	}
}

@include b(cell) {
	position: relative;
	padding-left: $wot-cell-padding;
	background-color: $wot-color-white;
	text-decoration: none;
	color: $wot-cell-title-color;
	line-height: $wot-cell-line-height;
	-webkit-tap-highlight-color: transparent;
	box-sizing: border-box;
	width: 100%;
	overflow: hidden;

	@include when(border) {
		.wd-cell__wrapper {
			@include halfPixelBorder('top');
		}
	}

	@include e(wrapper) {
		position: relative;
		display: flex;
		padding: $wot-cell-wrapper-padding $wot-cell-padding $wot-cell-wrapper-padding 0;
		justify-content: space-between;
		align-items: flex-start;
		overflow: hidden;

		@include when(vertical) {
			display: block;

			.wd-cell__right {
				margin-top: $wot-cell-vertical-top;
			}

			.wd-cell__value {
				text-align: left;
			}

			.wd-cell__left {
				margin-right: 0;
			}
		}

		@include when(label) {
			padding: $wot-cell-wrapper-padding-with-label $wot-cell-padding $wot-cell-wrapper-padding-with-label 0;
		}
	}

	@include e(left) {
		position: relative;
		flex: 1;
		display: flex;
		text-align: left;
		font-size: $wot-cell-title-fs;
		box-sizing: border-box;
		margin-right: $wot-cell-padding;

		@include when(required) {
			padding-left: 12px;

			&::after {
				position: absolute;
				content: '*';
				top: 0;
				left: 0;
				font-size: $wot-cell-required-size;
				color: $wot-cell-required-color;
			}
		}
	}

	@include e(right) {
		position: relative;
		flex: 1;
		min-width: 0;
	}

	@include e(title) {
		flex: 1;
		width: 100%;
		font-size: $wot-cell-title-fs;
	}

	@include e(label) {
		margin-top: 2px;
		font-size: $wot-cell-label-fs;
		color: $wot-cell-label-color;
	}

	@include edeep(icon) {
		display: block;
		position: relative;
		margin-right: $wot-cell-icon-right;
		font-size: $wot-cell-icon-size;
		height: $wot-cell-line-height;
		line-height: $wot-cell-line-height;
	}

	@include e(body) {
		display: flex;
		min-width: 0;
	}

	@include e(value) {
		position: relative;
		flex: 1;
		font-size: $wot-cell-value-fs;
		color: $wot-cell-value-color;
		vertical-align: middle;

		@include m(left) {
			text-align: left;
		}

		@include m(right) {
			text-align: right;
		}

		@include m(ellipsis) {
			@include lineEllipsis;
			min-width: 0;
		}
	}

	@include edeep(arrow-right) {
		display: block;
		margin-left: 8px;
		width: $wot-cell-arrow-size;
		font-size: $wot-cell-arrow-size;
		color: $wot-cell-arrow-color;
		height: $wot-cell-line-height;
		line-height: $wot-cell-line-height;
	}

	@include e(error-message) {
		color: $wot-form-item-error-message-color;
		font-size: $wot-form-item-error-message-font-size;
		line-height: $wot-form-item-error-message-line-height;
		text-align: left;
		vertical-align: middle;
	}

	@include when(link) {
		-webkit-tap-highlight-color: $wot-cell-tap-bg;
	}

	@include when(hover) {
		background-color: $wot-cell-tap-bg;
	}

	@include when(large) {
		.wd-cell__title {
			font-size: $wot-cell-title-fs-large;
		}

		.wd-cell__wrapper {
			padding-top: $wot-cell-wrapper-padding-large;
			padding-bottom: $wot-cell-wrapper-padding-large;
		}

		.wd-cell__label {
			font-size: $wot-cell-label-fs-large;
		}

		.wd-cell__value {
			font-size: $wot-cell-value-fs-large;
		}

		:deep(.wd-cell__icon) {
			font-size: $wot-cell-icon-size-large;
		}
	}

	@include when(center) {
		.wd-cell__wrapper {
			align-items: center;
		}
	}
}
