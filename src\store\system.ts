import { appconfig } from '@/config/appconfig';
import { encryptPassword } from '@/util/crypto';

// @ts-ignore
import { TextEncoder } from 'text-decoding';

const FORM_URLENCODED = 'application/x-www-form-urlencoded';
const KEY_LOCKED_USERNAME = 'diw-locked-username';
// 新增持久化存储的键名
const KEY_SESSION_STATE = 'diw-session-state';

export const useSystemStore = defineStore('system', () => {
	const metrics = ref({
		safeArea: {
			left: 0,
			top: 0,
			width: 0,
			height: 0,
		},
		hasMenuButton: false,
		menuButton: {
			left: 0,
			top: 0,
			width: 0,
			height: 0,
		},
	});

	let updateSystemInfoCalled = false;

	function updateSystemInfo() {
		const si = uni.getSystemInfoSync();
		if (si.safeArea) {
			metrics.value.safeArea.left = si.safeArea.left;
			metrics.value.safeArea.top = si.safeArea.top;
			metrics.value.safeArea.width = si.safeArea.width;
			metrics.value.safeArea.height = si.safeArea.height;
		}

		// #ifdef MP-WEIXIN
		const mb = uni.getMenuButtonBoundingClientRect();
		metrics.value.hasMenuButton = true;
		metrics.value.menuButton.left = mb.left;
		metrics.value.menuButton.top = mb.top;
		metrics.value.menuButton.width = mb.width;
		metrics.value.menuButton.height = mb.height;
		// #endif
	}

	onMounted(() => {
		updateSystemInfo();
	});

	onReady(() => {
		updateSystemInfo();
	});

	onUpdated(() => {
		if (!updateSystemInfoCalled) {
			updateSystemInfoCalled = true;
			setTimeout(() => {
				updateSystemInfo();
			});
		}
	});

	let state = {
		token: '',
		tenantId: '',
		refreshToken: '',
	};

	const sessionInfo = ref<SessionInfo | null>(null);
	const hasSession = ref(false);

	const enc = new TextEncoder();
	const basic = `${appconfig.client.id}:${appconfig.client.secret}`;
	const loginAuthorization = `Basic ${uni.arrayBufferToBase64(enc.encode(basic))}`;
	const basic2 = `${appconfig.client2.id}:${appconfig.client2.secret}`;
	const loginAuthorization2 = `Basic ${uni.arrayBufferToBase64(enc.encode(basic2))}`;

	// 新增：保存登录状态到本地存储
	async function saveSessionState() {
		if (state.token && state.tenantId && state.refreshToken) {
			await uni.setStorage({
				key: KEY_SESSION_STATE,
				data: {
					token: state.token,
					tenantId: state.tenantId,
					refreshToken: state.refreshToken,
				},
			});
		}
	}

	// 新增：从本地存储恢复登录状态
	async function restoreSessionState() {
		try {
			const d = await uni.getStorage({ key: KEY_SESSION_STATE });
			if (d && d.data) {
				const sessionData = d.data;
				if (sessionData.token && sessionData.tenantId && sessionData.refreshToken) {
					state.token = sessionData.token;
					state.tenantId = sessionData.tenantId;
					state.refreshToken = sessionData.refreshToken;
					return true;
				}
			}
		} catch (err) {
			console.log('恢复登录状态失败:', err);
		}
		return false;
	}

	// 新增：检验登录状态是否有效
	async function validateSessionState(): Promise<boolean> {
		if (!state.token || !state.tenantId || !state.refreshToken) {
			return false;
		}

		try {
			// 检查token是否有效
			const tokenCheck = await checkToken();
			if (tokenCheck) {
				// token有效，构建会话信息
				hasSession.value = true;
				await buildSessionInfo();
				return true;
			}
		} catch (error: any) {
			// token无效，尝试使用refreshToken刷新
			try {
				await refreshToken();
				hasSession.value = true;
				await buildSessionInfo();
				await saveSessionState(); // 保存新的token
				return true;
			} catch (refreshError) {
				console.log('刷新token失败:', refreshError);
				// 刷新失败，清除本地存储的登录状态
				await clearSessionState();
				return false;
			}
		}
		return false;
	}

	// 新增：清除本地存储的登录状态
	async function clearSessionState() {
		try {
			await uni.removeStorage({ key: KEY_SESSION_STATE });
		} catch (err) {
			console.log('清除登录状态失败:', err);
		}
	}

	function apiGet(options: ApiGetOptions | string) {
		if (typeof options === 'string') {
			return apiRequest({ url: options });
		}

		return apiRequest(Object.assign({}, options, { method: 'GET' } as ApiRequestOptions));
	}

	function apiPost(options: ApiPostOptions) {
		return apiRequest(Object.assign({}, options, { method: 'POST' } as ApiRequestOptions));
	}

	function apiPut(options: ApiPutOptions) {
		return apiRequest(Object.assign({}, options, { method: 'PUT' } as ApiRequestOptions));
	}

	function apiDelete(options: ApiDeleteOptions) {
		return apiRequest(Object.assign({}, options, { method: 'DELETE' } as ApiRequestOptions));
	}

	function apiMapUrl(url: string, params?: Record<string, any>) {
		if (!url.startsWith('http://') && !url.startsWith('https://')) {
			if (!url.startsWith('/')) {
				url = '/api/' + url;
			}

			// #ifdef APP || MP-WEIXIN
			url = appconfig.apiBaseUrl + url;
			// #endif
		}

		if (params) {
			const ls: string[] = [];
			for (const key of Object.keys(params)) {
				let s = encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
				ls.push(s);
			}
			const qp = ls.join('&');
			if (qp) {
				url = `${url}?${qp}`;
			}
		}

		return url;
	}

	function apiMapFileUrl(fileId: string) {
		return apiMapUrl('admin/sys-file/fileRedirectUrl?id=' + encodeURIComponent(fileId));
	}

	async function apiDownload(options: ApiDownloadOptions) {
		let url = options.url;

		if (!url.startsWith('http://') && !url.startsWith('https://')) {
			if (!url.startsWith('/')) {
				url = '/api/' + url;
			}

			// #ifdef APP || MP-WEIXIN
			url = appconfig.apiBaseUrl + url;
			// #endif
		}

		if (options.params) {
			const ls = [];
			for (const key of Object.keys(options.params)) {
				const value = options.params[key];
				if (Array.isArray(value)) {
					for (const el of value) {
						ls.push(`${encodeURIComponent(key)}=${encodeURIComponent(el)}`);
					}
				} else if (value !== null && value !== undefined) {
					ls.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
				}
			}

			const query = ls.join('&');
			if (url.indexOf('?') >= 0) {
				url = `${url}&${query}`;
			} else {
				url = `${url}?${query}`;
			}
		}

		const header: Record<string, any> = {};

		if (!('Authorization' in header)) {
			if (state.token) {
				header['Authorization'] = 'Bearer ' + state.token;
			}
		}

		if (state.tenantId) {
			header['Tenant-Id'] = state.tenantId;
		} else {
			header['Tenant-Id'] = 1;
		}

		if (__CONFIG_IS_DEV_MODE__) {
			await checkThrow();
		}

		const d = (await uni.downloadFile({ url, header })) as any;
		if (d.statusCode === 200) {
			return d.tempFilePath;
		} else {
			let msg = '未知错误';

			if (d.data && d.data.ok === false) {
				if (typeof d.data.msg === 'string') {
					msg = d.data.msg;
				}
			}

			const err = new Error(msg);

			if (d.statusCode === 424) {
				// 凭证已过期
				clearSession();

				const anyErr: any = err;
				anyErr.$noSession = true;
			}

			throw err;
		}
	}

	async function apiUpload(options: ApiUploadOptions) {
		let url = options.url;

		if (!url.startsWith('http://') && !url.startsWith('https://')) {
			if (!url.startsWith('/')) {
				url = '/api/' + url;
			}

			// #ifdef APP || MP-WEIXIN
			url = appconfig.apiBaseUrl + url;
			// #endif
		}

		const header: Record<string, any> = {};

		if (!('Authorization' in header)) {
			if (state.token) {
				header['Authorization'] = 'Bearer ' + state.token;
			}
		}

		if (state.tenantId) {
			header['Tenant-Id'] = state.tenantId;
		} else {
			header['Tenant-Id'] = 1;
		}

		let useFiles = true;

		let files: any[] = [];
		/* #ifdef H5 */
		files = options.files.map((e: any) => {
			return { file: e };
		});
		/* #endif */

		/* #ifdef APP-PLUS */
		files = options.files.map((e: any) => {
			return { uri: e.path || e.tempFilePath };
		});
		/* #endif */

		/* #ifdef MP-WEIXIN */
		useFiles = false;
		/* #endif */

		if (__CONFIG_IS_DEV_MODE__) {
			await checkThrow();
		}

		const d = (await uni.uploadFile({
			url,
			files: useFiles ? files : undefined,
			/* #ifdef MP-WEIXIN */
			filePath: options.files[0].path,
			name: 'file',
			/* #endif */
			formData: options.formData || {},
			header,
		})) as any;

		if (d.statusCode === 200) {
			if (typeof d.data === 'string') {
				d.data = JSON.parse(d.data);
			}
			if (options.parse === 0) {
				if (d.data && d.data.ok !== false) {
					return d.data;
				}
			} else {
				if (d.data && d.data.ok === true) {
					return d.data.data;
				}
			}

			throw new Error('接口错误');
		} else {
			let msg = '未知错误';

			if (d.data && d.data.ok === false) {
				if (typeof d.data.msg === 'string') {
					msg = d.data.msg;
				}
			}

			const err = new Error(msg);

			if (d.statusCode === 424) {
				// 凭证已过期
				clearSession();

				const anyErr: any = err;
				anyErr.$noSession = true;
			}

			throw err;
		}
	}

	async function apiRequest(options: ApiRequestOptions) {
		let url = options.url;

		if (!url.startsWith('http://') && !url.startsWith('https://')) {
			if (!url.startsWith('/')) {
				url = '/api/' + url;
			}

			// #ifdef APP || MP-WEIXIN
			url = appconfig.apiBaseUrl + url;
			// #endif
		}

		if (options.params) {
			const ls = [];
			for (const key of Object.keys(options.params)) {
				const value = options.params[key];
				if (Array.isArray(value)) {
					for (const el of value) {
						ls.push(`${encodeURIComponent(key)}=${encodeURIComponent(el)}`);
					}
				} else if (value !== null && value !== undefined) {
					ls.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
				}
			}

			const query = ls.join('&');
			if (url.indexOf('?') >= 0) {
				url = `${url}&${query}`;
			} else {
				url = `${url}?${query}`;
			}
		}

		const opt: ApiRequestOptions = { url, method: 'GET' };

		if (typeof options.method === 'string') {
			opt.method = options.method;
		}

		if (options.data) {
			opt.data = options.data;
		}

		if (options.header) {
			opt.header = options.header;
		} else {
			opt.header = {};
		}

		if (!('Content-Type' in opt.header)) {
			opt.header['Content-Type'] = 'application/json';
		}

		if (!('Authorization' in opt.header)) {
			if (state.token) {
				opt.header['Authorization'] = 'Bearer ' + state.token;
			}
		}

		if (state.tenantId) {
			opt.header['Tenant-Id'] = state.tenantId;
		} else {
			opt.header['Tenant-Id'] = '1';
		}

		if (__CONFIG_IS_DEV_MODE__) {
			await checkThrow();
		}

		const d = (await uni.request(opt)) as any;
		if (d.statusCode === 200) {
			if (options.parse === 0) {
				if (d.data) {
					if (d.data.ok !== false) {
						return d.data;
					}

					if (typeof d.data.msg === 'string') {
						throw new Error(d.data.msg);
					}
				}
			} else if (options.parse === 2) {
				return d;
			} else {
				if (d.data) {
					if (d.data.ok === true) {
						return d.data.data;
					}

					if (typeof d.data.msg === 'string') {
						throw new Error(d.data.msg);
					}
				}
			}

			throw new Error('接口错误');
		} else {
			let msg = '未知错误';

			if (d.data && d.data.ok === false) {
				if (typeof d.data.msg === 'string') {
					msg = d.data.msg;
				}
			}

			const err = new Error(msg);

			if (d.statusCode === 424) {
				// 凭证已过期
				clearSession();

				const anyErr: any = err;
				anyErr.$noSession = true;
			}

			throw err;
		}
	}

	async function login(username: string, password: string): Promise<SessionInfo> {
		const d = await apiRequest({
			method: 'POST',
			url: 'auth/oauth2/token',
			params: { grant_type: 'password', username, scope: appconfig.client.scope, randomStr: 'blockPuzzle', code: '' },
			header: {
				'Content-Type': FORM_URLENCODED,
				Authorization: loginAuthorization,
				'Enc-Flag': 'false',
				skipToken: 'true',
			},
			data: {
				password: encryptPassword(password),
			},
			parse: 0,
		});

		state.token = d.access_token;
		state.tenantId = d.tenant_id;
		state.refreshToken = d.refresh_token;
		hasSession.value = true;

		await uni.setStorage({ key: KEY_LOCKED_USERNAME, data: username });
		// 保存登录状态到本地存储
		await saveSessionState();

		return await buildSessionInfo();
	}

	async function loginMobile(mobile: string, code: string): Promise<SessionInfo> {
		const d = await apiRequest({
			method: 'POST',
			url: 'auth/oauth2/token',
			params: { grant_type: 'mobile' },
			header: {
				'Content-Type': FORM_URLENCODED,
				Authorization: loginAuthorization2,
			},
			data: {
				mobile: 'SMS@' + mobile,
				code,
				scope: appconfig.client2.scope,
			},
			parse: 0,
		});
		state.token = d.access_token;
		state.tenantId = d.tenant_id;
		state.refreshToken = d.refresh_token;
		hasSession.value = true;

		await uni.setStorage({ key: KEY_LOCKED_USERNAME, data: mobile });
		// 保存登录状态到本地存储
		await saveSessionState();

		return await buildSessionInfo();
	}

	async function refreshToken() {
		const d = await apiRequest({
			method: 'POST',
			url: 'auth/oauth2/token',
			header: {
				Authorization: loginAuthorization,
			},
			data: {
				grant_type: 'refresh_token',
				refresh_token: state.refreshToken,
				scope: appconfig.client.scope,
			},
			parse: 0,
		});
		state.token = d.access_token;
		state.refreshToken = d.refresh_token;
	}

	async function checkToken() {
		const d = await apiGet({
			url: 'auth/token/check_token',
			params: {
				token: state.token,
			},
			header: {
				Authorization: loginAuthorization,
			},
		});

		return d;
	}

	async function logout() {
		if (hasSession.value) {
			await apiRequest({
				method: 'DELETE',
				header: {
					Authorization: 'Bearer ' + state.token,
				},
				url: '/api/auth/token/logout',
			});

			await uni.removeStorage({ key: KEY_LOCKED_USERNAME });
			// 清除本地存储的登录状态
			await clearSessionState();
			clearSession();
		}
	}

	async function buildSessionInfo() {
		const u = await apiGet('admin/user/info');
		hasSession.value = true;

		let avatarUrl = '';
		avatarUrl = u.oaAvatar || '';

		sessionInfo.value = {
			avatarUrl,
			accessToken: state.token,
			userId: u.userId,
			tenantId: u.tenantId,
			username: u.username,
			permissions: u.permissions || [],

			// 用户基本信息
			name: u.name || u.nickname || u.username,
			nickname: u.nickname || u.name || u.username,
			phone: u.phone || '',
			email: u.email,
			syncJobNumber: u.syncJobNumber || '',

			// 工厂信息
			factoryCode: u.factoryCode || '',
			factoryName: u.factoryName || '',

			// 部门列表
			deptList: u.deptList || [],

			// 角色列表
			roleList: u.roleList || [],

			// 岗位列表
			postList: u.postList || [],

			userInfo: u,
		};

		return sessionInfo.value;
	}

	async function getLockedUsername() {
		try {
			const d = await uni.getStorage({ key: KEY_LOCKED_USERNAME });
			if (d && typeof d.data === 'string') {
				return d.data;
			}
		} catch (err) {}

		return null;
	}

	function clearSession() {
		state.token = '';
		state.tenantId = '';
		state.refreshToken = '';
		hasSession.value = false;
		sessionInfo.value = null;
	}

	let simNetError = 0;
	let simDelay = 0;

	async function checkThrow() {
		if (__CONFIG_IS_DEV_MODE__) {
			if (simDelay > 0) {
				await new Promise((resolve) => {
					setTimeout(resolve, Math.random() * simDelay);
				});
			}

			if (simNetError === 1) {
				simNetError = 0;
				throw new Error('模拟网络错误');
			}

			if (simNetError === 2) {
				throw new Error('模拟网络错误');
			}
		}
	}

	function hack(m: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7) {
		if (__CONFIG_IS_DEV_MODE__) {
			switch (m) {
				case 0:
					// 一次性网络错误
					simNetError = 1;
					break;

				case 1:
					// 持续网络错误
					simNetError = 2;
					break;

				case 2:
					// 关闭网络错误模拟
					simNetError = 0;
					break;

				case 3:
					simDelay = 5000;
					break;

				case 4:
					simDelay = 0;
					break;

				case 5:
					return simNetError === 2;

				case 6:
					return simDelay > 0;

				case 7:
					return checkThrow();
			}
		}
	}

	return {
		metrics,
		hasSession,
		sessionInfo,
		buildSessionInfo,
		login,
		loginMobile,
		refreshToken,
		checkToken,
		getLockedUsername,
		logout,
		apiMapUrl,
		apiMapFileUrl,
		apiUpload,
		apiDownload,
		apiRequest,
		apiGet,
		apiPost,
		apiPut,
		apiDelete,
		hack,
		// 新增的导出函数
		saveSessionState,
		restoreSessionState,
		validateSessionState,
		clearSessionState,
	};
});
