<template>
	<DIWAppPage mode="0" title="选择合同">
		<DIWProtect>
			<DIWRoot>
				<OrderList @select="finishPage" :orderStatus="order_status" />
			</DIWRoot>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
import OrderList from './components/orderList.vue';

const { usePageResult, mapDict } = useFramework();
const { finishPage } = usePageResult();

const { order_status } = mapDict('order_status');
</script>
