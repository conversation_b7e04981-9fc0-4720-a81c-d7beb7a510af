@use './../../common/abstracts/_mixin.scss' as *;
@use './../../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(key) {
		background: $wot-dark-background2;
		color: $wot-dark-color;

		&:active {
			background-color: $wot-dark-background4;
		}

		@include m(active) {
			background-color: $wot-dark-background4;
		}
	}
}

.wd-key-wrapper {
	position: relative;
	flex: 1;
	flex-basis: 33%;
	box-sizing: border-box;
	padding: 0 6px 6px 0;

	@include m(wider) {
		flex-basis: 66%;
	}
}

@include b(key) {
	display: flex;
	align-items: center;
	justify-content: center;
	height: $wot-keyboard-key-height;
	font-size: $wot-keyboard-key-font-size;
	line-height: 1.5;
	background: $wot-keyboard-key-background;
	border-radius: $wot-keyboard-key-border-radius;

	&:active {
		background-color: $wot-keyboard-key-active-color;
	}

	@include m(large) {
		position: absolute;
		top: 0;
		right: 6px;
		bottom: 6px;
		left: 0;
		height: auto;
	}

	@include m(delete, close) {
		font-size: $wot-keyboard-delete-font-size;
	}

	@include m(active) {
		background-color: $wot-keyboard-key-active-color;
	}

	@include m(close) {
		color: $wot-keyboard-button-text-color;
		background: $wot-keyboard-button-background;

		&:active {
			background: $wot-keyboard-button-background;
			opacity: $wot-keyboard-button-active-opacity;
		}
	}

	@include edeep(loading-icon) {
		color: $wot-keyboard-button-text-color;
	}

	@include edeep(icon) {
		font-size: $wot-keyboard-icon-size;
	}
}
