function replaceHost(s: string) {
	if (!__SHIPPING__) {
		return s.replace('mes.diwyun.cn', 'testmes.diwyun.cn');
	}
	return s;
}

export const appconfig = {
	passwordEncryptionKey: 'diwxdiwxdiwxdiwx',
	client: {
		id: 'app',
		secret: 'app',
		scope: 'server',
	},
	client2: {
		id: 'app',
		secret: 'app',
		scope: 'server',
	},
	origin: replaceHost(import.meta.env.VITE_CONFIG_ORIGIN),
	apiBaseUrl: replaceHost(import.meta.env.VITE_CONFIG_API_BASE_URL),
	imUrl: replaceHost(import.meta.env.VITE_CONFIG_IM_URL),
	hubUrl: replaceHost(import.meta.env.VITE_CONFIG_HUB_URL),
	docUrl: replaceHost(import.meta.env.VITE_CONFIG_DOC_ROOT),
};
