@use '../../common/abstracts/variable' as *;
@use '../../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(year) {
		@include e(title) {
			color: $wot-dark-color;
		}

		@include e(months) {
			color: $wot-dark-color;
		}

		@include e(month) {
			@include when(disabled) {
				.wd-year__month-text {
					color: $wot-dark-color-gray;
				}
			}
		}
	}
}

@include b(year) {
	@include e(title) {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 45px;
		font-size: $wot-calendar-panel-title-fs;
		color: $wot-calendar-panel-title-color;
	}

	@include e(months) {
		display: flex;
		flex-wrap: wrap;
		font-size: $wot-calendar-day-fs;
		color: $wot-calendar-day-color;
	}

	@include e(month) {
		position: relative;
		width: 25%;
		height: $wot-calendar-day-height;
		line-height: $wot-calendar-day-height;
		text-align: center;
		margin-bottom: $wot-calendar-item-margin-bottom;

		@include when(disabled) {
			.wd-year__month-text {
				color: $wot-calendar-disabled-color;
			}
		}

		@include when(current) {
			color: $wot-calendar-active-color;
		}

		@include when(selected) {
			color: #fff;

			.wd-year__month-text {
				border-radius: $wot-calendar-active-border;
				background: $wot-calendar-active-color;
			}
		}

		@include when(middle) {
			background: $wot-calendar-range-color;
		}

		@include when(start) {
			color: $wot-calendar-selected-color;

			&::after {
				position: absolute;
				top: 0;
				right: 0;
				left: 50%;
				bottom: 0;
				content: '';
				background: $wot-calendar-range-color;
			}

			.wd-year__month-text {
				background: $wot-calendar-active-color;
				border-radius: $wot-calendar-active-border 0 0 $wot-calendar-active-border;
			}

			&.is-without-end::after {
				display: none;
			}
		}

		@include when(end) {
			color: $wot-calendar-selected-color;

			&::after {
				position: absolute;
				top: 0;
				left: 0;
				right: 50%;
				bottom: 0;
				content: '';
				background: $wot-calendar-range-color;
			}

			.wd-year__month-text {
				background: $wot-calendar-active-color;
				border-radius: 0 $wot-calendar-active-border $wot-calendar-active-border 0;
			}
		}

		@include when(same) {
			color: $wot-calendar-selected-color;

			.wd-year__month-text {
				background: $wot-calendar-active-color;
				border-radius: $wot-calendar-active-border;
			}
		}
		@include when(last-row) {
			margin-bottom: 0;
		}
	}

	@include e(month-text) {
		width: $wot-calendar-month-width;
		margin: 0 auto;
		text-align: center;
	}

	@include e(month-top) {
		position: absolute;
		top: 10px;
		left: 0;
		right: 0;
		line-height: 1.1;
		font-size: $wot-calendar-info-fs;
		text-align: center;
	}

	@include e(month-bottom) {
		position: absolute;
		bottom: 10px;
		left: 0;
		right: 0;
		line-height: 1.1;
		font-size: $wot-calendar-info-fs;
		text-align: center;
	}
}
