<template>
	<DIWScrollView>
		<!-- 搜索头部 -->
		<view class="order-header">
			<wd-search v-model="searchModel.searchText" placeholder="搜索合同" @change="handleSearch" @clear="handleSearch" />
		</view>

		<!-- 合同列表 -->
		<DIWListView ref="listView" :meta="listViewMeta">
			<template #default="{ data }">
				<view class="order-item">
					<view class="order-card">
						<!-- 状态和时间 -->
						<view class="order-header-info">
							<wd-tag :type="getStatusTagType(data.orderStatus || data.status)" size="small">
								{{ getStatusText(data.orderStatus || data.status) }}
							</wd-tag>
							<text class="order-time">{{ formatOrderTime(data.createTime) }}</text>
						</view>

						<!-- 合同号 -->
						<view class="order-no-row">
							<text class="order-no">{{ data.orderNo }}</text>
						</view>

						<!-- 卖家信息 -->
						<view class="seller-row">
							<text class="seller-name">{{ data.sellerName || '未知卖家' }}</text>
						</view>

						<!-- 底部信息 -->
						<view class="order-bottom">
							<text class="product-count">{{ data.productCount || data.quantity || 1 }}件商品</text>
							<view class="price-action">
								<text class="price">¥{{ formatPrice(data.totalAmount) }}</text>
								<wd-button @click.stop="handleSend(data)" custom-class="send-btn" type="primary" size="small"> 发送 </wd-button>
							</view>
						</view>
					</view>
				</view>
			</template>

			<template #empty>
				<view class="empty-state">
					<wd-icon name="shopping-cart" size="120rpx" color="#ccc" />
					<text class="empty-text">{{ getEmptyDescription() }}</text>
					<wd-button v-if="loadError" type="primary" size="small" class="mt-3" @click="handleReload">重新加载</wd-button>
				</view>
			</template>
		</DIWListView>
	</DIWScrollView>
</template>

<script setup lang="ts">
import { debounce } from '@/ui/wot-design-uni/components/common/util';

const props = defineProps<{
	orderStatus?: Array<{ label: string; value: string }>;
}>();

const emit = defineEmits<{ select: [Record<string, any>] }>();

const { apiGet, sessionInfo } = useFramework();

// 状态变量
const loadError = ref(false);
const listView = ref();

// 搜索参数
const searchModel = reactive({
	searchText: '',
	current: 1,
	size: 20,
});

// 处理搜索
const handleSearch = debounce(() => {
	listView.value?.reload();
}, 300);

// DIWListView配置
const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(trackInfo) {
			if (!sessionInfo.value) {
				return { hasMore: false, items: [], trackInfo: 1 };
			}

			const size1 = 20;
			const size2 = 10;

			const isReload = trackInfo === undefined;
			let current = isReload ? 1 : trackInfo;
			let size = isReload ? size1 : size2;

			try {
				// 构建搜索参数
				const params: Record<string, any> = {
					current,
					size,
					descs: 'create_time', // 按创建时间倒序
				};

				// 如果有搜索文本，同时搜索合同号和卖家名称
				if (searchModel.searchText.trim()) {
					params.orderNo = searchModel.searchText.trim();
					// 可以根据需要添加更多搜索字段
					// params.sellerName = searchModel.searchText.trim();
				}

				const response = await apiGet({
					url: 'order/order/page',
					params,
				});

				const items = response.records || [];
				loadError.value = false;

				if (isReload) {
					return { hasMore: response.total > size, items, trackInfo: 1 + size1 / size2 };
				}

				return { hasMore: response.total > current * size, items, trackInfo: current + 1 };
			} catch (error) {
				console.error('加载合同列表失败:', error);
				loadError.value = true;
				return { hasMore: false, items: [], trackInfo: current };
			}
		},
	});
});

// 获取状态标签类型
function getStatusTagType(status: number | string): 'primary' | 'success' | 'warning' | 'danger' {
	const statusStr = String(status);
	// 根据实际订单状态定义类型
	switch (statusStr) {
		case '0': // 待审核
		case '1': // 待复核
		case '2': // 待签约
			return 'warning';
		case '3': // 已签署
		case '4': // 交易完成
			return 'success';
		case '5': // 交易关闭
		case '7': // 已失效
		case '8': // 已删除
			return 'danger';
		case '6': // 待确认
			return 'primary';
		default:
			return 'primary';
	}
}

// 获取状态文本
function getStatusText(status: number | string) {
	// 优先使用传入的状态字典
	if (props.orderStatus) {
		const statusOption = props.orderStatus.find((option) => option.value === String(status));
		return statusOption?.label || '未知状态';
	}

	// 如果没有传入状态字典，使用默认映射（保持向后兼容）
	const statusMap: Record<string, string> = {
		'0': '待审核',
		'1': '待复核',
		'2': '待签约',
		'3': '已签署',
		'4': '交易完成',
		'5': '交易关闭',
		'6': '待确认',
		'7': '已失效',
		'8': '已删除',
	};
	return statusMap[String(status)] || '未知状态';
}

// 格式化价格
function formatPrice(price: number | string) {
	if (typeof price === 'undefined' || price === null) return '0.00';
	return Number(price).toFixed(2);
}

// 格式化合同时间 - 参考PC端格式
function formatOrderTime(time: string | Date) {
	if (!time) return '';
	try {
		const date = new Date(time);
		return formatDateTime(date, 'MM-dd HH:mm');
	} catch (e) {
		return String(time);
	}
}

// 获取空状态描述
function getEmptyDescription() {
	if (!sessionInfo.value) return '请先登录查看您的合同';
	if (loadError.value) return '加载合同失败';
	if (searchModel.searchText) return '没有找到匹配的合同';
	return '暂无合同数据';
}

// 重新加载
function handleReload() {
	loadError.value = false;
	listView.value?.reload();
}

// 发送合同
function handleSend(order: Record<string, any>) {
	console.log('选择合同:', order);
	emit('select', order);
}
</script>

<style scoped lang="scss">
.order-header {
	background-color: #fff;
	border-bottom: 1rpx solid #ebeef5;
	position: sticky;
	top: 0;
	z-index: 10;
}

.order-item {
	background-color: #fff;
	margin: 16rpx 24rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
	cursor: pointer;

	&:hover {
		background-color: #f8f9fa;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
	}
}

.order-card {
	padding: 24rpx;
}

.order-header-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.order-time {
	font-size: 24rpx;
	color: #999;
}

.order-no-row {
	margin-bottom: 12rpx;
}

.order-no {
	font-size: 28rpx;
	font-weight: 600;
	color: #409eff;
}

.seller-row {
	margin-bottom: 16rpx;
}

.seller-name {
	font-size: 28rpx;
	color: #333;
}

.order-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.product-count {
	font-size: 24rpx;
	color: #999;
}

.price-action {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.price {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff5000;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 32rpx;
	gap: 32rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #909399;
}
:deep(.send-btn) {
	background-color: #ff5000 !important;
	border: none !important;
	border-radius: 20rpx !important;
	padding: 0 32rpx !important;
	height: 60rpx !important;
	font-size: 24rpx !important;
	color: #ffffff !important;
}
</style>
