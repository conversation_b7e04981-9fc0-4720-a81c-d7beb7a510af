<template>
	<DIWAppPage title="%notificationDetail%">
		<view class="detail-page" v-if="!loading">
			<!-- 消息标题 -->
			<view class="detail-header">
				<text class="detail-title">{{ notification.title }}</text>
				<view class="detail-meta">
					<text class="detail-date">{{ notification.createTime || '-' }}</text>
					<view class="detail-status" :class="{ read: notification.isRead }">
						{{ notification.isRead ? t('read') : t('unread') }}
					</view>
				</view>
			</view>

			<!-- 消息内容 -->
			<view class="detail-content">
				<text class="content-text">{{ notification.content || notification.title }}</text>
			</view>

			<!-- 附件 -->
			<view class="detail-attachments" v-if="notification.attachments && notification.attachments.length > 0">
				<text class="attachments-title">{{ t('attachments') }}</text>
				<view class="attachments-list">
					<view v-for="(file, index) in notification.attachments" :key="index" class="attachment-item" @click="handleDownload(file)">
						<wd-icon name="document" size="32rpx" color="#0082f0" />
						<text class="attachment-name">{{ file.name }}</text>
						<wd-icon name="download" size="24rpx" color="#86909C" />
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="detail-actions" v-if="!notification.isRead">
				<wd-button type="primary" @click="markAsRead">{{ t('markAsRead') }}</wd-button>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<wd-loading color="#0082f0" size="36px" />
			<text class="loading-text">{{ t('loading') }}</text>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();

// 使用框架功能
const { apiGet, apiPost, showMessage, protect, usePageQuery } = useFramework();

// 页面参数
const pq = usePageQuery();

// 响应式数据
const loading = ref(true);
const notification = ref<any>({});

// 页面加载时获取详情
onMounted(() => {
	loadNotificationDetail();
});

// 加载消息详情
async function loadNotificationDetail() {
	if (!pq.value?.id) {
		showMessage('参数错误');
		return;
	}

	try {
		loading.value = true;
		const res = await apiGet({ url: `system/notification/${pq.value.id}` });
		if (res) {
			notification.value = res;
		} else {
			// 使用模拟数据
			notification.value = getMockNotification(pq.value.id);
		}
	} catch (error) {
		console.error('加载消息详情失败', error);
		// 使用模拟数据
		notification.value = getMockNotification(pq.value.id);
	} finally {
		loading.value = false;
	}
}

// 模拟消息详情数据
function getMockNotification(id: string) {
	const mockData: Record<string, any> = {
		'1': {
			id: 1,
			title: '关于下发《OMS项目模块化开发与考核管理办法》的通知',
			content:
				'各部门：\n\n为进一步规范OMS项目模块化开发工作，提高开发效率和代码质量，现制定《OMS项目模块化开发与考核管理办法》，请各相关部门认真学习贯彻执行。\n\n具体内容如下：\n\n一、开发规范要求\n1. 严格按照模块化设计原则进行开发\n2. 代码需要通过质量检查\n3. 及时提交开发文档\n\n二、考核标准\n1. 代码质量评分\n2. 功能完成度评估\n3. 团队协作能力\n\n请各部门负责人组织学习，确保相关人员充分理解并严格执行。\n\n特此通知。\n\n数智世界集团\n2025年7月12日',
			createTime: '2025.7.12',
			isRead: false,
			type: 'group',
			attachments: [{ name: 'OMS项目模块化开发与考核管理办法.pdf', url: '/files/oms-management.pdf' }],
		},
	};

	return (
		mockData[id] || {
			id,
			title: '消息详情',
			content: '消息内容加载中...',
			createTime: '2025.7.01',
			isRead: false,
			type: 'all',
			attachments: [],
		}
	);
}

// 标记为已读
async function markAsRead() {
	protect(async () => {
		try {
			await apiPost({ url: `system/notification/read/${notification.value.id}` });
			notification.value.isRead = true;
			showMessage(t('markSuccessfully'));
		} catch (error) {
			console.error('标记已读失败', error);
			// 模拟成功
			notification.value.isRead = true;
			showMessage(t('markSuccessfully'));
		}
	});
}

// 下载附件
function handleDownload(file: any) {
	protect(async () => {
		try {
			// 这里应该调用下载API
			showMessage(`开始下载：${file.name}`);
		} catch (error) {
			console.error('下载失败', error);
			showMessage('下载失败');
		}
	});
}
</script>

<style lang="scss" scoped>
.detail-page {
	background-color: #f5f7fa;
	min-height: 100vh;
}

/* 头部样式 */
.detail-header {
	background-color: #ffffff;
	padding: 32rpx;
	margin-bottom: 24rpx;
}

.detail-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #1d2129;
	line-height: 1.5;
	margin-bottom: 24rpx;
	display: block;
}

.detail-meta {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.detail-date {
	font-size: 26rpx;
	color: #86909c;
}

.detail-status {
	font-size: 24rpx;
	color: #ffffff;
	background-color: #f62323;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;

	&.read {
		background-color: #00b42a;
	}
}

/* 内容样式 */
.detail-content {
	background-color: #ffffff;
	padding: 32rpx;
	margin-bottom: 24rpx;
}

.content-text {
	font-size: 30rpx;
	color: #1d2129;
	line-height: 1.6;
	white-space: pre-wrap;
	word-break: break-word;
}

/* 附件样式 */
.detail-attachments {
	background-color: #ffffff;
	padding: 32rpx;
	margin-bottom: 24rpx;
}

.attachments-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1d2129;
	margin-bottom: 24rpx;
	display: block;
}

.attachments-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.attachment-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background-color: #f7f8fa;
	border-radius: 12rpx;

	&:active {
		background-color: #e8f3ff;
	}
}

.attachment-name {
	flex: 1;
	font-size: 28rpx;
	color: #1d2129;
	margin-left: 16rpx;
	margin-right: 16rpx;
}

/* 操作按钮样式 */
.detail-actions {
	padding: 32rpx;
	margin-bottom: 64rpx;
}

/* 加载状态样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 60vh;
}

.loading-text {
	margin-top: 32rpx;
	font-size: 28rpx;
	color: #86909c;
}
</style>
