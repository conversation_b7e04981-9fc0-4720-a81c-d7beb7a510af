.DS_Store
node_modules
/dist
docker/dist
/mobile/app/dist
/mobile/uni/dist
/mobile/app/dist
unpackage
.hbuilderx

# generated
auto-imports.d.ts
components.d.ts

# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 忽略所有 .cursorrules 文件
# .cursorrules

# 忽略所有 .cursorrules 文件
# **/.cursorrules

# 忽略所有 .cursorignore 文件
# .cursorignore

# 忽略所有 .cursorignore 文件
# **/.cursorignore
*/wot-design-uni/*
# lock
package-lock.json
yarn.lock
# pnpm-lock.yaml
.qodo
.trae
