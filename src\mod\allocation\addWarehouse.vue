<template>
	<DIWAppPage title="添加移库单">
		<DIWScrollView>
			<view class="add-warehouse-container">
				<!-- 基本信息表单 -->
				<view class="form-section">
					<view class="form-row">
						<view class="form-item">
							<text class="form-label required">调拨名称</text>
							<wd-input v-model="formData.allocationName" placeholder="请输入调拨名称" class="form-input" />
						</view>
					</view>

					<view class="form-row">
						<view class="form-item">
							<text class="form-label required">调出仓库</text>
							<picker mode="selector" :range="warehouseList" range-key="name" @change="handleOutWarehouseChange">
								<view class="edit-form-picker">
									<text class="edit-form-picker-text" :class="{ 'edit-form-picker-text-placeholder': !formData.outWarehouseId }">{{
										formData.outWarehouseId ? formData.warehouseName : '请选择入库仓'
									}}</text>
									<view class="edit-form-picker-actions">
										<text class="edit-form-input-clear-icon" v-if="formData.outWarehouseId">✕</text>
										<text class="edit-form-picker-arrow">▼</text>
									</view>
								</view>
							</picker>
						</view>
						<view class="form-item">
							<text class="form-label required">调入仓库</text>
							<wd-select v-model="formData.inWarehouseId" :options="inWarehouseList" placeholder="请选择调入仓库" />
							<picker mode="selector" :range="warehouseList" range-key="name" @change="handleOutWarehouseChange">
								<view class="edit-form-picker">
									<text class="edit-form-picker-text" :class="{ 'edit-form-picker-text-placeholder': !formData.outWarehouseId }">
										{{ formData.outWarehouseId ? formData.warehouseName : '请选择入库仓' }}
									</text>
									<view class="edit-form-picker-actions">
										<text class="edit-form-input-clear-icon" v-if="formData.outWarehouseId">✕</text>
										<text class="edit-form-picker-arrow">▼</text>
									</view>
								</view>
							</picker>
						</view>
					</view>

					<view class="form-row">
						<view class="form-item">
							<text class="form-label required">调拨时间</text>
							<wd-datetime-picker v-model="formData.outAllocationTime" type="datetime" placeholder="请选择调拨时间" class="form-picker" />
						</view>
						<view class="form-item">
							<text class="form-label required">运输方式</text>
							<!-- <wd-select v-model="formData.deliveryMethod" placeholder="请选择运输方式" class="form-select">
                <wd-option v-for="item in deliveryMethodOptions" :key="item.value" :value="item.value" :label="item.label" />
              </wd-select> -->
						</view>
					</view>

					<view class="form-row">
						<view class="form-item">
							<text class="form-label required">预计到达时间</text>
							<wd-datetime-picker v-model="formData.arrivalAllocationTime" type="datetime" placeholder="请选择预计到达时间" class="form-picker" />
						</view>
					</view>
				</view>

				<!-- 商品列表 -->
				<view class="product-section">
					<view class="product-header">
						<view class="product-header-item" style="width: 20%">商品编码</view>
						<view class="product-header-item" style="width: 20%">商品名称</view>
						<view class="product-header-item" style="width: 15%">库存吨数</view>
						<view class="product-header-item" style="width: 15%">工厂</view>
						<view class="product-header-item" style="width: 15%">调拨吨数</view>
						<view class="product-header-item" style="width: 15%">批次</view>
						<view class="product-header-action">
							<wd-button type="primary" size="small" @click="selectProduct">选择商品</wd-button>
						</view>
					</view>

					<view v-if="formData.details.length === 0" class="no-data"> 暂无数据 </view>

					<view v-else class="product-list">
						<view v-for="(item, index) in formData.details" :key="index" class="product-item">
							<view class="product-item-content">
								<view class="product-item-field" style="width: 20%">{{ item.productNo }}</view>
								<view class="product-item-field" style="width: 20%">{{ item.productName }}</view>
								<view class="product-item-field" style="width: 15%">{{ item.currentStock }}</view>
								<view class="product-item-field" style="width: 15%">{{ item.factoryName }}</view>
								<view class="product-item-field" style="width: 15%">
									<wd-input-number v-model="item.allocationNum" :min="0.025" :max="parseFloat(item.currentStock)" :step="0.025" :precision="3" />
								</view>
								<view class="product-item-field" style="width: 15%">{{ item.batchNo }}</view>
								<view class="product-item-action">
									<wd-button size="small" @click="removeProduct(index)">删除</wd-button>
								</view>
							</view>

							<!-- 物流信息 -->
							<view class="logistics-info">
								<view class="logistics-row">
									<view class="logistics-item">
										<text class="logistics-label">司机名称</text>
										<wd-input v-model="item.driverName" placeholder="请输入司机名称" class="logistics-input" />
									</view>
									<view class="logistics-item">
										<text class="logistics-label">电话</text>
										<wd-input v-model="item.phone" placeholder="请输入电话" class="logistics-input" />
									</view>
								</view>
								<view class="logistics-row">
									<view class="logistics-item">
										<text class="logistics-label">司机身份证</text>
										<wd-input v-model="item.driverIdCard" placeholder="请输入司机身份证" class="logistics-input" />
									</view>
									<view class="logistics-item">
										<text class="logistics-label">物流单号</text>
										<wd-input v-model="item.logisticsNo" placeholder="请输入物流单号" class="logistics-input" />
									</view>
								</view>
								<view class="logistics-row">
									<view class="logistics-item">
										<text class="logistics-label">车牌号</text>
										<wd-input v-model="item.licenseNumber" placeholder="请输入车牌号" class="logistics-input" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 备注信息 -->
				<view class="remark-section">
					<text class="remark-label">备注信息</text>
					<wd-textarea v-model="formData.remark" placeholder="请输入备注信息" :maxlength="150" show-word-limit class="remark-input" />
				</view>

				<!-- 底部按钮 -->
				<view class="action-buttons">
					<wd-button type="info" @click="cancel">取消</wd-button>
					<wd-button type="primary" @click="submitForm">确定</wd-button>
				</view>
			</view>
		</DIWScrollView>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { protect, apiGet, apiPost, navigateBack, showMessage, getTenantId } = useFramework();

// 表单数据
const formData = reactive({
	allocationName: '',
	outWarehouseId: '',
	outWarehouseName: '',
	inWarehouseId: '',
	inWarehouseName: '',
	outAllocationTime: '',
	deliveryMethod: '',
	arrivalAllocationTime: '',
	remark: '',
	details: [] as any[],
});

// 仓库列表
const warehouseList = ref<any[]>([]);
const inWarehouseList = ref<any[]>([]);

// 运输方式选项
const deliveryMethodOptions = ref([
	{ value: '1', label: '汽运' },
	{ value: '2', label: '铁运' },
	{ value: '3', label: '空运' },
	{ value: '4', label: '海运' },
]);

// 加载仓库列表
onMounted(() => {
	loadWarehouseList();
});

// 加载仓库列表
function loadWarehouseList() {
	protect(async () => {
		try {
			const res = await apiGet({ url: 'stock/warehouse/page', params: { size: 1000, type: 1, status: 1, manager: getTenantId() } });
			console.log('仓库列表', res.records);
			if (res && res.records) {
				warehouseList.value = res.records.map((e: any) => ({ id: e.id, name: e.name }));
				// 初始化调入仓库列表
				inWarehouseList.value = [...warehouseList.value];
			}
		} catch (error) {
			console.error('加载仓库列表失败', error);
			showMessage('加载仓库列表失败');
		}
	});
}

// 处理调出仓库变更
function handleOutWarehouseChange() {
	// 清空已选商品
	formData.details = [];

	// 更新调入仓库列表（排除当前选中的调出仓库）
	inWarehouseList.value = warehouseList.value.filter((item) => item.id !== formData.outWarehouseId);

	// 如果当前选中的调入仓库与调出仓库相同，则清空调入仓库选择
	if (formData.inWarehouseId === formData.outWarehouseId) {
		formData.inWarehouseId = '';
	}
}

// 选择商品
function selectProduct() {
	if (!formData.outWarehouseId) {
		showMessage('请先选择调出仓库');
		return;
	}

	protect(async () => {
		try {
			// 打开商品选择弹窗
			/* const result = await openDialog('ProductSelectList', {
        warehouseId: formData.outWarehouseId
      }); */
			/* if (result && result.length > 0) {
        // 添加选中的商品到列表
        result.forEach((item: any) => {
          formData.details.push({
            productId: item.productId,
            productNo: item.productNo,
            productName: item.productName,
            currentStock: item.currentStock || 0,
            factoryName: item.factoryName,
            allocationNum: 0,
            batchNo: item.batchNo || '',
            driverName: '',
            phone: '',
            driverIdCard: '',
            logisticsNo: '',
            licenseNumber: ''
          });
        });
      } */
		} catch (error) {
			console.error('选择商品失败', error);
		}
	});
}

// 移除商品
function removeProduct(index: number) {
	formData.details.splice(index, 1);
}

// 表单验证
function validateForm() {
	if (!formData.allocationName) {
		showMessage('请输入调拨名称');
		return false;
	}

	if (!formData.outWarehouseId) {
		showMessage('请选择调出仓库');
		return false;
	}

	if (!formData.inWarehouseId) {
		showMessage('请选择调入仓库');
		return false;
	}

	if (formData.outWarehouseId === formData.inWarehouseId) {
		showMessage('调出仓库和调入仓库不能相同');
		return false;
	}

	if (!formData.outAllocationTime) {
		showMessage('请选择调拨时间');
		return false;
	}

	if (!formData.deliveryMethod) {
		showMessage('请选择运输方式');
		return false;
	}

	if (!formData.arrivalAllocationTime) {
		showMessage('请选择预计到达时间');
		return false;
	}

	if (formData.details.length === 0) {
		showMessage('请选择商品');
		return false;
	}

	// 验证商品信息
	for (let i = 0; i < formData.details.length; i++) {
		const item = formData.details[i];

		if (!item.allocationNum || item.allocationNum <= 0) {
			showMessage(`第${i + 1}个商品的调拨吨数必须大于0`);
			return false;
		}

		if (item.allocationNum > parseFloat(item.currentStock)) {
			showMessage(`第${i + 1}个商品的调拨吨数不能大于库存吨数`);
			return false;
		}

		// 验证调拨吨数是否为0.025的倍数
		const remainder = (item.allocationNum * 1000) % 25;
		if (remainder !== 0) {
			showMessage(`第${i + 1}个商品的调拨吨数必须为0.025的倍数`);
			return false;
		}

		// 验证物流信息
		if (!item.driverName) {
			showMessage(`请输入第${i + 1}个商品的司机名称`);
			return false;
		}

		if (!item.phone) {
			showMessage(`请输入第${i + 1}个商品的电话`);
			return false;
		}

		if (!item.driverIdCard) {
			showMessage(`请输入第${i + 1}个商品的司机身份证`);
			return false;
		}

		if (!item.logisticsNo) {
			showMessage(`请输入第${i + 1}个商品的物流单号`);
			return false;
		}

		if (!item.licenseNumber) {
			showMessage(`请输入第${i + 1}个商品的车牌号`);
			return false;
		}
	}

	return true;
}

// 提交表单
function submitForm() {
	protect(async () => {
		// 表单验证
		if (!validateForm()) {
			return;
		}

		try {
			// 构建提交参数
			const params = {
				allocationName: formData.allocationName,
				outWarehouseId: formData.outWarehouseId,
				inWarehouseId: formData.inWarehouseId,
				outAllocationTime: formData.outAllocationTime,
				deliveryMethod: formData.deliveryMethod,
				arrivalAllocationTime: formData.arrivalAllocationTime,
				remark: formData.remark,
				allocationType: '2', // 移库单类型
				details: formData.details.map((item) => ({
					productId: item.productId,
					allocationNum: item.allocationNum,
					batchNo: item.batchNo,
					driverName: item.driverName,
					phone: item.phone,
					driverIdCard: item.driverIdCard,
					logisticsNo: item.logisticsNo,
					licenseNumber: item.licenseNumber,
				})),
			};

			// 提交数据
			const res = await apiPost({ url: '/allocation/add', params });

			if (res && res.code === 200) {
				showMessage('添加成功');
				// 返回上一页
				navigateBack();
			} else {
				showMessage(res?.msg || '添加失败');
			}
		} catch (error) {
			console.error('提交表单失败', error);
			showMessage('提交失败，请重试');
		}
	});
}

// 取消
function cancel() {
	navigateBack();
}
</script>

<style scoped>
.add-warehouse-container {
	padding: 20rpx;
	background-color: #f5f7fa;
}

.form-section {
	background-color: #ffffff;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.form-row {
	display: flex;
	margin-bottom: 20rpx;
}

.form-item {
	flex: 1;
	margin-right: 20rpx;
}

.form-item:last-child {
	margin-right: 0;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.required::before {
	content: '*';
	color: #f56c6c;
	margin-right: 4rpx;
}

.form-input,
.form-select,
.form-picker {
	width: 100%;
}

.product-section {
	background-color: #ffffff;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.product-header {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #ebeef5;
	font-weight: bold;
}

.product-header-item {
	font-size: 28rpx;
	color: #333;
	padding: 0 10rpx;
}

.product-header-action {
	width: 120rpx;
	text-align: center;
}

.no-data {
	padding: 40rpx 0;
	text-align: center;
	color: #909399;
	font-size: 28rpx;
}

.product-list {
	margin-top: 20rpx;
}

.product-item {
	border-bottom: 2rpx solid #ebeef5;
	padding-bottom: 20rpx;
	margin-bottom: 20rpx;
}

.product-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.product-item-content {
	display: flex;
	align-items: center;
	padding: 10rpx 0;
}

.product-item-field {
	font-size: 26rpx;
	color: #606266;
	padding: 0 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.product-item-action {
	width: 120rpx;
	text-align: center;
}

.logistics-info {
	background-color: #f5f7fa;
	padding: 20rpx;
	margin-top: 10rpx;
	border-radius: 4rpx;
}

.logistics-row {
	display: flex;
	margin-bottom: 20rpx;
}

.logistics-row:last-child {
	margin-bottom: 0;
}

.logistics-item {
	flex: 1;
	margin-right: 20rpx;
}

.logistics-item:last-child {
	margin-right: 0;
}

.logistics-label {
	display: block;
	font-size: 26rpx;
	color: #606266;
	margin-bottom: 10rpx;
}

.logistics-input {
	width: 100%;
}

.remark-section {
	background-color: #ffffff;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.remark-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.remark-input {
	width: 100%;
}

.action-buttons {
	display: flex;
	justify-content: flex-end;
	gap: 20rpx;
	padding: 20rpx 0 40rpx;
}
.edit-form-picker {
	width: calc(100% - 48rpx);
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
}

.edit-form-picker-text {
	font-size: 28rpx;
	color: #1d2129;
	flex: 1;
}
.edit-form-picker-text-placeholder {
	color: gray;
}

.edit-form-picker-arrow {
	font-size: 24rpx;
	color: #86909c;
}

.edit-form-picker-actions {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.edit-form-input-clear {
	position: absolute;
	right: 0;
	top: 0;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 24rpx;
}

.edit-form-input-clear-icon {
	color: #c0c4cc;
	font-size: 24rpx;
}
</style>
