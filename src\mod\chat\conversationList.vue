<template>
	<DIWAppPage mode="1" title="会话列表">
		<DIWProtect>
			<DIWRoot>
				<DIWScrollView>
					<!-- 搜索区域 -->
					<view class="search-area">
						<wd-search v-model="searchText" placeholder="搜索最近联系人" clearable hide-cancel shape="round" bg-color="#f5f7fa" />
					</view>

					<!-- 服务商联系人Tab栏 -->
					<view class="contact-tab-header">
						<view class="tab-container">
							<view class="tab-item" :class="{ 'tab-item--active': activeContactTab === 'merchants' }" @click="activeContactTab = 'merchants'">
								<text class="tab-text">商家</text>
								<wd-badge :model-value="merchantsUnread" :max="99" :show-zero="false" class="tab-badge" />
							</view>
							<view class="tab-divider"></view>
							<view class="tab-item" :class="{ 'tab-item--active': activeContactTab === 'customers' }" @click="activeContactTab = 'customers'">
								<text class="tab-text">客户</text>
								<wd-badge :model-value="customersUnread" :max="99" :show-zero="false" class="tab-badge" />
							</view>
						</view>
					</view>

					<!-- 按公司分组显示会话 -->
					<view v-for="group in groupedConversations" :key="group.companyName" class="company-group">
						<!-- 公司分组标题 -->
						<view class="company-header" @click="toggleCompany(group.companyName)">
							<view class="company-header-content">
								<view class="company-info">
									<wd-icon
										:name="isCompanyCollapsed(group.companyName) ? 'arrow-right' : 'arrow-down'"
										size="32rpx"
										class="collapse-icon"
										color="#909399"
									/>
									<view v-if="group.hasOnlineUsers" class="online-indicator"></view>
									<text class="company-name">{{ group.companyName || '未分组' }}</text>
									<text class="company-count">({{ group.conversations.length }})</text>
								</view>
								<wd-badge :model-value="group.totalUnread" :max="99" :show-zero="false" />
							</view>
						</view>

						<!-- 联系人列表 -->
						<view v-show="!isCompanyCollapsed(group.companyName)" class="company-conversations">
							<wd-cell-group>
								<wd-cell
									v-for="conv in group.conversations"
									:key="conv.id"
									class="conv-item"
									title-width="100%"
									clickable
									vertical
									@click="startConversation(conv)"
								>
									<view class="conversation-content">
										<view class="conversation-header">
											<view class="avatar-section">
												<view class="avatar">
													<image v-if="conv.avatarUrl" class="avatar-image" :src="conv.avatarUrl" mode="aspectFill" />
													<view v-if="conv.online > 0" class="avatar-online"></view>
												</view>
											</view>
											<view class="conversation-info">
												<view class="conversation-title">
													<text class="title-text">{{ conv.title }}</text>
													<view class="status-badge">
														<wd-badge :model-value="conv.unread" :max="99" :show-zero="false">
															<wd-tag v-if="conv.online > 0" type="success" size="small">在线</wd-tag>
															<wd-tag v-else size="small">离线</wd-tag>
														</wd-badge>
													</view>
												</view>
												<view class="conversation-subtitle">
													<text class="subtitle-text">{{ conv.subtitle }}</text>
													<text v-if="conv.last" class="last-time">{{ formatLastTime(conv.last.time) }}</text>
												</view>
												<view v-if="conv.last" class="last-message">
													<template v-if="conv.last.type === 0">
														<text class="message-preview">{{ conv.last.content }}</text>
													</template>
													<template v-else-if="conv.last.type === 1">
														<text class="message-preview">[图片]</text>
													</template>
													<template v-else-if="conv.last.type === 2">
														<text class="message-preview">[合同]</text>
													</template>
													<template v-else-if="conv.last.type === 3">
														<text class="message-preview">[商品]</text>
													</template>
												</view>
											</view>
										</view>
									</view>
								</wd-cell>
							</wd-cell-group>
						</view>
					</view>

					<!-- 空状态 -->
					<view v-if="groupedConversations.length === 0" class="empty-state">
						<wd-icon name="chat" size="120rpx" color="#ccc" />
						<text class="empty-text">{{ getEmptyStateText() }}</text>
					</view>
				</DIWScrollView>
			</DIWRoot>
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { protect, navigateTo, conversations } = useFramework();

const searchText = ref('');
const collapsedCompanies = ref<Set<string>>(new Set());
const activeContactTab = ref('merchants'); // 联系人Tab栏状态：merchants（商家）或 customers（客户）

function formatLastTime(date: Date) {
	const now = new Date();
	const diff = now.getTime() - date.getTime();
	const minutes = Math.floor(diff / 60000);
	const hours = Math.floor(diff / 3600000);
	const days = Math.floor(diff / 86400000);

	if (minutes < 1) return '刚刚';
	if (minutes < 60) return `${minutes}分钟前`;
	if (hours < 24) return `${hours}小时前`;
	if (days < 7) return `${days}天前`;

	return formatDateTime(date, 'MM/dd');
}

// 为服务商Tab模式提供的联系人分组数据
const contactTabGroups = computed(() => {
	const query = searchText.value.trim().toLowerCase();

	// 先过滤
	let filtered = [...conversations.value];
	if (query) {
		filtered = filtered.filter((conv) => conv.title.toLowerCase().includes(query) || conv.subtitle.toLowerCase().includes(query));
	}

	// 按租户类型分组
	const merchants = filtered.filter((conv) => conv.tenantType === 1);
	const customers = filtered.filter((conv) => conv.tenantType === 3);

	// 对每种类型的联系人进行公司分组
	const groupByCompany = (convs: any[]) => {
		// 先按未读消息数和时间排序
		const sorted = convs.sort((a, b) => {
			// 按未读消息数排序（多的在前）
			if ((a.unread || 0) !== (b.unread || 0)) {
				return (b.unread || 0) - (a.unread || 0);
			}
			// 未读消息数相同时，按时间排序
			if (a.last && b.last) {
				return b.last.time.getTime() - a.last.time.getTime();
			}
			if (a.last) return -1;
			if (b.last) return 1;
			return 0;
		});

		// 按公司分组
		const groups = new Map<string, any[]>();
		sorted.forEach((conv) => {
			const companyName = conv.subtitle || '未分组';
			if (!groups.has(companyName)) {
				groups.set(companyName, []);
			}
			groups.get(companyName)!.push(conv);
		});

		// 转换为数组格式并计算未读数和在线状态
		const result = Array.from(groups.entries()).map(([companyName, conversations]) => ({
			companyName,
			conversations,
			totalUnread: conversations.reduce((sum, conv) => sum + (conv.unread || 0), 0),
			hasOnlineUsers: conversations.some((conv) => conv.online > 0),
		}));

		// 排序：有在线用户的分组在前，然后按未读消息数排序，最后按公司名称排序
		result.sort((a, b) => {
			// 首先按在线状态排序
			if (a.hasOnlineUsers && !b.hasOnlineUsers) return -1;
			if (!a.hasOnlineUsers && b.hasOnlineUsers) return 1;

			// 在线状态相同时，按未读消息数排序（多的在前）
			if (a.totalUnread !== b.totalUnread) {
				return b.totalUnread - a.totalUnread;
			}

			// 其他情况按分组名称排序，未分组的放在最后
			if (a.companyName === '未分组') return 1;
			if (b.companyName === '未分组') return -1;
			return a.companyName.localeCompare(b.companyName);
		});

		return result;
	};

	return {
		merchants: groupByCompany(merchants),
		customers: groupByCompany(customers),
	};
});

// 计算各Tab的未读消息数
const merchantsUnread = computed(() => {
	return contactTabGroups.value.merchants.reduce((total, group) => total + group.totalUnread, 0);
});

const customersUnread = computed(() => {
	return contactTabGroups.value.customers.reduce((total, group) => total + group.totalUnread, 0);
});

// 按公司分组的会话列表
const groupedConversations = computed(() => {
	const query = searchText.value.trim().toLowerCase();

	// 先过滤和排序
	let filtered = [...conversations.value];
	if (query) {
		filtered = filtered.filter((conv) => conv.title.toLowerCase().includes(query) || conv.subtitle.toLowerCase().includes(query));
	}

	// 按未读消息数和时间排序
	filtered.sort((a, b) => {
		// 首先按未读消息数排序（多的在前）
		if ((a.unread || 0) !== (b.unread || 0)) {
			return (b.unread || 0) - (a.unread || 0);
		}

		// 未读消息数相同时，按时间排序
		if (a.last && b.last) {
			return b.last.time.getTime() - a.last.time.getTime();
		}
		if (a.last) return -1;
		if (b.last) return 1;
		return 0;
	});

	// 按公司分组
	const groups = new Map<string, any[]>();
	filtered.forEach((conv) => {
		const companyName = conv.subtitle || '';
		if (!groups.has(companyName)) {
			groups.set(companyName, []);
		}
		groups.get(companyName)!.push(conv);
	});

	// 转换为数组格式并计算未读数和在线状态
	const result = Array.from(groups.entries()).map(([companyName, conversations]) => ({
		companyName,
		conversations,
		totalUnread: conversations.reduce((sum, conv) => sum + (conv.unread || 0), 0),
		hasOnlineUsers: conversations.some((conv) => conv.online > 0),
	}));

	// 排序：有在线用户的分组在前，然后按未读消息数排序，最后按公司名称排序
	result.sort((a, b) => {
		// 首先按在线状态排序
		if (a.hasOnlineUsers && !b.hasOnlineUsers) return -1;
		if (!a.hasOnlineUsers && b.hasOnlineUsers) return 1;

		// 在线状态相同时，按未读消息数排序（多的在前）
		if (a.totalUnread !== b.totalUnread) {
			return b.totalUnread - a.totalUnread;
		}

		// 未读消息数相同时，按公司名称排序，未分组的放在最后
		if (!a.companyName && !b.companyName) return 0;
		if (!a.companyName) return 1;
		if (!b.companyName) return -1;
		return a.companyName.localeCompare(b.companyName);
	});

	// 如果有搜索查询，自动展开有匹配结果的公司
	if (query) {
		result.forEach((group) => {
			if (group.conversations.length > 0) {
				collapsedCompanies.value.delete(group.companyName);
			}
		});
	}

	return result;
});

// 折叠相关函数
function toggleCompany(companyName: string) {
	if (collapsedCompanies.value.has(companyName)) {
		collapsedCompanies.value.delete(companyName);
	} else {
		collapsedCompanies.value.add(companyName);
	}
}

function isCompanyCollapsed(companyName: string) {
	return collapsedCompanies.value.has(companyName);
}

function getEmptyStateText() {
	if (searchText.value) {
		return '没有找到相关联系人';
	}

	return '暂无会话';
}

function startConversation(conv: any) {
	console.log(conv);

	protect(async () => {
		await navigateTo({
			url: '/mod/chat/chat',
			params: { id: conv.id, isInvalid: conv.isInvalid },
		});
	});
}
</script>

<style scoped lang="scss">
.search-area {
	background-color: #fff;
	position: sticky;
	top: 0;
	z-index: 10;
}

.contact-tab-header {
	background-color: #fff;
	border-bottom: 2rpx solid #ebeef5;
	position: sticky;
	top: 0;
	z-index: 9;
}

.tab-container {
	display: flex;
	align-items: center;
}

.tab-item {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 24rpx 0;
	cursor: pointer;
	transition: all 0.2s ease;
	position: relative;
	gap: 8rpx;
}

.tab-text {
	font-size: 28rpx;
	color: #606266;
	transition: color 0.2s ease;
}

.tab-item--active .tab-text {
	color: #409eff;
	font-weight: 500;
}

.tab-item--active::after {
	content: '';
	position: absolute;
	bottom: -2rpx;
	left: 0;
	right: 0;
	height: 4rpx;
	background-color: #409eff;
}

.tab-badge {
	margin-left: 8rpx;
}

.tab-divider {
	width: 2rpx;
	height: 32rpx;
	background-color: #ebeef5;
}

.company-group {
	margin-bottom: 0;
}

.company-header {
	background-color: #f5f7fa;
	border-bottom: 1rpx solid #ebeef5;
	padding: 24rpx 32rpx;
	position: sticky;
	top: 0;
	z-index: 5;
}

.company-header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.company-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.collapse-icon {
	transition: transform 0.2s ease;
}

.online-indicator {
	width: 16rpx;
	height: 16rpx;
	background-color: #67c23a;
	border-radius: 50%;
}

.company-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #303133;
}

.company-count {
	font-size: 24rpx;
	color: #909399;
}

.company-conversations {
	background-color: #fff;
	border-bottom: 16rpx solid #f5f7fa;
}

.conv-item {
	--wot-cell-vertical-top: 0;
	--wot-cell-padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.conversation-content {
	width: 100%;
}

.conversation-header {
	display: flex;
	align-items: flex-start;
	margin: 10rpx 10rpx 10rpx 10rpx;
	gap: 24rpx;
	width: 100%;
}

.avatar-section {
	flex-shrink: 0;
	position: relative;
}

.avatar {
	display: inline-block;
	border-radius: 8rpx;
	width: 80rpx;
	height: 80rpx;
	background-color: #f0f0f0;
	overflow: hidden;
	position: relative;
}

.avatar-image {
	position: absolute;
	inset: 0;
	width: 80rpx;
	height: 80rpx;
}

.avatar-online {
	position: absolute;
	bottom: -2rpx;
	right: -2rpx;
	width: 24rpx;
	height: 24rpx;
	background-color: #67c23a;
	border: 4rpx solid #fff;
	border-radius: 50%;
}

.conversation-info {
	flex: 1;
	min-width: 0;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	padding-top: 4rpx;
}

.conversation-title {
	display: flex;
	justify-content: space-between;
	padding-right: 30rpx;
	align-items: center;
	width: 90%;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.status-badge {
	flex-shrink: 0;
	margin-left: 10rpx;
}

.conversation-subtitle {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	color: #666;
	width: 90%;
}

.subtitle-text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

.last-time {
	flex-shrink: 0;
	margin-left: 16rpx;
	font-size: 22rpx;
	color: #999;
}

.last-message {
	font-size: 24rpx;
	color: #999;
}

.message-preview {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 32rpx;
	gap: 32rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #909399;
}
</style>
