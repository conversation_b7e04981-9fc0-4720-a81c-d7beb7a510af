<!-- 
DIWFile 负责所有文件上传、下载、显示、预览
兼容性说明：
1. APP平台（安卓/iOS）：使用 uni.chooseMedia API，统一处理图片和视频选择
2. H5平台：使用 chooseFile API，支持所有文件类型

API兼容性：
- uni.chooseMedia: APP平台支持，统一的媒体选择API
- uni.chooseFile: 仅H5平台，支持所有文件类型

支持的文件类型：
- 图片：jpg, jpeg, png, gif, bmp, webp, svg等
- 视频：mp4, avi, mov, wmv, flv, mkv等  
- 文档：pdf, doc, docx, xls, xlsx等（H5平台支持，APP平台仅支持预览）
-->
<!-- 
使用示例：
只允许选择图片 
<DIWFile v-model="imageFiles" :limit="3" accept="image/*" />

只允许选择视频 
<DIWFile v-model="videoFiles" :limit="1" accept="video/*" />

允许选择图片和视频 
<DIWFile v-model="mediaFiles" :limit="5" accept="image/*,video/*" />

只允许选择PDF文件（仅H5平台）
<DIWFile v-model="pdfFiles" :limit="1" accept=".pdf" />

允许选择文档文件（PDF、Word、Excel）（仅H5平台）
<DIWFile v-model="docFiles" :limit="3" accept=".pdf,.doc,.docx,.xls,.xlsx" />

允许所有类型文件（仅H5平台）
<DIWFile v-model="allFiles" :limit="5" accept="all" />

设置文件大小限制（10MB）
<DIWFile v-model="files" :limit-size="10" />

注意事项：
1. H5环境支持所有文件类型选择
2. APP环境使用统一的chooseMedia API选择图片和视频，更加稳定
3. 使用 accept 属性可以限制文件类型，支持MIME类型和扩展名两种格式
4. chooseMedia API返回格式与传统API不同，已适配新的数据结构
-->
<template>
	<view class="diw-file" :style="{ justifyContent: 'flex-start !important', width: '100%' }">
		<!-- APP-PLUS平台特殊处理 -->
		<!-- #ifdef APP-PLUS -->
		<view class="diw-file__container">
			<!-- #endif -->

			<view v-for="item in displayItems" :key="item.id" class="diw-file__preview">
				<template v-if="item.type === 1">
					<!-- 图片文件 -->
					<wd-img v-if="item.fileType === 'image'" :width="160" :height="160" :src="item.url" mode="aspectFill" enable-preview />
					<!-- 视频文件 -->
					<view v-else-if="item.fileType === 'video'" class="diw-file__preview-content" @click="previewVideo(item)">
						<wd-icon name="videocam" size="80rpx" />
					</view>
					<!-- PDF文件 -->
					<view v-else-if="item.fileType === 'pdf'" class="diw-file__preview-content bg-blue-50" @click="previewFile(item)">
						<wd-icon name="document" size="80rpx" />
					</view>
					<!-- Word文件 -->
					<view v-else-if="item.fileType === 'word'" class="diw-file__preview-content bg-blue-50">
						<wd-icon name="document" size="80rpx" />
					</view>
					<!-- Excel文件 -->
					<view v-else-if="item.fileType === 'excel'" class="diw-file__preview-content bg-green-50">
						<wd-icon name="document" size="80rpx" />
					</view>
					<!-- 其他文件类型 -->
					<view v-else class="diw-file__preview-content">
						<wd-icon name="document" size="80rpx" />
					</view>
				</template>
				<template v-else-if="item.type === 0">
					<!-- 图片文件 -->
					<wd-img v-if="item.fileType === 'image'" :width="160" :height="160" :src="item.previewUrl" mode="aspectFill" enable-preview />
					<!-- 视频文件 -->
					<view v-else-if="item.fileType === 'video'" class="diw-file__preview-content" @click="previewVideo(item)">
						<wd-icon name="videocam" size="80rpx" />
					</view>
					<!-- 其他文件类型 -->
					<view v-else class="diw-file__preview-content">
						<wd-icon name="document" size="80rpx" />
					</view>
				</template>
				<template v-else-if="item.type === 2">
					<!-- 正在解析的文件 -->
					<view class="diw-file__preview-content">
						<wd-loading />
					</view>
				</template>
				<!-- 文件名称展示 -->
				<view v-if="props.showFileName" class="diw-file__filename">
					{{ item.displayName }}
				</view>
				<wd-icon v-if="!props.readonly" name="delete" custom-class="diw-file__close" @click="removeFile(item.id)" />
			</view>
			<template v-if="canAddMore">
				<view @click="addFiles" class="diw-file__evoke">
					<!-- 唤起项图标 -->
					<wd-icon class="diw-file__evoke-icon" name="fill-camera"></wd-icon>
					<!-- 有限制个数时确认是否展示限制个数 -->
					<view v-if="props.limit > 0 && showLimitNum" class="diw-file__evoke-num">（{{ displayItems.length }}/{{ props.limit }}）</view>
				</view>
			</template>

			<!-- APP-PLUS平台特殊处理结束 -->
			<!-- #ifdef APP-PLUS -->
		</view>
		<!-- #endif -->

		<!-- 视频播放器 -->
		<view v-if="currentVideoUrl" class="diw-file__video-container">
			<view class="diw-file__video-overlay" @click="closeVideo"></view>
			<view class="diw-file__video-player">
				<view class="diw-file__video-close" @click="closeVideo">×</view>
				<video :src="currentVideoUrl" controls autoplay class="diw-file__video"></video>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
const props = withDefaults(
	defineProps<{
		// 是否只读模式，也就是只用于查看
		readonly?: boolean;
		limit?: number;
		showLimitNum?: boolean;
		showFileName?: boolean;
		limitSize?: number;
		multiple?: boolean;
		// 接受的文件类型
		accept?: string | string[];
	}>(),
	{
		readonly: false,
		limit: 1,
		showLimitNum: true,
		showFileName: false,
		limitSize: 0, // 默认不限制文件大小
		multiple: false,
	}
);

const model = defineModel<Array<DIWFileItem | string> | string>();
const currentVideoUrl = ref<string>('');

const { mapFileIdToUrl, apiGet, uploadFileHelper } = useFramework();

// 生成唯一ID
let seq = 1000;
function nextKey() {
	return `nk_${seq++}`;
}

// 文件类型映射 - 根据MIME类型
const FILE_TYPE_MAP: Record<string, DIWFileType> = {
	// 图片类型
	'image/jpeg': 'image',
	'image/jpg': 'image',
	'image/png': 'image',
	'image/apng': 'image',
	'image/gif': 'image',
	'image/bmp': 'image',
	'image/webp': 'image',
	'image/svg+xml': 'image',
	'image/tiff': 'image',

	// 视频类型
	'video/mp4': 'video',
	'video/avi': 'video',
	'video/quicktime': 'video', // .mov
	'video/x-ms-wmv': 'video', // .wmv
	'video/x-flv': 'video', // .flv
	'video/x-matroska': 'video', // .mkv
	'video/webm': 'video',
	'video/mpeg': 'video',
	'video/ogg': 'video',

	// 文档类型
	'application/pdf': 'pdf',
	'application/msword': 'word', // .doc
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'word', // .docx
	'application/vnd.ms-excel': 'excel', // .xls
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'excel', // .xlsx
};

// 文件扩展名映射
const FILE_EXTENSION_MAP: Record<string, DIWFileType> = {
	// 图片扩展名
	jpg: 'image',
	jpeg: 'image',
	png: 'image',
	apng: 'image',
	gif: 'image',
	bmp: 'image',
	webp: 'image',
	svg: 'image',
	tiff: 'image',
	tif: 'image',

	// 视频扩展名
	mp4: 'video',
	avi: 'video',
	mov: 'video',
	wmv: 'video',
	flv: 'video',
	mkv: 'video',
	webm: 'video',
	mpeg: 'video',
	ogg: 'video',

	// 文档扩展名
	pdf: 'pdf',
	doc: 'word',
	docx: 'word',
	xls: 'excel',
	xlsx: 'excel',
};

// 监听model值变化
watchEffect(() => {
	if (model.value) {
		if (typeof model.value === 'string') {
			const mv = model.value.split(',');
			parseModelValue(mv);
		} else if (Array.isArray(model.value)) {
			parseModelValue(model.value);
		}
	}
});

// 解析model值
function parseModelValue(modelValue: Array<DIWFileItem | string>) {
	const fileIds: string[] = [];
	const itemList: DIWFileItem[] = [];

	for (const item of modelValue) {
		if (typeof item === 'string') {
			fileIds.push(item);
			itemList.push({ type: 2, id: item });
		} else {
			itemList.push(item);
		}
	}

	if (fileIds.length > 0) {
		resolveFiles(fileIds)
			.then((d) => updateModelFiles(d, modelValue))
			.catch((err) => console.error(err));
	}
}

interface SysFileInfo {
	id: string;
	fileName: string;
	original: string;
	size: string;
}

// 根据文件ID获取文件信息
async function resolveFiles(files: (DIWFileItem | string)[]): Promise<SysFileInfo[]> {
	if (Array.isArray(files) && files.length > 0) {
		const ids = files.filter((e) => typeof e === 'string');
		return await apiGet({ url: 'admin/sys-file/list', params: { ids } });
	}
	return [];
}

// 更新model值中的文件信息
function updateModelFiles(files: SysFileInfo[], modelValue: Array<DIWFileItem | string>) {
	const fileMap = new Map<string, SysFileInfo>();
	files.forEach((e) => fileMap.set(e.id, e));

	if (Array.isArray(modelValue)) {
		const result: DIWFileItem[] = [];

		for (const item of modelValue) {
			let fileInfo: SysFileInfo | undefined;

			if (typeof item === 'string') {
				fileInfo = fileMap.get(item);
			} else if (item.type === 2) {
				fileInfo = fileMap.get(item.id);
			} else {
				result.push(item);
				continue;
			}

			if (fileInfo) {
				const displayName = fileInfo.original || fileInfo.fileName;

				result.push({
					type: 1,
					id: fileInfo.id,
					url: mapFileIdToUrl(fileInfo.id),
					fileType: detectFileTypeFromFileName(displayName),
					displayName,
					fileSize: fileInfo.size,
				});
			}
		}

		model.value = result;
	}
}

// 获取显示项列表
const displayItems = computed<DIWFileItem[]>(() => {
	const result: DIWFileItem[] = [];

	if (Array.isArray(model.value)) {
		for (const item of model.value) {
			if (typeof item === 'string') {
				result.push({ type: 2, id: item });
			} else {
				result.push(item);
			}
		}
	}

	return result;
});

// 是否可以添加更多文件
const canAddMore = computed(() => {
	return !props.readonly && displayItems.value.length < props.limit;
});

// 从文件对象检测文件类型
function detectFileType(file: File): DIWFileType {
	// 通过MIME类型判断
	if (file.type && FILE_TYPE_MAP[file.type]) {
		return FILE_TYPE_MAP[file.type];
	}

	return detectFileTypeFromFileName(file.name);
}

// 从文件名检测文件类型
function detectFileTypeFromFileName(fileName: string): DIWFileType {
	if (!fileName) return 'other';

	// 获取文件扩展名
	const extension = fileName.split('.').pop()?.toLowerCase() || '';

	// 通过文件扩展名判断
	if (extension && FILE_EXTENSION_MAP[extension]) {
		return FILE_EXTENSION_MAP[extension];
	}

	return 'other';
}

// 从路径中提取文件名
function extractFileName(path: string) {
	const sep = path.lastIndexOf('/');
	return sep >= 0 ? path.substring(sep + 1) : path;
}

// 移除文件
function removeFile(id: string) {
	if (Array.isArray(model.value)) {
		model.value = model.value.filter((e) => !(typeof e !== 'string' && e.id === id));
	}
}

// 检查文件大小是否超出限制
function checkFileSize(size: number): boolean {
	// 如果没有设置大小限制，直接通过
	if (!props.limitSize || props.limitSize <= 0) {
		return true;
	}

	// 如果文件大小为0或未定义，可能是获取失败，但仍然允许
	if (!size || size <= 0) {
		console.warn('文件大小获取失败，但仍允许上传');
		return true;
	}

	// 检查是否超出限制
	const maxSize = props.limitSize * 1024 * 1024;
	if (size > maxSize) {
		const sizeInMB = (size / (1024 * 1024)).toFixed(2);
		console.log(`文件大小 ${sizeInMB}MB 超出限制 ${props.limitSize}MB`);
		uni.showToast({
			title: `文件超出大小限制 ${props.limitSize}MB`,
			icon: 'none',
		});
		return false;
	}

	return true;
}

// 检查文件类型是否符合accept限制
function checkFileTypeByAccept(fileItem: DIWFileItem_New): boolean {
	if (!props.accept) return true;

	const acceptTypes = Array.isArray(props.accept) ? props.accept : props.accept.split(',');

	// 只接受图片
	if (acceptTypes.every((type) => type.startsWith('image/'))) {
		return fileItem.fileType === 'image';
	}

	// 只接受视频
	if (acceptTypes.every((type) => type.startsWith('video/'))) {
		return fileItem.fileType === 'video';
	}

	// 其他情况，检查文件类型是否在accept列表中
	const fileExt = fileItem.displayName?.split('.').pop()?.toLowerCase() || '';
	const fileMimeType = fileItem.raw && (fileItem.raw as File).type ? (fileItem.raw as File).type : '';

	// 检查文件扩展名是否匹配accept中的任一类型
	const isExtensionMatch = acceptTypes.some((acceptType) => {
		// 处理.pdf, .doc这类带点的扩展名
		if (acceptType.startsWith('.')) {
			return `.${fileExt}` === acceptType.toLowerCase();
		}

		// 处理像application/pdf这样的MIME类型
		if (acceptType.includes('/')) {
			// 如果文件有MIME类型，直接比较
			if (fileMimeType) {
				// 处理通配符，如image/*
				if (acceptType.endsWith('/*')) {
					const acceptPrefix = acceptType.split('/')[0];
					const fileMimePrefix = fileMimeType.split('/')[0];
					return acceptPrefix === fileMimePrefix;
				}

				return acceptType === fileMimeType;
			}

			// 如果没有MIME类型，尝试从扩展名判断
			const acceptSubtype = acceptType.split('/')[1];
			if (acceptType.startsWith('image/')) {
				// 检查特定图片类型
				if (acceptSubtype !== '*') {
					// 处理特殊情况，如image/jpeg应该匹配jpg和jpeg
					if (acceptSubtype === 'jpeg' && ['jpg', 'jpeg'].includes(fileExt)) {
						return true;
					} else if (acceptSubtype === 'svg+xml' && fileExt === 'svg') {
						return true;
					} else {
						// 直接比较子类型和扩展名
						return acceptSubtype === fileExt;
					}
				}
				return fileItem.fileType === 'image';
			} else if (acceptType.startsWith('video/')) {
				// 检查特定视频类型
				if (acceptSubtype !== '*') {
					// 处理特殊情况
					if (acceptSubtype === 'quicktime' && fileExt === 'mov') {
						return true;
					} else if (acceptSubtype === 'x-ms-wmv' && fileExt === 'wmv') {
						return true;
					} else if (acceptSubtype === 'x-flv' && fileExt === 'flv') {
						return true;
					} else if (acceptSubtype === 'x-matroska' && fileExt === 'mkv') {
						return true;
					} else {
						// 直接比较子类型和扩展名
						return acceptSubtype === fileExt;
					}
				}
				return fileItem.fileType === 'video';
			} else if (acceptType === 'application/pdf') {
				return fileExt === 'pdf';
			} else if (acceptType.includes('wordprocessing') || acceptType.includes('msword')) {
				return ['doc', 'docx'].includes(fileExt);
			} else if (acceptType.includes('spreadsheet') || acceptType.includes('excel')) {
				return ['xls', 'xlsx'].includes(fileExt);
			} else {
				// 尝试直接用acceptSubtype与文件扩展名匹配
				// 处理application/json, text/plain等情况
				return acceptSubtype === fileExt;
			}
		}

		// 直接比较扩展名（不带点）
		return fileExt === acceptType.toLowerCase();
	});

	if (!isExtensionMatch) {
		uni.showToast({
			title: '不支持该文件类型',
			icon: 'none',
		});
		return false;
	}

	return true;
}

// 更新文件列表
function updateFiles(files: DIWFileItem_New[]) {
	// 过滤符合accept条件的文件
	const allowedFiles = files.filter(checkFileTypeByAccept);

	if (allowedFiles.length > 0) {
		let updatedFiles: (DIWFileItem | string)[] = [];

		if (model.value) {
			updatedFiles = [...model.value, ...allowedFiles];
		} else {
			updatedFiles = allowedFiles;
		}

		model.value = updatedFiles.slice(0, Math.min(updatedFiles.length, props.limit));
	}
}

// 添加文件
function addFiles() {
	let count = Math.min(9, props.limit - displayItems.value.length);
	if (!canAddMore.value || count < 1) {
		return;
	}

	let chooseFileType: 'image' | 'video' | 'all' = 'all';

	// 根据accept属性决定文件类型
	if (props.accept) {
		const acceptTypes = Array.isArray(props.accept) ? props.accept : props.accept.split(',');
		// 仅图片
		if (acceptTypes.every((type) => type.startsWith('image/'))) {
			chooseFileType = 'image';
		}
		// 仅视频
		else if (acceptTypes.every((type) => type.startsWith('video/'))) {
			chooseFileType = 'video';
		}
	}

	// 处理APP平台
	// #ifdef APP-PLUS
	handleAppFileSelect(chooseFileType);
	// #endif

	// 处理H5平台
	// #ifdef H5
	handleH5FileSelect(chooseFileType, count);
	// #endif
}

// APP平台选择文件
function handleAppFileSelect(chooseFileType: 'image' | 'video' | 'all') {
	// 处理APP平台选择图片
	const handleAppImageSelect = () => {
		uni.chooseMedia({
			count: props.multiple ? Math.min(9, props.limit - displayItems.value.length) : 1,
			mediaType: ['image'],
			sourceType: ['album', 'camera'],
			sizeType: ['original', 'compressed'],
			success: (res) => {
				const files: DIWFileItem_New[] = [];
				// chooseMedia返回的是tempFiles数组，每个对象包含tempFilePath
				for (const tempFile of res.tempFiles) {
					const path = tempFile.tempFilePath; // 注意：chooseMedia使用tempFilePath
					const fileName = extractFileName(path);
					const fileSize = tempFile.size || 0;

					if (!checkFileSize(fileSize)) continue;

					files.push({
						id: nextKey(),
						type: 0,
						previewUrl: path,
						raw: tempFile,
						fileType: 'image', // chooseMedia可以直接使用tempFile.fileType，但这里固定为image
						displayName: fileName,
						fileSize: fileSize,
					});
				}

				if (files.length > 0) {
					updateFiles(files);
				}
			},
			fail: handleChooseMediaFail,
		});
	};

	// 处理APP平台选择视频
	const handleAppVideoSelect = () => {
		uni.chooseMedia({
			count: props.multiple ? Math.min(9, props.limit - displayItems.value.length) : 1,
			mediaType: ['video'],
			sourceType: ['album', 'camera'],
			maxDuration: 60,
			camera: 'back',
			success: (res) => {
				const files: DIWFileItem_New[] = [];
				// chooseMedia返回的是tempFiles数组
				for (const tempFile of res.tempFiles) {
					const path = tempFile.tempFilePath; // chooseMedia使用tempFilePath
					const fileName = extractFileName(path);
					const fileSize = tempFile.size || 0;

					if (!checkFileSize(fileSize)) continue;

					files.push({
						id: nextKey(),
						type: 0,
						previewUrl: path,
						raw: tempFile,
						fileType: 'video', // chooseMedia可以直接使用tempFile.fileType
						displayName: fileName,
						fileSize: fileSize,
					});
				}

				if (files.length > 0) {
					updateFiles(files);
				}
			},
			fail: handleChooseMediaFail,
		});
	};

	// 处理chooseMedia操作失败
	const handleChooseMediaFail = (err: any) => {
		console.error('chooseMedia 操作失败：', err);
		// 用户取消操作通常不需要显示错误提示
		if (err.errMsg && (err.errMsg.includes('cancel') || err.errMsg.includes('取消'))) {
			console.log('用户取消选择文件');
			return;
		}
		// 显示其他错误信息
		uni.showToast({
			title: err.errMsg || '选择文件失败',
			icon: 'none',
		});
	};

	if (chooseFileType === 'image') {
		handleAppImageSelect();
	} else if (chooseFileType === 'video') {
		handleAppVideoSelect();
	} else {
		// 显示选择菜单
		uni.showActionSheet({
			itemList: ['选择图片', '选择视频'],
			success: (res) => {
				if (res.tapIndex === 0) {
					handleAppImageSelect();
				} else if (res.tapIndex === 1) {
					handleAppVideoSelect();
				}
			},
		});
	}
}

// H5平台选择文件
function handleH5FileSelect(chooseFileType: 'image' | 'video' | 'all', count: number) {
	uni.chooseFile({
		count: props.multiple ? count : 1,
		type: chooseFileType,
		accept: props.accept,
		success(res) {
			if (Array.isArray(res.tempFiles) && Array.isArray(res.tempFilePaths)) {
				const files: DIWFileItem_New[] = [];
				for (let i = 0; i < res.tempFiles.length; ++i) {
					const file = res.tempFiles[i] as File;
					// 检查文件大小
					if (!checkFileSize(file.size)) continue;

					files.push({
						id: nextKey(),
						type: 0,
						previewUrl: res.tempFilePaths[i],
						raw: file,
						fileType: detectFileType(file),
						displayName: file.name,
						fileSize: file.size,
					});
				}

				if (files.length > 0) {
					updateFiles(files);
				}
			}
		},
		fail: (err) => {
			console.error('chooseFile 操作失败：', err);
			// 用户取消操作通常不需要显示错误提示
			if (err.errMsg && (err.errMsg.includes('cancel') || err.errMsg.includes('取消'))) {
				console.log('用户取消选择文件');
				return;
			}
			// 显示其他错误信息
			uni.showToast({
				title: err.errMsg || '选择文件失败',
				icon: 'none',
			});
		},
	});
}

// 文件预览
function previewFile(item: DIWFileItem) {
	const fileItem = item as DIWFileItem_Uploaded | DIWFileItem_New;
	if (fileItem.fileType === 'pdf') {
		const url = (fileItem as DIWFileItem_Uploaded).url;

		// #ifdef H5
		window.open(url, '_blank');
		// #endif

		// #ifdef APP-PLUS
		previewPdfInApp(url);
		// #endif
	}
}

// APP端预览PDF文件
function previewPdfInApp(url: string) {
	uni.showLoading({
		title: '正在加载文件...',
	});

	uni.downloadFile({
		url: url,
		success: function (res) {
			uni.hideLoading();
			if (res.statusCode === 200) {
				const filePath = res.tempFilePath;
				uni.openDocument({
					filePath: filePath,
					showMenu: true,
					success: function () {
						console.log('打开文档成功');
					},
					fail: function (err) {
						console.error('打开文档失败', err);
						uni.showToast({
							title: '打开文档失败',
							icon: 'none',
						});
					},
				});
			} else {
				uni.showToast({
					title: '文件下载失败',
					icon: 'none',
				});
			}
		},
		fail: function (err) {
			uni.hideLoading();
			console.error('下载文件失败', err);
			uni.showToast({
				title: '下载文件失败',
				icon: 'none',
			});
		},
	});
}

// 视频预览
function previewVideo(item: DIWFileItem) {
	const fileItem = item as DIWFileItem_Uploaded | DIWFileItem_New;
	const url = (fileItem as DIWFileItem_Uploaded).url || (fileItem as DIWFileItem_New).previewUrl;
	currentVideoUrl.value = url;
}

// 关闭视频播放器
function closeVideo() {
	currentVideoUrl.value = '';
}

// 上传文件
async function upload(): Promise<string[]> {
	if (Array.isArray(model.value)) {
		const r = await uploadFileHelper(model.value);
		if (r) {
			model.value = r;
			return r.map((e) => {
				return typeof e === 'string' ? e : e.id;
			});
		}
	}
	return [];
}

defineExpose({
	upload,
});
</script>

<style scoped lang="scss">
.diw-file {
	display: flex;
	flex-wrap: wrap;
	position: relative;
	justify-content: flex-start;
	width: 100%;
	margin: -10rpx 0 0 -10rpx;

	/* #ifdef APP-PLUS */
	align-items: flex-start;
	text-align: left;
	/* #endif */

	&__container {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
	}

	&__preview {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border: 2rpx solid #eee;
		border-radius: 16rpx;
		overflow: hidden;
		margin: 10rpx 0 0 10rpx;
		box-sizing: border-box;

		&-content {
			width: 160rpx;
			height: 160rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
		}
	}

	&__close {
		position: absolute;
		top: 0;
		right: 0;
		background-color: transparent;
		color: #ff5000;
		border-radius: 0 0 0 16rpx;
		padding: 8rpx;
		z-index: 9;
		width: 48rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&::after {
			background-color: transparent;
		}
	}

	&__filename {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		font-size: 20rpx;
		background-color: rgba(255, 255, 255, 0.9);
		padding: 8rpx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		line-height: 1.2;
	}

	&__evoke {
		width: 160rpx;
		height: 160rpx;
		border: 2rpx dashed #ccc;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		background-color: #f8f8f8;
		margin: 10rpx 0 0 10rpx;
		transition: all 0.2s ease;

		/* #ifdef APP-PLUS */
		align-self: flex-start;
		float: left;
		/* #endif */

		&:active {
			background-color: #f0f0f0;
			transform: scale(0.98);
		}

		&-icon {
			font-size: 72rpx;
			color: #999;
		}

		&-num {
			font-size: 24rpx;
			color: #999;
			margin-top: 16rpx;
		}
	}

	&__video {
		&-container {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 999;
		}

		&-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.7);
		}

		&-player {
			position: relative;
			width: 90%;
			max-width: 750rpx;
			z-index: 1000;
		}

		&-close {
			position: absolute;
			top: -40rpx;
			right: 0;
			color: #fff;
			font-size: 40rpx;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 1001;
		}

		&-close::after {
			background-color: transparent;
		}
	}
}
</style>
