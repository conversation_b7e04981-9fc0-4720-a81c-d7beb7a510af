import { useSystemStore } from '@/store/system';
import { useDictStore } from '@/store/dict';
import { useAreaStore } from '@/store/area';
import { useUpload } from '@/store/upload';
import { useAgreementStore } from '@/store/agreement';
import { useIMStore } from '@/store/im';
import { useMessageStore } from '@/store/message';
import { navigateTo, invokeTo, navigateBack, usePageResult, usePageQuery, usePageInitData } from './nav';
import { isAbortError, throwAbortError } from './error';
import { injectDIWAppPageRoot, provideDIWAppPageRoot } from './page';
import { encodeArea, decodeArea } from '../util/area';
import { encryptPassword } from '../util/crypto';
import { appconfig } from '@/config/appconfig';

import { useStatStore } from '@/store/stat';
import { useOrderStore } from '@/store/order';
import { useProductStore } from '@/store/imProduct';
import i18n from '@/locale';

function buildDIWAppPageRoot() {
	let interfaceDIWAppPageRoot = injectDIWAppPageRoot();
	if (!interfaceDIWAppPageRoot) {
		let appPageImpl: DIWAppPageImpl | null = null;

		function registerAppPageImpl(impl: DIWAppPageImpl) {
			appPageImpl = impl;
		}

		function showLoading() {
			if (appPageImpl) {
				return appPageImpl.showLoading();
			}
			uni.showLoading({ mask: true });
		}

		function hideLoading() {
			if (appPageImpl) {
				return appPageImpl.hideLoading();
			}
			uni.hideLoading();
		}

		function showMessage(message: string) {
			if (appPageImpl) {
				return appPageImpl.showMessage(message);
			}
			uni.showToast({ icon: 'success', title: message });
		}

		function showErrorMessage(message: string) {
			if (appPageImpl) {
				return appPageImpl.showErrorMessage(message);
			}
			console.error(message);
			// uni.showToast({ icon: 'error', title: message });
			uni.showModal({
				title: i18n.global.t('error'),
				content: message,
				showCancel: false,
				confirmText: i18n.global.t('uni.showModal.confirm'),
				cancelText: i18n.global.t('uni.showModal.cancel'),
			});
		}

		function showError(err: any) {
			if (appPageImpl) {
				return appPageImpl.showError(err);
			}

			if (isAbortError(err)) {
				return;
			}

			if (err) {
				if (typeof err.msg === 'string') {
					showErrorMessage(err.msg);
					return;
				}

				if (err instanceof Error) {
					showErrorMessage(err.message);
					return;
				}

				if (err instanceof Blob) {
					if (err.type === 'application/json') {
						err
							.text()
							.then((d) => {
								return JSON.parse(d);
							})
							.then((e) => {
								if (typeof e.msg === 'string') {
									showErrorMessage(e.msg);
								}
							});
					}
				}

				console.error(typeof err, err);
			}
		}

		async function confirm(msg: string) {
			if (appPageImpl) {
				return await appPageImpl.confirm(msg);
			}

			const d = await uni.showModal({
				title: i18n.global.t('uni.showModal.confirm'),
				content: msg,
				confirmText: i18n.global.t('uni.showModal.confirm'),
				cancelText: i18n.global.t('uni.showModal.cancel'),
			});
			if (d.cancel) {
				throwAbortError();
			}
		}

		interfaceDIWAppPageRoot = { registerAppPageImpl, showLoading, hideLoading, showError, showErrorMessage, showMessage, confirm };
		provideDIWAppPageRoot(interfaceDIWAppPageRoot);
	}

	return interfaceDIWAppPageRoot;
}

export function useFramework() {
	const interfaceDIWAppPageRoot = buildDIWAppPageRoot();

	const ss = useSystemStore();
	const {
		apiGet,
		apiPost,
		apiPut,
		apiDelete,
		apiRequest,
		apiUpload,
		apiDownload,
		getLockedUsername,
		buildSessionInfo,
		login: SS_login,
		loginMobile: SS_loginMobile,
		logout: SS_logout,
	} = ss;
	const { sessionInfo, metrics } = storeToRefs(ss);

	const im = useIMStore();
	const { restart: IM_restart, stop: IM_stop } = im;
	const { conversations, totalUnread } = storeToRefs(im);

	const ms = useMessageStore();
	const { restart: MSG_restart, stop: MSG_stop, checkMessages, markMessageAsRead, markAllMessagesAsRead } = ms;
	const { hasUnreadMessage, totalUnreadMessage } = storeToRefs(ms);

	const { flush: flush_orders } = useOrderStore();
	const { flush: flush_products } = useProductStore();

	const { clearWorkbenchStat } = useStatStore();

	const { useDict, useDict2 } = useDictStore();

	const { getAreaList } = useAreaStore();

	const { getAgreementByType } = useAgreementStore();

	const { mapFileIdToUrl, uploadFileHelper, parseImageFiles, platformUploadFile, platformUploadFileEx } = useUpload();

	function checkAuth(auth?: string | string[], all?: boolean) {
		if (sessionInfo.value) {
			if (auth) {
				if (Array.isArray(auth)) {
					for (const e of auth) {
						const d = sessionInfo.value.permissions.includes(e);
						if (d) {
							if (!all) {
								return true;
							}
						} else {
							if (all) {
								return false;
							}
						}
					}

					return !!all;
				}

				return sessionInfo.value.permissions.includes(auth);
			}

			return true;
		}

		return false;
	}

	function showMessage(message: string) {
		return interfaceDIWAppPageRoot.showMessage(message);
	}

	function showErrorMessage(message: string) {
		return interfaceDIWAppPageRoot.showErrorMessage(message);
	}

	function confirm(msg: string) {
		return interfaceDIWAppPageRoot.confirm(msg);
	}

	function showError(err: any) {
		return interfaceDIWAppPageRoot.showError(err);
	}

	function showLoading() {
		return interfaceDIWAppPageRoot.showLoading();
	}

	function hideLoading() {
		return interfaceDIWAppPageRoot.hideLoading();
	}

	async function protect(func: () => any) {
		try {
			await Promise.resolve(func());
		} catch (err) {
			showError(err);
		}
	}

	async function protectOp(func: () => any, successMessage?: string | null): Promise<any>;
	async function protectOp(confirmMessage: string, func: () => any): Promise<any>;
	async function protectOp(confirmMessage: string, func: () => any, successMessage: string | null): Promise<any>;
	async function protectOp(m1: string | (() => any), m2?: string | null | (() => any), m3?: string | null): Promise<any> {
		if (typeof m1 === 'string') {
			if (typeof m2 === 'function') {
				try {
					if (m1.trim()) {
						await confirm(m1);
					}

					showLoading();

					try {
						await Promise.resolve(m2());
					} finally {
						hideLoading();
					}

					if (m3 !== null) {
						if (m3 !== undefined) {
							showMessage(m3);
						} else {
							showMessage('操作成功');
						}
					}
				} catch (err) {
					showError(err);
				}
			}
		} else {
			try {
				showLoading();

				try {
					await Promise.resolve(m1());
				} finally {
					hideLoading();
				}

				if (m2 !== null) {
					if (typeof m2 === 'string') {
						showMessage(m2);
					} else {
						showMessage('操作成功');
					}
				}
			} catch (err) {
				showError(err);
			}
		}
	}

	async function validateForm(form: any) {
		const v = isRef(form) ? form.value : form;
		if (v && typeof v === 'object' && 'validate' in v && typeof v.validate === 'function') {
			const z = await Promise.resolve(v.validate());
			if (!z.valid) {
				if (Array.isArray(z.errors) && z.errors.length > 0) {
					const firstError = z.errors[0];
					if (firstError && typeof firstError.message === 'string') {
						throw new Error(firstError.message);
					}
				}
				throw new Error('校验错误');
			}
		}
	}

	// 契约锁签合同
	async function invokeQYS(title: string, getUrlFunc: (backPageUrl: string) => Promise<string>, bizId: string, bizType: string) {
		const cookie = encryptPassword('DIW0.050400635613079614YARN');
		let origin = '';
		// #ifdef H5
		origin = window.location.origin;
		// #endif
		const backOrigin = appconfig.origin;
		const backPageUrl = `${backOrigin}/yarn/qiyuesuo/?cookie=${encodeURIComponent(cookie)}&bizId=${encodeURIComponent(bizId)}&bizType=${encodeURIComponent(bizType)}&app=1&origin=${encodeURIComponent(origin)}`;

		let url = '';

		try {
			uni.showLoading({ mask: true });
			url = await getUrlFunc(backPageUrl);
		} finally {
			uni.hideLoading();
		}

		await invokeTo({
			url: '/mod/common/webViewer',
			params: {
				title,
				cookie,
				url,
			},
		});
	}

	async function login(username: string, password: string): Promise<SessionInfo> {
		const si = await SS_login(username, password);
		// if (si.permissions.includes('chat_imConversation_view')) {
		// 	await IM_restart(si.accessToken, si.tenantId);
		// }

		// await MSG_restart(si.accessToken, si.tenantId);

		return si;
	}

	async function loginMobile(mobile: string, code: string): Promise<SessionInfo> {
		const si = await SS_loginMobile(mobile, code);
		// if (si.permissions.includes('chat_imConversation_view')) {
		// 	await IM_restart(si.accessToken, si.tenantId);
		// }

		// await MSG_restart(si.accessToken, si.tenantId);

		return si;
	}

	async function logout() {
		clearWorkbenchStat();
		await MSG_stop();
		await IM_stop();
		await SS_logout();
		flush_orders();
		flush_products();
	}

	async function reloadSessionInfo() {
		return await buildSessionInfo();
	}
	function mapDict2(...types: string[]) {
		let z: Record<string, any> = {};
		const p = new Promise((resolve, reject) => {
			z.resolve = resolve;
			z.reject = reject;
		});
		const r = useDict2(
			() => {
				z.resolve();
			},
			...types
		);

		return { signal: p, items: r };
	}
	function getTenantId() {
		if (sessionInfo.value) {
			return sessionInfo.value.tenantId;
		}
	}

	return {
		// API
		apiGet,
		apiPost,
		apiPut,
		apiDelete,
		apiRequest,
		apiUpload,
		apiDownload,
		getLockedUsername,
		login,
		loginMobile,
		logout,
		reloadSessionInfo,

		// IM
		conversations,
		totalUnread,

		// 通知消息
		totalUnreadMessage,
		hasUnreadMessage,
		checkMessages,
		markMessageAsRead,
		markAllMessagesAsRead,

		//
		getAreaList,
		encodeArea,
		decodeArea,

		// 文件
		mapFileIdToUrl,
		uploadFileHelper,
		parseImageFiles,
		platformUploadFile,
		platformUploadFileEx,
		// 会话
		sessionInfo,
		checkAuth,

		//
		metrics,

		//
		encryptPassword,

		//
		protect,
		protectOp,
		showError,
		showErrorMessage,
		showMessage,
		confirm,

		showLoading,
		hideLoading,

		// 表单
		validateForm,

		// 导航
		navigateTo,
		invokeTo,
		navigateBack,
		usePageResult,
		usePageQuery,
		usePageInitData,

		//
		mapDict: useDict,
		mapDict2,

		//
		getAgreementByType,

		//
		invokeQYS,
		getTenantId,
	};
}
