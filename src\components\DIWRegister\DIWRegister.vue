<template>
	<view class="register-page-container">
		<!-- 顶部Logo区域 -->
		<view class="logo-container">
			<image src="/static/yarn.jpg" class="logo-image" mode="aspectFit"></image>
		</view>

		<view class="register-container">
			<!-- 步骤条 -->
			<view class="steps-container">
				<view class="steps-wrapper">
					<view v-for="(stepItem, index) in steps" :key="index" class="step-item">
						<view class="step-icon" :class="{ active: step >= index + 1, completed: step > index + 1 }">
							<text>{{ index + 1 }}</text>
						</view>
						<view class="step-label" :class="{ active: step >= index + 1 }">{{ stepItem }}</view>
						<view v-if="index < steps.length - 1" class="step-line" :class="{ active: step > index + 1 }"></view>
					</view>
				</view>
			</view>

			<!-- 注册表单 -->
			<view class="register-form">
				<!-- 步骤1：验证手机号 -->
				<wd-form v-if="step === 1" ref="phoneFormRef" :model="phoneForm">
					<!-- 手机号输入框 -->
					<view class="input-item">
						<view class="phone-input-container">
							<wd-input
								v-model="phoneForm.phone"
								placeholder="请输入手机号"
								:disabled="inProgress"
								:maxlength="11"
								type="number"
								class="custom-input phone-input"
								clearable
								no-border
							>
								<template #prefix>
									<view class="input-prefix">手机号</view>
								</template>
							</wd-input>
						</view>
						<view class="error-tip">{{ phoneError }}</view>
					</view>

					<!-- 验证码输入框 -->
					<view class="input-item">
						<view class="flex items-center justify-between code-container">
							<view class="verification-input-wrap">
								<wd-input
									v-model="phoneForm.code"
									placeholder="请输入验证码"
									:disabled="inProgress"
									class="custom-input"
									type="number"
									clearable
									no-border
								>
									<template #prefix>
										<view class="input-prefix">验证码</view>
									</template>
								</wd-input>
							</view>
							<view class="get-code-btn" @click="handleSendSms" :class="{ disabled: countDown > 0 || !isPhoneValid }">
								{{ countDown > 0 ? `${countDown}秒后重试` : '获取验证码' }}
							</view>
						</view>
						<view class="error-tip">{{ codeError }}</view>
					</view>

					<!-- 协议同意 -->
					<view class="agreement items-center">
						<wd-checkbox v-model="phoneForm.checked" class="agreement-checkbox">
							<view class="flex items-center">
								<text class="text-xs text-gray-500 text-nowrap">我已阅读并同意</text>
								<view class="flex items-center">
									<text class="text-xs text-primary" @click.stop.prevent="viewAgreement('7', '服务协议')">《服务协议》</text>
									<text class="text-xs text-primary" @click.stop.prevent="viewAgreement('5', '隐私协议')">《隐私协议》</text>
									<text class="text-xs text-primary" @click.stop.prevent="viewAgreement('2', '监督声明')">《监督声明》</text>
								</view>
							</view>
						</wd-checkbox>
						<view class="error-tip">{{ checkedError }}</view>
					</view>

					<!-- 继续按钮 -->
					<view class="mt-6">
						<wd-button block :loading="inProgress" @click="handleVerifyCode" class="register-button"> 同意协议并继续 </wd-button>
					</view>
				</wd-form>

				<!-- 步骤2：填写账号信息 -->
				<wd-form v-if="step === 2" ref="registerFormRef" :model="registerForm">
					<!-- 账号名输入框 -->
					<view class="input-item">
						<view class="account-input-container">
							<wd-input
								v-model="registerForm.username"
								placeholder="账号名"
								:disabled="inProgress || !!phoneData.username"
								class="custom-input"
								clearable
								no-border
							>
								<template #prefix>
									<view class="input-prefix">账号名</view>
								</template>
							</wd-input>
						</view>
						<view class="error-tip">{{ usernameError }}</view>
					</view>

					<!-- 联系人输入框 -->
					<view class="input-item">
						<view class="account-input-container">
							<wd-input v-model="registerForm.contact" placeholder="企业联系人" :disabled="inProgress" class="custom-input" clearable no-border>
								<template #prefix>
									<view class="input-prefix">联系人</view>
								</template>
							</wd-input>
						</view>
						<view class="error-tip">{{ contactError }}</view>
					</view>

					<!-- 企业名称选择框 -->
					<view class="input-item">
						<view class="account-input-container">
							<wd-input
								v-model="registerForm.name"
								placeholder="请选择工商企业"
								:disabled="inProgress"
								class="custom-input"
								readonly
								no-border
								@click="showCompanySelect"
							>
								<template #prefix>
									<view class="input-prefix">企业名称</view>
								</template>
							</wd-input>
						</view>
						<view class="error-tip">{{ nameError }}</view>
					</view>

					<!-- 密码输入框 -->
					<view class="input-item" v-if="!phoneData.username">
						<view class="account-input-container">
							<wd-input
								v-model="registerForm.password"
								placeholder="建议使用两种或两种以上字符组合"
								:disabled="inProgress"
								class="custom-input"
								show-password
								no-border
								:maxlength="20"
								@input="checkPasswordStrength"
							>
								<template #prefix>
									<view class="input-prefix">设置密码</view>
								</template>
							</wd-input>
						</view>
						<view :class="score == 2 ? 'error-tip warning-tip' : score == 3 ? 'error-tip success-tip' : 'error-tip'">{{ passwordError }}</view>
					</view>

					<!-- 确认密码输入框 -->
					<view class="input-item" v-if="!phoneData.username">
						<view class="account-input-container">
							<wd-input
								v-model="registerForm.password2"
								placeholder="再次确认密码"
								:disabled="inProgress"
								class="custom-input"
								show-password
								no-border
								:maxlength="20"
							>
								<template #prefix>
									<view class="input-prefix">确认密码</view>
								</template>
							</wd-input>
						</view>
						<view class="error-tip">{{ password2Error }}</view>
					</view>

					<!-- 邀请码输入框 -->
					<view class="input-item">
						<view class="account-input-container">
							<wd-input
								v-model="registerForm.platformInvitationCode"
								placeholder="服务商邀请码(选填)"
								:disabled="inProgress"
								class="custom-input"
								clearable
								no-border
							>
								<template #prefix>
									<view class="input-prefix">邀请码</view>
								</template>
							</wd-input>
						</view>
						<view class="error-tip">{{ inviteCodeError }}</view>
					</view>

					<!-- 注册按钮 -->
					<view class="mb-4">
						<wd-button block :loading="inProgress" @click="handleRegister" class="register-button"> 注册 </wd-button>
					</view>
				</wd-form>

				<!-- 步骤3：注册成功 -->
				<view v-if="step === 3" class="success-container">
					<view class="success-icon">
						<wd-icon name="success" size="80" color="#07c160"></wd-icon>
					</view>
					<view class="success-title">注册成功</view>
					<view class="success-desc">您的账号已经创建成功，即将跳转到登录页面...</view>
					<view class="mt-6">
						<wd-button block @click="backToLogin" class="register-button"> 立即登录 </wd-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 企业选择组件 -->
		<DIWCompanySelect v-if="companySelectVisible" @select="handleCompanySelect" @close="companySelectVisible = false" />
	</view>
</template>

<script setup lang="ts">
import DIWCompanySelect from '../DIWCompanySelect/DIWCompanySelect.vue';

const { protect, apiRequest, encryptPassword, getAgreementByType, invokeTo } = useFramework();

const emit = defineEmits(['change']);

// 步骤内容
const steps = ref(['验证手机号', '填写账号信息', '注册成功']);
const step = ref(1);

// 加载状态
const inProgress = ref(false);

// 手机验证表单
const phoneFormRef = ref();
const phoneForm = ref({
	phone: '',
	code: '',
	checked: false,
});

// 注册信息表单
const registerFormRef = ref();
const registerForm = ref({
	username: '',
	phone: '',
	contact: '',
	name: '',
	password: '',
	password2: '',
	platformInvitationCode: '',
	identificationNumber: '',
});

// 错误提示
const phoneError = ref('');
const codeError = ref('');
const checkedError = ref('');
const usernameError = ref('');
const contactError = ref('');
const nameError = ref('');
const passwordError = ref('');
const password2Error = ref('');
const inviteCodeError = ref('');

// 验证码发送相关
const countDown = ref(0);
const timer = ref<any>(null);

// 密码强度评分
const score = ref(0);

// 手机号数据
interface PhoneData {
	username?: string;
	phone?: string;
	allowRegister: boolean;
}
const phoneData = ref<PhoneData>({ allowRegister: false });

// 企业选择相关
const companySelectVisible = ref(false);

// 计算手机号是否有效
const isPhoneValid = computed(() => {
	return /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/.test(phoneForm.value.phone);
});

// 显示用户协议
function viewAgreement(type: string, title: string) {
	protect(async () => {
		const d = await getAgreementByType(type);
		await invokeTo({ url: '/mod/common/richTextViewer', data: { content: d, title: title } });
	});
}

// 清除所有错误提示
function clearErrors() {
	phoneError.value = '';
	codeError.value = '';
	checkedError.value = '';
	usernameError.value = '';
	contactError.value = '';
	nameError.value = '';
	passwordError.value = '';
	password2Error.value = '';
	inviteCodeError.value = '';
}

// 返回登录页面
function backToLogin() {
	uni.navigateTo({ url: '/mod/main/login' });
}

// 验证手机号
async function validatePhone() {
	if (!phoneForm.value.phone) {
		phoneError.value = '请输入手机号';
		return false;
	}

	if (!isPhoneValid.value) {
		phoneError.value = '手机号格式不正确';
		return false;
	}

	try {
		const res = await apiRequest({
			url: 'admin/register/phoneExist',
			method: 'GET',
			parse: 0,
			params: { phone: phoneForm.value.phone, type: 3 },
		});

		if (res.data.allowRegister) {
			phoneData.value = res.data;
			return true;
		} else {
			phoneError.value = '手机号已存在于同类型企业中，不允许重复注册';
			return false;
		}
	} catch (error: any) {
		phoneError.value = error.msg || '验证手机号失败';
		return false;
	}
}

// 发送短信验证码
async function handleSendSms() {
	if (countDown.value > 0 || !isPhoneValid.value) return;

	phoneError.value = '';

	// 先验证手机号
	const isValid = await validatePhone();
	if (!isValid) return;

	protect(async () => {
		try {
			const data = await apiRequest({
				url: 'msg/sysMessage/send/smsCodeNoToken',
				method: 'GET',
				parse: 0,
				params: { mobile: phoneForm.value.phone, type: 'REGISTER' },
			});

			if (data.code === 0 && data.ok) {
				uni.showToast({
					title: '验证码已发送，请注意查收',
					icon: 'none',
				});

				// 开始倒计时
				countDown.value = 60;
				timer.value = setInterval(() => {
					if (countDown.value > 0) {
						countDown.value--;
					} else {
						clearInterval(timer.value);
						timer.value = null;
					}
				}, 1000);
			} else {
				codeError.value = data.msg || '获取验证码失败';
			}
		} catch (error: any) {
			codeError.value = error.msg || '获取验证码失败';
		}
	});
}

// 验证手机号和验证码
async function handleVerifyCode() {
	clearErrors();

	// 验证手机号
	if (!phoneForm.value.phone) {
		phoneError.value = '请输入手机号';
		return;
	}

	if (!isPhoneValid.value) {
		phoneError.value = '手机号格式不正确';
		return;
	}

	// 验证验证码
	if (!phoneForm.value.code) {
		codeError.value = '请输入验证码';
		return;
	}

	// 验证是否同意协议
	if (!phoneForm.value.checked) {
		checkedError.value = '请阅读并同意用户协议和隐私政策';
		return;
	}

	inProgress.value = true;

	protect(async () => {
		try {
			const res = await apiRequest({
				url: 'admin/register/valid',
				method: 'POST',
				parse: 0,
				data: {
					phone: phoneForm.value.phone,
					mobileCode: phoneForm.value.code,
				},
			});

			if (res.code === 0) {
				uni.showToast({
					title: '验证成功',
					icon: 'success',
				});

				// 清除定时器
				clearInterval(timer.value);

				// 进入下一步
				step.value = 2;

				// 设置注册表单的手机号
				registerForm.value.phone = phoneForm.value.phone;

				// 如果手机号已经关联了用户名，则自动填充
				if (phoneData.value.username) {
					registerForm.value.username = phoneData.value.username;
					registerForm.value.phone = phoneData.value.phone || '';
					usernameError.value = '该手机号已注册，您可直接注册企业，无需重复创建账号';
				}
			} else {
				codeError.value = res.msg || '验证码验证失败';
			}
		} catch (error: any) {
			codeError.value = error.msg || '验证码验证失败';
		} finally {
			inProgress.value = false;
		}
	});
}

// 显示企业选择弹窗
function showCompanySelect() {
	companySelectVisible.value = true;
}

// 处理企业选择
function handleCompanySelect(company: any) {
	console.log(company);

	if (company.new_status !== '存续') {
		nameError.value = '该企业状态异常，不能选择';
		registerForm.value.name = '';
		registerForm.value.identificationNumber = '';
		return;
	}

	registerForm.value.name = company.name;
	registerForm.value.identificationNumber = company.credit_no || '';
	nameError.value = '';
	companySelectVisible.value = false;
}

// 检查密码强度
function checkPasswordStrength(val: any) {
	const hasLetter = /[a-zA-Z]/.test(val.value);
	const hasNumber = /[0-9]/.test(val.value);
	const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(val.value);
	const validLength = val.value.length >= 8 && val.value.length <= 20;
	console.log(val.value, hasLetter, hasNumber, hasSymbol, validLength);

	if (!val.value) {
		passwordError.value = '请将密码设置为8-20位，由字母、数字和符号两种以上组合';
		score.value = 0;
		return;
	}

	if (!validLength) {
		passwordError.value = '请将密码设置为8-20位，由字母、数字和符号两种以上组合';
		score.value = 0;
		return;
	}

	const strengthLevel = [hasLetter, hasNumber, hasSymbol].filter(Boolean).length;

	if (strengthLevel === 1) {
		passwordError.value = '有被盗的风险，建议使用字母、数字和符号两种以上组合';
		score.value = 1;
	} else if (strengthLevel === 2) {
		passwordError.value = '安全强度适中，可以使用三种以上的组合来提高安全强度';
		score.value = 2;
	} else if (strengthLevel === 3) {
		passwordError.value = '你的密码很安全';
		score.value = 3;
	}
}

// 验证注册表单
function validateRegisterForm() {
	clearErrors();

	// 验证用户名
	if (!registerForm.value.username) {
		usernameError.value = '请输入账号名';
		return false;
	}

	// 验证联系人
	if (!registerForm.value.contact) {
		contactError.value = '请输入企业联系人';
		return false;
	}

	// 验证企业名称
	if (!registerForm.value.name) {
		nameError.value = '请选择工商企业';
		return false;
	}

	// 验证密码（仅当不是已有账号时）
	if (!phoneData.value.username) {
		if (!registerForm.value.password) {
			passwordError.value = '请输入密码';
			return false;
		}

		if (score.value < 2) {
			passwordError.value = '密码强度不足，请使用字母、数字和符号两种以上组合';
			return false;
		}

		if (!registerForm.value.password2) {
			password2Error.value = '请再次输入密码';
			return false;
		}

		if (registerForm.value.password !== registerForm.value.password2) {
			password2Error.value = '两次输入的密码不一致';
			return false;
		}
	}

	return true;
}

// 执行注册操作
async function handleRegister() {
	if (!validateRegisterForm()) return;

	inProgress.value = true;

	protect(async () => {
		try {
			const { password2, ...restRegisterData } = registerForm.value;
			const registerData = {
				...restRegisterData,
				password: encryptPassword(registerForm.value.password),
			};

			const res = await apiRequest({
				url: 'admin/register',
				method: 'POST',
				parse: 0,
				data: registerData,
			});

			if (res.code === 0) {
				step.value = 3;

				// 3秒后自动跳转到登录页面
				setTimeout(() => {
					backToLogin();
				}, 3000);
			} else {
				uni.showToast({
					title: res.msg || '注册失败',
					icon: 'none',
				});
			}
		} catch (error: any) {
			uni.showToast({
				title: error.msg || '注册失败',
				icon: 'none',
			});
		} finally {
			inProgress.value = false;
		}
	});
}

// 组件卸载时清除定时器
onUnmounted(() => {
	if (timer.value) {
		clearInterval(timer.value);
		timer.value = null;
	}
});
</script>

<style lang="scss" scoped>
.register-page-container {
	width: 100%;
	min-height: calc(100vh - 84rpx) !important;
	height: auto;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 0 0;
	overflow: hidden;
	box-sizing: border-box;
}

.logo-container {
	// margin-bottom: 40rpx;
	text-align: center;
}

.logo-image {
	width: 320rpx;
	height: 320rpx;
}

.register-container {
	width: 650rpx;
	padding: 0 40rpx;
	background-color: transparent;
	display: flex;
	flex-direction: column;
}

/* 步骤条样式 */
.steps-container {
	margin-bottom: 20rpx;
}

.steps-wrapper {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.step-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	z-index: 1;
	flex: 1;
}

.step-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 28rpx;
	margin-bottom: 16rpx;
	border: 2rpx solid #ddd;
	position: relative;
	z-index: 2;
}

.step-icon.active {
	background-color: #e43a3d;
	color: #fff;
	border-color: #e43a3d;
}

.step-icon.completed {
	background-color: #07c160;
	color: #fff;
	border-color: #07c160;
}

.step-label {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

.step-label.active {
	color: #333;
	font-weight: 500;
}

.step-line {
	position: absolute;
	width: 132rpx;
	top: 25rpx;
	left: 70%;
	right: 0;
	height: 2rpx;
	background-color: #ddd;
	z-index: 0;
}

.step-line.active {
	background-color: #07c160;
}

/* 表单样式 */
.register-form {
	margin-top: 30rpx;
	background-color: transparent;
}

.input-item {
	padding: 10rpx 0;
	// margin-bottom: 10rpx;
}

.phone-input-container,
.account-input-container {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	padding: 0 40rpx;
	border: none;
	height: 90rpx;
}

.account-input-container {
	:deep(.wd-input__icon) {
		background: transparent;
	}
}

.phone-input {
	width: 100%;
	background-color: transparent;
}

.code-container {
	background-color: #f5f5f5;
	border-radius: 8rpx;
	height: 90rpx;
	padding-left: 40rpx;
	border: none;
	display: flex;
	align-items: center;
}

.verification-input-wrap {
	flex: 1;
	max-width: 65%;
	height: 100%;
	display: flex;
	align-items: center;
	border: none;

	:deep(.wd-input__inner) {
		padding-right: 40rpx;
		height: 90rpx;
		line-height: 90rpx;
	}

	:deep(.wd-input__suffix) {
		right: 0;
	}
}

.get-code-btn {
	font-size: 28rpx;
	color: #e43a3d;
	white-space: nowrap;
	text-align: center;
	flex-shrink: 0;
	padding: 0 20rpx 0 30rpx;
	border-left: 1px solid #ddd;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: transparent;
	margin-right: 10rpx;

	&.disabled {
		color: #999;
	}
}

.custom-input {
	background-color: #f5f5f5;
	width: 100%;

	:deep(.wd-input::after) {
		display: none;
	}

	:deep(.wd-input__inner) {
		border: none;
		border-radius: 0;
		padding-left: 0;
		padding-right: 40rpx;
		font-size: 32rpx;
		height: 90rpx;
		line-height: 90rpx;
		color: #333;
		background-color: transparent !important;
	}

	:deep(.wd-input__prefix),
	:deep(.wd-input__suffix) {
		color: #999;
		background-color: transparent;
	}

	:deep(.wd-input__suffix) {
		right: 0;
		background-color: transparent;
	}

	:deep(.wd-input__clear) {
		background-color: transparent;
	}
}

.input-prefix {
	width: 116rpx;
	display: block;
	color: #666;
	font-size: 28rpx;
	padding-right: 20rpx;
	text-align-last: justify;
}

.register-button {
	height: 90rpx;
	border-radius: 45rpx;
	font-size: 32rpx;
	background-color: #e43a3d !important;
	border-color: #e43a3d !important;
	// margin-top: 30rpx;
}

.agreement-checkbox {
	display: flex;
	flex-direction: row;

	:deep(.wd-checkbox__shape) {
		transform: scale(0.8);
		border-radius: 50%;
	}

	:deep(.wd-checkbox) {
		display: flex;
		align-items: center;
		font-size: 24rpx;
	}

	:deep(.wd-checkbox__input.is-checked .wd-checkbox__shape) {
		background-color: #e43a3d;
		border-color: #e43a3d;
	}
}

.error-tip {
	color: #f56c6c;
	font-size: 24rpx;
	margin-top: 10rpx;
	margin-left: 10rpx;
	min-height: 30rpx;
	display: block;
}

.warning-tip {
	color: #e6a23c;
}

.success-tip {
	color: #67c23a;
}

.text-primary {
	color: #0082f0;
}

/* 协议弹窗样式 */
.agreement-popup {
	color: black;
	width: 400rpx;
	height: 400rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 40rpx;
	border-radius: 32rpx;
}

.agreement-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.agreement-title {
	font-size: 34rpx;
	font-weight: 500;
}

.agreement-close {
	padding: 10rpx;
}

.agreement-content {
	flex: 1;
	overflow: hidden;
	position: relative;
}

.agreement-scroll {
	height: 100%;
	padding: 30rpx;
}

.loading-container,
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.loading-text {
	margin-top: 20rpx;
	color: #999;
}

.error-container {
	color: #f56c6c;
}

.agreement-footer {
	padding: 30rpx;
	border-top: 1px solid #f0f0f0;
}

.agree-button {
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 45rpx;
	background-color: #e43a3d !important;
	border-color: #e43a3d !important;
}

/* 成功页面样式 */
.success-container {
	padding: 60rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.success-icon {
	margin-bottom: 40rpx;
}

.success-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.success-desc {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 60rpx;
}

.mt-4 {
	margin-top: 16rpx;
}
</style>
