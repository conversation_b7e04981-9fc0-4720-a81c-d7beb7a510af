<template>
	<view class="info-block-wrapper">
		<view class="info-block" @click="handleClick">
			<InfoItem
				v-if="props.title"
				:title="props.title"
				:value="props.value"
				:unit="props.unit"
				:custom="props.custom"
				:icon="props.icon"
				:bg="props.bg"
				:fg="props.fg"
				:badgeValue="props.badgeValue"
			>
				<slot />
			</InfoItem>
			<view v-else class="flex flex-row">
				<slot />
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import InfoItem from './InfoItem.vue';

const props = withDefaults(
	defineProps<{ title?: string; value?: any; unit?: string; custom?: boolean; icon?: string; bg?: string; fg?: string; badgeValue?: number }>(),
	{
		custom: false,
	}
);

const emit = defineEmits<{ click: [] }>();

// #ifdef MP-WEIXIN

defineOptions({
	options: {
		styleIsolation: 'shared',
	},
});

// #endif

function handleClick() {
	emit('click');
}
</script>

<style scoped lang="scss">
.info-block-wrapper {
	padding: 8rpx;
	box-sizing: border-box;

	.info-block {
		background: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
		border: 1rpx solid #f0f0f0;
		transition: all 0.2s ease;
		width: 100%;
		box-sizing: border-box;

		&:active {
			transform: scale(0.98);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		}
	}
}

// 处理flex布局中的尺寸
.w-full .info-block-wrapper {
	width: 100%;
}

.w-half .info-block-wrapper {
	width: 100%;
}
</style>
