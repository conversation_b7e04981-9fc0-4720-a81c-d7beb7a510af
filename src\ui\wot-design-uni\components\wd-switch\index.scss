@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

@include b(switch) {
	display: inline-block;
	position: relative;
	width: $wot-switch-width;
	height: $wot-switch-height;
	border-radius: $wot-switch-circle-size;
	background: $wot-switch-inactive-color;
	font-size: $wot-switch-size;
	transition: all 0.3s;

	@include e(checkbox) {
		position: absolute;
		width: 0;
		height: 0;
		opacity: 0;
		margin: 0;
	}
	@include e(circle) {
		box-sizing: border-box;
		position: absolute;
		display: inline-block;
		width: $wot-switch-circle-size;
		height: $wot-switch-circle-size;
		top: 2px;
		left: 2px;
		background: #fff;
		border-radius: 50%;
		transition: left 0.3s ease-out;
		box-shadow: 0 2px 4px 0 $wot-switch-inactive-shadow-color;

		&::after {
			position: absolute;
			content: '';
			width: calc(200% - 2px);
			height: calc(200% - 2px);
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%) scale(0.5);
			border: 1px solid $wot-switch-border-color;
			border-radius: 50%;
		}
	}
	@include when(checked) {
		background: $wot-switch-active-color;
		border-color: $wot-switch-active-color;

		.wd-switch__circle {
			left: calc($wot-switch-width - $wot-switch-circle-size - 2px);
			box-shadow: 0 2px 4px 0 $wot-switch-active-shadow-color;
		}
	}
	@include when(disabled) {
		opacity: 0.5;
	}
}
