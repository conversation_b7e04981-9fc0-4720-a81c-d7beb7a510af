<!-- 首页 -->
<template>
	<DIWAppPage>
		<view class="home-container">
			<!-- 顶部区域（包含背景图片） -->
			<HomeTopSection :need-refresh="needRefresh" />
			<!-- 通知区域 -->
			<HomeNotification :need-refresh="needRefresh" />

			<!-- 异常报警 -->
			<HomeAlarmList :need-refresh="needRefresh" />

			<!-- 打卡区域 -->
			<HomeCheckIn :shift-schedule-list="shiftScheduleList" :need-refresh="needRefresh" />

			<!-- 我的看台 -->
			<HomeMyWatch :shift-schedule-list="shiftScheduleList" :need-refresh="needRefresh" />

			<!-- 细纱断头 -->
			<HomeFineSpinningBreak :shift-schedule-list="shiftScheduleList" :need-refresh="needRefresh" />
			<!-- 我的生活 -->
			<HomeMyLife :need-refresh="needRefresh" />
			<!-- 智能分析通知 -->
			<HomeSmartAnalysis :need-refresh="needRefresh" />

			<!-- 浮动按钮  故障上报 -->
			<FaultReportFloat />
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import HomeTopSection from './components/home/<USER>';
import HomeNotification from './components/home/<USER>';
import HomeAlarmList from './components/home/<USER>';
import HomeCheckIn from './components/home/<USER>';
import HomeMyWatch from './components/home/<USER>';
import HomeFineSpinningBreak from './components/home/<USER>';
import HomeMyLife from './components/home/<USER>';
import HomeSmartAnalysis from './components/home/<USER>';
import FaultReportFloat from '@/mod/main/components/home/<USER>';

const { sessionInfo, navigateTo, apiGet, protect } = useFramework();

const jumpFlag = ref(false);
const shiftScheduleList = ref([]);
const needRefresh = ref(false);

// 数据加载函数
async function loadData() {
	return await protect(async () => {
		// 获取截止到今天工厂排班列表
		const d = await apiGet('mesmultidata/DiwShiftSchedule/hisToTodaylist');
		shiftScheduleList.value = d || [];
		console.log('工厂排班数据加载完成:', d);
	});
}

// 下拉刷新处理
onPullDownRefresh(async () => {
	await loadData();
	needRefresh.value = true;
	uni.stopPullDownRefresh();
});

onShow(() => {
	if (!sessionInfo.value) {
		if (!jumpFlag.value) {
			jumpFlag.value = true;
			nextTick(() => {
				navigateTo('/mod/main/login');
			});
		} else {
			jumpFlag.value = false;
			navigateTo({ url: '/mod/mall/home', mode: 'switchTab' });
		}
	} else {
		jumpFlag.value = true;
	}
});

onMounted(async () => {
	await loadData();
});
</script>

<style lang="scss" scoped>
.home-container {
	min-height: 100vh;
	width: 750rpx;
	overflow: hidden;
	background-image: url('/static/icons/<EMAIL>');
	display: flex;
	flex-direction: column;
	align-items: center;
	background-size: 750rpx 1624rpx;
	background-repeat: no-repeat;
	background-position: top center;
	background-color: #f1f5fb;
	box-sizing: content-box;
	padding-bottom: 28rpx;
}
</style>
