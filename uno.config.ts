import type { Postprocessor } from 'unocss';
import { defineConfig, presetUno, presetIcons, transformerVariantGroup, transformerDirectives } from 'unocss';

const presets = [
	presetUno(),
	presetIcons({
		collections: {},
	}),
];

const postprocess: Postprocessor[] = [];

if (process.env.UNI_PLATFORM === 'mp-weixin') {
	const RE_SPACE = /^\.space-.*$/;

	postprocess.push((util) => {
		if (RE_SPACE.test(util.selector)) {
			console.log('discard', util);
			util.entries = [];
		} else if (util.selector === '.\\?') {
			console.log('discard', util);
			// https://github.com/unocss/unocss/blob/main/packages-presets/preset-mini/src/_rules/question-mark.ts
			util.selector = '__WARNING__QUESTIONMARK__DISCARDED__';
		} else if (util.selector.indexOf('%') >= 0 || util.selector.indexOf('\\') >= 0) {
			console.log('discard', util);
			util.entries = [];
		}
	});
}

export default defineConfig({
	content: {
		pipeline: {
			include: [/\.(vue|svelte|[jt]sx|mdx?|astro|elm|php|phtml|html)($|\?)/, 'src/menu.ts'],
		},
	},
	presets,
	postprocess,
	transformers: [transformerVariantGroup(), transformerDirectives()],
});
