<template>
	<DIWAppPage title="%notificationTitle%">
		<view class="notification-page">
			<!-- Tab选项卡 -->
			<view class="tab-container">
				<view class="tab-list">
					<view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }" @click="switchTab(index)">
						<text class="tab-text">{{ tab.name }}</text>
					</view>
				</view>
			</view>

			<!-- 搜索框 -->
			<view class="search-container">
				<view class="search-box">
					<wd-icon name="search" size="36rpx" color="#86909C" />
					<wd-input v-model="searchKeyword" placeholder="%searchPlaceholder%" class="search-input" @input="onSearchInput" clearable />
					<wd-button type="primary" size="small" class="search-btn" @click="handleSearch">
						{{ t('search') }}
					</wd-button>
				</view>
			</view>

			<!-- 消息列表 -->
			<DIWScrollView>
				<DIWListView ref="listView" :meta="listViewMeta">
					<template #default="{ data }">
						<view class="message-item" @click="handleMessageClick(data)">
							<view class="message-icon">
								<image class="icon-image" src="/static/icons/<EMAIL>" mode="aspectFit" />
								<view v-if="!data.isRead" class="unread-dot"></view>
							</view>
							<view class="message-content">
								<text class="message-title">{{ data.title }}</text>
								<text class="message-date">{{ data.createTime || '-' }}</text>
							</view>
						</view>
					</template>

					<template #empty>
						<view class="empty-state">
							<text class="empty-text">{{ t('noData') }}</text>
						</view>
					</template>
				</DIWListView>
			</DIWScrollView>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();

// 使用框架功能
const { apiGet, navigateTo, protect } = useFramework();

// 响应式数据
const listView = ref();
const activeTab = ref(0);
const searchKeyword = ref('');

// Tab数据
const tabs = ref([
	{ name: t('all'), value: 'all' },
	{ name: t('group'), value: 'group' },
	{ name: t('park'), value: 'park' },
]);

// 列表视图元数据
const listViewMeta = computed(() => {
	return defineDIWListViewMeta({
		async loadData(pageIndex) {
			const size1 = 20;
			const size2 = 10;
			const isReload = pageIndex === undefined;

			let current = isReload ? 1 : pageIndex;
			let size = isReload ? size1 : size2;

			const params = {
				current,
				size,
				descs: 'create_time',
				type: tabs.value[activeTab.value].value,
				keyword: searchKeyword.value,
			};

			try {
				const res = await apiGet({ url: 'system/notification/page', params });

				if (isReload) {
					return {
						hasMore: res && res.total ? res.total > size : false,
						items: res && res.records ? res.records : [],
						trackInfo: 1 + size1 / size2,
					};
				}

				return {
					hasMore: res && res.total ? res.total > current * size : false,
					items: res && res.records ? res.records : [],
					trackInfo: current + 1,
				};
			} catch (error) {
				console.error('加载消息列表失败', error);
				return {
					hasMore: false,
					items: getMockData(),
					trackInfo: 1,
				};
			}
		},
	});
});

// 模拟数据（用于开发测试）
function getMockData() {
	return [
		{
			id: 1,
			title: '关于下发《OMS项目模块化开发与考核管理办法》的通知',
			createTime: '2025.7.12',
			isRead: false,
			type: 'group',
		},
		{
			id: 2,
			title: '关于下发《数智世界集团综合园区开发重要事项决策办法》的通知',
			createTime: '2025.7.11',
			isRead: false,
			type: 'group',
		},
		{
			id: 3,
			title: '关于下发《上海数智世界工业科技集团有限公司请假管理办法》的通知',
			createTime: '2025.7.10',
			isRead: true,
			type: 'group',
		},
		{
			id: 4,
			title: '香格一厂104号细纱机存在安全隐患，请当班人员立即上报组长',
			createTime: '2025.7.09',
			isRead: false,
			type: 'park',
		},
		{
			id: 5,
			title: '香格二厂房停电，电路维修请当班人员立即上报组长',
			createTime: '2025.7.08',
			isRead: true,
			type: 'park',
		},
		{
			id: 6,
			title: '胡娜、张丽(看台3)：张丽断头423次但耗时2080秒',
			createTime: '2025.7.07',
			isRead: false,
			type: 'park',
		},
		{
			id: 7,
			title: '关于下发《OMS项目模块化开发与考核管理办法》的通知',
			createTime: '2025.7.06',
			isRead: true,
			type: 'group',
		},
	];
}

// 切换Tab
function switchTab(index: number) {
	activeTab.value = index;
	// 重新加载数据
	nextTick(() => {
		if (listView.value) {
			listView.value.reload();
		}
	});
}

// 搜索输入
function onSearchInput() {
	// 防抖处理
	clearTimeout(searchTimer);
	searchTimer = setTimeout(() => {
		handleSearch();
	}, 500);
}

let searchTimer: number;

// 执行搜索
function handleSearch() {
	if (listView.value) {
		listView.value.reload();
	}
}

// 处理消息点击
function handleMessageClick(item: any) {
	protect(async () => {
		// 标记为已读
		if (!item.isRead) {
			try {
				await apiGet({ url: `system/notification/read/${item.id}` });
				item.isRead = true;
			} catch (error) {
				console.error('标记已读失败', error);
			}
		}

		// 跳转到消息详情页
		await navigateTo({
			url: `/mod/message/notificationDetail?id=${item.id}`,
		});
	});
}

// 页面加载时刷新数据
onMounted(() => {
	if (listView.value) {
		listView.value.reload();
	}
});

// 下拉刷新
onPullDownRefresh(() => {
	if (listView.value) {
		listView.value.reload();
	}
	uni.stopPullDownRefresh();
});
</script>

<style lang="scss" scoped>
.notification-page {
	background-color: #f5f7fa;
	min-height: 100vh;
}

/* Tab选项卡样式 */
.tab-container {
	background-color: #ffffff;
	padding: 0 32rpx;
}

.tab-list {
	display: flex;
	align-items: center;
	border-bottom: 2rpx solid #e5e6eb;
}

.tab-item {
	position: relative;
	padding: 32rpx 48rpx;
	margin-right: 48rpx;

	&.active {
		.tab-text {
			color: #1d2129;
			font-weight: 600;
		}

		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 48rpx;
			height: 6rpx;
			background-color: #0082f0;
			border-radius: 3rpx;
		}
	}
}

.tab-text {
	font-size: 32rpx;
	color: #86909c;
	transition: color 0.3s;
}

/* 搜索框样式 */
.search-container {
	background-color: #ffffff;
	padding: 24rpx 32rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f7f8fa;
	border-radius: 48rpx;
	padding: 0 32rpx;
	height: 88rpx;
}

.search-input {
	flex: 1;
	margin: 0 24rpx;
	font-size: 28rpx;
	border: none;
	background: transparent;
}

.search-btn {
	flex-shrink: 0;
	height: 64rpx;
	padding: 0 32rpx;
	border-radius: 32rpx;
	font-size: 28rpx;
}

/* 消息列表样式 */
.message-item {
	display: flex;
	align-items: center;
	background-color: #ffffff;
	padding: 32rpx;
	margin-bottom: 2rpx;
	position: relative;

	&:active {
		background-color: #f7f8fa;
	}
}

.message-icon {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	margin-right: 24rpx;
	flex-shrink: 0;
}

.icon-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #e8f3ff;
}

.unread-dot {
	position: absolute;
	top: 0;
	right: 0;
	width: 20rpx;
	height: 20rpx;
	background-color: #f62323;
	border-radius: 50%;
	border: 4rpx solid #ffffff;
}

.message-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.message-title {
	font-size: 28rpx;
	color: #1d2129;
	line-height: 1.5;
	margin-bottom: 12rpx;
	/* 限制显示行数 */
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.message-date {
	font-size: 24rpx;
	color: #86909c;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 120rpx 0;
}

.empty-text {
	font-size: 28rpx;
	color: #86909c;
}
</style>
