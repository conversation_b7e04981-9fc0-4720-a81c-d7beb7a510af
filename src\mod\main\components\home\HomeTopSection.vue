<template>
	<view class="home-top-section">
		<!-- 用户信息区域 -->
		<view class="user-info">
			<view class="factory-name">{{ userInfo?.factoryName }}</view>
			<view class="user-avatar-wrapper">
				<image class="user-avatar" :src="userInfo?.oaAvatar" mode="aspectFill" />
				<view class="user-level">{{ userInfo?.level || 'L1级' }}</view>
			</view>
		</view>

		<!-- 收入统计 -->
		<view class="income-stats" @click="handleIncomeClick">
			<!-- 主要收入显示 -->
			<view class="main-income">
				<view class="income-amount">
					<view class="income-amount-wrapper">
						<text class="amount">{{ incomeData.currentShiftIncome }}</text>
						<text class="unit">元</text>
					</view>
					<view class="badge">第{{ incomeData.currentShiftRank }}名</view>
				</view>
				<text class="income-label">{{ t('currentShiftEstimatedIncome') }}</text>
			</view>

			<!-- 收入统计网格 -->
			<view class="income-grid">
				<view class="income-item">
					<view class="income-item-wrapper">
						<text class="value">{{ incomeData.monthlyEstimatedSalary || 0 }}</text>
						<text class="unit">元</text>
					</view>
					<text class="label">{{ t('monthlyEstimatedSalary') }}</text>
				</view>
				<!-- 分隔线 -->
				<view class="income-divider"></view>
				<view class="income-item">
					<view class="income-item-wrapper">
						<text class="value">{{ incomeData.lastMonthSalary || 0 }}</text>
						<text class="unit">元</text>
					</view>
					<text class="label">{{ t('lastMonthSalary') }}</text>
				</view>
				<view class="income-divider"></view>
				<view class="income-item">
					<view class="income-item-wrapper">
						<text class="value">{{ incomeData.monthlyBonus || 0 }}</text>
						<text class="unit">元</text>
					</view>
					<text class="label">{{ t('monthlyRewardPunishment').split('/')[0] }}</text>
				</view>
				<view class="income-divider"></view>
				<view class="income-item">
					<view class="income-item-wrapper">
						<text class="value">{{ incomeData.monthlyPenalty || 0 }}</text>
						<text class="unit">元</text>
					</view>
					<text class="label">{{ t('monthlyRewardPunishment').split('/')[1] }}</text>
				</view>
			</view>
		</view>

		<!-- 产量统计 -->
		<view class="production-stats">
			<view class="production-stats-innner">
				<view class="card-header">
					<view class="title-wrapper">
						<view class="title-indicator"></view>
						<text class="title">{{ t('productionStatistics') }}</text>
						<view class="badge">第{{ productionData.rank }}名</view>
					</view>
					<wd-icon name="arrow-right" size="24rpx" color="#86909C" @click="handleProductionClick" />
				</view>

				<view class="stats-content">
					<view class="stat-item">
						<text class="value">{{ productionData.currentShiftOutput }}</text>
						<text class="label">{{ t('currentShiftOutput') }}</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="value">{{ productionData.monthlyOutput }}</text>
						<text class="label">{{ t('monthlyOutput') }}</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="value">{{ productionData.averagePlatforms }}</text>
						<text class="label">{{ t('monthlyAverageStands') }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();
const props = defineProps<{
	needRefresh: boolean;
}>();

// 使用框架功能
const { navigateTo, protect, sessionInfo, apiGet } = useFramework();
console.log(sessionInfo.value?.userInfo);

// 模拟用户数据
const userInfo = computed(() => {
	return sessionInfo.value?.userInfo;
});
// 模拟收入数据
const incomeData = ref({
	currentShiftIncome: 326.2,
	currentShiftRank: 3,
	monthlyEstimatedSalary: 7528.6,
	lastMonthSalary: 7246.5,
	monthlyBonus: 320,
	monthlyPenalty: -50,
});

// 模拟产量数据
const productionData = ref({
	currentShiftOutput: 152.68,
	monthlyOutput: 1566.32,
	averagePlatforms: 5.5,
	rank: 155,
});

// 处理收入点击事件
function handleIncomeClick() {
	protect(async () => {
		await navigateTo({
			url: '/mod/salary/salaryHistory',
		});
	});
}

// 处理产量点击事件
function handleProductionClick() {
	protect(async () => {
		await navigateTo({
			url: '/mod/production/productionHistory',
		});
	});
}

async function loadData() {
	// 工资数据
}

watch(
	() => props.needRefresh,
	async (newVal) => {
		if (newVal) {
			await loadData();
		}
	}
);

onMounted(async () => {
	await loadData();
});
</script>

<style lang="scss" scoped>
.home-top-section {
	padding: 44rpx 0 0 0;
	display: flex;
	flex-direction: column;
}

/* 用户信息样式 */
.user-info {
	margin-top: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 24rpx 0rpx 24rpx;
	height: 72rpx;
	.factory-name {
		height: 24rpx;

		font-size: 28rpx;
		font-weight: normal;
		line-height: 24rpx;
		letter-spacing: 0em;
		color: #ffffff;
	}

	.user-avatar-wrapper {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;

		.user-avatar {
			position: absolute;
			right: 86rpx;
			width: 72rpx;
			height: 72rpx;
			border-radius: 50%;
			border: 4rpx solid rgba(255, 255, 255, 1);
		}

		.user-level {
			width: 100rpx;
			height: 36rpx;
			border-radius: 0px 24px 24px 0px;
			background: linear-gradient(270deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.8) 100%);

			font-size: 24rpx;
			font-weight: normal;
			line-height: 24rpx;
			letter-spacing: 0em;

			color: #ffffff;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

/* 收入统计样式 */
.income-stats {
	margin-top: 18rpx;
	padding: 0 24rpx 0rpx 24rpx;
	.main-income {
		text-align: center;
		color: #ffffff;
		display: flex;
		flex-direction: column;
		align-items: center;

		.income-amount {
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			line-height: normal;
			text-align: center;
			letter-spacing: 0em;
			color: #ffffff;
			.income-amount-wrapper {
				.amount {
					font-weight: 700;
					font-size: 56rpx;
				}

				.unit {
					font-weight: 700;
					font-size: 28rpx;
				}
			}

			.badge {
				margin-left: 18rpx;
				height: 32rpx;
				border-radius: 12rpx 20rpx 20rpx 2rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #ffffff;
				color: #0082f0;
				font-size: 20rpx;
				padding: 0 6rpx;
			}
		}

		.income-label {
			margin-top: 8rpx;
			font-size: 24rpx;
			font-weight: normal;
			line-height: normal;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0em;
		}
	}
}

.income-grid {
	margin-top: 36rpx;
	padding: 0 36rpx;
	display: flex;
	justify-content: space-between;
	font-size: 20rpx;
	font-weight: bold;
	line-height: normal;
	text-align: center;
	display: flex;
	align-items: center;
	letter-spacing: 0em;
	color: #ffffff;

	.income-item {
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;

		.income-item-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			.value {
				font-weight: 700;
				font-size: 32rpx;
			}

			.unit {
				font-weight: 700;
				font-size: 20rpx;
			}
		}

		.label {
			margin-top: 4rpx;
			font-size: 20rpx;
			font-weight: normal;
			line-height: normal;
			text-align: center;
			display: flex;
			align-items: center;
			letter-spacing: 0em;
			color: #ffffff;
		}
	}
	.income-divider {
		display: flex;
		width: 2rpx;
		height: 48rpx;
		background-color: #ffffff;
	}
}

/* 产量统计样式 */
.production-stats {
	margin-top: 20rpx;
	width: 750rpx;
	height: 244rpx;
	border-radius: 24rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 750rpx 244rpx;
	background-repeat: no-repeat;
	background-position: 0 0;

	.production-stats-innner {
		width: 702rpx;
		height: 188rpx;
		margin: 0 24rpx;

		.card-header {
			padding: 19rpx 36rpx 0 36rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.title-wrapper {
				display: flex;
				align-items: center;

				.title-indicator {
					width: 8rpx;
					height: 32rpx;
					background: #0082f0;
					border-radius: 4rpx;
					margin-right: 16rpx;
				}

				.title {
					font-size: 30rpx;
					font-weight: normal;
					line-height: 24rpx;
					text-align: center;
					letter-spacing: 0em;
					color: #1d2129;
				}

				.badge {
					margin-left: 16rpx;
					border-radius: 12rpx 20rpx 20rpx 2rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					padding: 0 6rpx;
					background: rgba(0, 130, 240, 0.7);
					color: #ffffff;
					font-size: 20rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: 0em;
					padding: 1rpx 4rpx;
				}
			}
		}

		.stats-content {
			display: flex;
			justify-content: space-around;
			align-items: center;
			margin-top: 24rpx;
			padding: 0 36rpx;

			.stat-item {
				text-align: center;
				display: flex;
				flex-direction: column;
				align-items: center;

				.value {
					font-size: 36rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: 0em;
					color: #1d2129;
				}

				.label {
					margin-top: 12rpx;

					font-size: 24rpx;
					font-weight: normal;
					line-height: normal;
					letter-spacing: 0em;
					color: #86909c;
				}
			}
			.stat-divider {
				width: 2rpx;
				height: 68rpx;
				background: #cfd3d7;
			}
		}
	}
}
</style>
