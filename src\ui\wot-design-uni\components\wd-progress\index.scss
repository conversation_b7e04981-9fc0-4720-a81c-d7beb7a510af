@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(progress) {
		@include e(label) {
			color: $wot-dark-color3;
		}
	}
}

@include b(progress) {
	width: 100%;
	height: $wot-progress-height;
	display: flex;
	align-items: center;
	padding: $wot-progress-padding;

	@include e(outer) {
		display: block;
		position: relative;
		flex: 1;
		height: $wot-progress-height;
		border-radius: calc($wot-progress-height / 2);
		background: $wot-progress-bg;
	}
	@include e(inner) {
		display: block;
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		border-radius: calc($wot-progress-height / 2);
		background: $wot-progress-color;
		transition-property: width;
		transition-timing-function: linear;
		font-size: $wot-progress-icon-fs;

		@include when(danger) {
			background: $wot-progress-danger-color;
		}
		@include when(success) {
			background: $wot-progress-success-color;
		}
		@include when(warning) {
			background: $wot-progress-warning-color;
		}
	}
	@include edeep(label) {
		width: 30px;
		margin-left: 9px;
		color: $wot-progress-label-color;
		font-size: $wot-progress-label-fs;
	}
	@include edeep(icon) {
		font-size: $wot-progress-icon-fs;

		@include when(danger) {
			color: $wot-progress-danger-color;
		}
		@include when(success) {
			color: $wot-progress-success-color;
		}
		@include when(warning) {
			color: $wot-progress-warning-color;
		}
	}
}
