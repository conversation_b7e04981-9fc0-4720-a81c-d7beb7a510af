<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4929664" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">审核不通过</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">工作台</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">库存管理</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">调拨单</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">调拨出库</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">待认证</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69e;</span>
                <div class="name">资料未填写</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b9;</span>
                <div class="name">未解决</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">已解决</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">认证不通过</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72e;</span>
                <div class="name">服务商</div>
                <div class="code-name">&amp;#xe72e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">认证不通过</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">待提货</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74d;</span>
                <div class="name">今日合同签订量</div>
                <div class="code-name">&amp;#xe74d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d4;</span>
                <div class="name">客户待申请开票</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">客户已确认收货</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">待发货</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe684;</span>
                <div class="name">今日客户付款金额</div>
                <div class="code-name">&amp;#xe684;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9fc;</span>
                <div class="name">出库单</div>
                <div class="code-name">&amp;#xe9fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe94d;</span>
                <div class="name">待申请</div>
                <div class="code-name">&amp;#xe94d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">待制单</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">待确认</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xef93;</span>
                <div class="name">待复核</div>
                <div class="code-name">&amp;#xef93;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">待审核合同</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">待申请</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70e;</span>
                <div class="name">合同待审核</div>
                <div class="code-name">&amp;#xe70e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68e;</span>
                <div class="name">待付款</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe884;</span>
                <div class="name">发货锁批货转申请</div>
                <div class="code-name">&amp;#xe884;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">合同调整待确认</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">合同待签约</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe932;</span>
                <div class="name">发票处理</div>
                <div class="code-name">&amp;#xe932;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">锲约锁认证</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c3;</span>
                <div class="name">企业认证</div>
                <div class="code-name">&amp;#xe7c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68d;</span>
                <div class="name">自提信息修改</div>
                <div class="code-name">&amp;#xe68d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9c7;</span>
                <div class="name">等确认收货</div>
                <div class="code-name">&amp;#xe9c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d9;</span>
                <div class="name">收货地址修改</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">货转待签署</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe755;</span>
                <div class="name">银行开户</div>
                <div class="code-name">&amp;#xe755;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c3;</span>
                <div class="name">已发货可提货</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb70;</span>
                <div class="name">锁批申请结果</div>
                <div class="code-name">&amp;#xeb70;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">待确认申请的提货单</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6da;</span>
                <div class="name">购物车</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1748411062850') format('woff2'),
       url('iconfont.woff?t=1748411062850') format('woff'),
       url('iconfont.ttf?t=1748411062850') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-shenhebutongguo"></span>
            <div class="name">
              审核不通过
            </div>
            <div class="code-name">.icon-shenhebutongguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuotai"></span>
            <div class="name">
              工作台
            </div>
            <div class="code-name">.icon-gongzuotai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-navicon-kcgl"></span>
            <div class="name">
              库存管理
            </div>
            <div class="code-name">.icon-navicon-kcgl
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiaobodan"></span>
            <div class="name">
              调拨单
            </div>
            <div class="code-name">.icon-tiaobodan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tiaobochuku"></span>
            <div class="name">
              调拨出库
            </div>
            <div class="code-name">.icon-tiaobochuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dairenzheng"></span>
            <div class="name">
              待认证
            </div>
            <div class="code-name">.icon-dairenzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weitianxie"></span>
            <div class="name">
              资料未填写
            </div>
            <div class="code-name">.icon-weitianxie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weijiejue"></span>
            <div class="name">
              未解决
            </div>
            <div class="code-name">.icon-weijiejue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yijiejue"></span>
            <div class="name">
              已解决
            </div>
            <div class="code-name">.icon-yijiejue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-renzhengbutongguo"></span>
            <div class="name">
              认证不通过
            </div>
            <div class="code-name">.icon-renzhengbutongguo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jishufuwushang"></span>
            <div class="name">
              服务商
            </div>
            <div class="code-name">.icon-jishufuwushang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-renzhengbutongguo1"></span>
            <div class="name">
              认证不通过
            </div>
            <div class="code-name">.icon-renzhengbutongguo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icondaitihuo"></span>
            <div class="name">
              待提货
            </div>
            <div class="code-name">.icon-icondaitihuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kehudingdan"></span>
            <div class="name">
              今日合同签订量
            </div>
            <div class="code-name">.icon-kehudingdan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fapiaokaiju"></span>
            <div class="name">
              客户待申请开票
            </div>
            <div class="code-name">.icon-fapiaokaiju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-querenshouhuo"></span>
            <div class="name">
              客户已确认收货
            </div>
            <div class="code-name">.icon-querenshouhuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daifahuo"></span>
            <div class="name">
              待发货
            </div>
            <div class="code-name">.icon-daifahuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kehushoukuan"></span>
            <div class="name">
              今日客户付款金额
            </div>
            <div class="code-name">.icon-kehushoukuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chukudan"></span>
            <div class="name">
              出库单
            </div>
            <div class="code-name">.icon-chukudan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daishenqing1"></span>
            <div class="name">
              待申请
            </div>
            <div class="code-name">.icon-daishenqing1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daizhidan"></span>
            <div class="name">
              待制单
            </div>
            <div class="code-name">.icon-daizhidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daiqueren"></span>
            <div class="name">
              待确认
            </div>
            <div class="code-name">.icon-daiqueren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daifuhe"></span>
            <div class="name">
              待复核
            </div>
            <div class="code-name">.icon-daifuhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiao-daishenhehetong-moren"></span>
            <div class="name">
              待审核合同
            </div>
            <div class="code-name">.icon-tubiao-daishenhehetong-moren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daishenqing2"></span>
            <div class="name">
              待申请
            </div>
            <div class="code-name">.icon-daishenqing2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hetongdaishenhe"></span>
            <div class="name">
              合同待审核
            </div>
            <div class="code-name">.icon-hetongdaishenhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daifukuan"></span>
            <div class="name">
              待付款
            </div>
            <div class="code-name">.icon-daifukuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baoguofahuo-xianxing"></span>
            <div class="name">
              发货锁批货转申请
            </div>
            <div class="code-name">.icon-baoguofahuo-xianxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hetongdaiqueren"></span>
            <div class="name">
              合同调整待确认
            </div>
            <div class="code-name">.icon-hetongdaiqueren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daiqianyue"></span>
            <div class="name">
              合同待签约
            </div>
            <div class="code-name">.icon-daiqianyue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fapiao"></span>
            <div class="name">
              发票处理
            </div>
            <div class="code-name">.icon-fapiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongzhimima"></span>
            <div class="name">
              锲约锁认证
            </div>
            <div class="code-name">.icon-zhongzhimima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-renzheng"></span>
            <div class="name">
              企业认证
            </div>
            <div class="code-name">.icon-renzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziti"></span>
            <div class="name">
              自提信息修改
            </div>
            <div class="code-name">.icon-ziti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dengdaiquerenshouhuo"></span>
            <div class="name">
              等确认收货
            </div>
            <div class="code-name">.icon-dengdaiquerenshouhuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dizhi"></span>
            <div class="name">
              收货地址修改
            </div>
            <div class="code-name">.icon-dizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daiqianshu"></span>
            <div class="name">
              货转待签署
            </div>
            <div class="code-name">.icon-daiqianshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yinhangkaihu"></span>
            <div class="name">
              银行开户
            </div>
            <div class="code-name">.icon-yinhangkaihu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ketihuo"></span>
            <div class="name">
              已发货可提货
            </div>
            <div class="code-name">.icon-ketihuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-LIMS_picijieguoluru"></span>
            <div class="name">
              锁批申请结果
            </div>
            <div class="code-name">.icon-LIMS_picijieguoluru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daishenqing"></span>
            <div class="name">
              待确认申请的提货单
            </div>
            <div class="code-name">.icon-daishenqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gouwuche"></span>
            <div class="name">
              购物车
            </div>
            <div class="code-name">.icon-gouwuche
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenhebutongguo"></use>
                </svg>
                <div class="name">审核不通过</div>
                <div class="code-name">#icon-shenhebutongguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotai"></use>
                </svg>
                <div class="name">工作台</div>
                <div class="code-name">#icon-gongzuotai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-navicon-kcgl"></use>
                </svg>
                <div class="name">库存管理</div>
                <div class="code-name">#icon-navicon-kcgl</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiaobodan"></use>
                </svg>
                <div class="name">调拨单</div>
                <div class="code-name">#icon-tiaobodan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tiaobochuku"></use>
                </svg>
                <div class="name">调拨出库</div>
                <div class="code-name">#icon-tiaobochuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dairenzheng"></use>
                </svg>
                <div class="name">待认证</div>
                <div class="code-name">#icon-dairenzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weitianxie"></use>
                </svg>
                <div class="name">资料未填写</div>
                <div class="code-name">#icon-weitianxie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weijiejue"></use>
                </svg>
                <div class="name">未解决</div>
                <div class="code-name">#icon-weijiejue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yijiejue"></use>
                </svg>
                <div class="name">已解决</div>
                <div class="code-name">#icon-yijiejue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renzhengbutongguo"></use>
                </svg>
                <div class="name">认证不通过</div>
                <div class="code-name">#icon-renzhengbutongguo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jishufuwushang"></use>
                </svg>
                <div class="name">服务商</div>
                <div class="code-name">#icon-jishufuwushang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renzhengbutongguo1"></use>
                </svg>
                <div class="name">认证不通过</div>
                <div class="code-name">#icon-renzhengbutongguo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icondaitihuo"></use>
                </svg>
                <div class="name">待提货</div>
                <div class="code-name">#icon-icondaitihuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kehudingdan"></use>
                </svg>
                <div class="name">今日合同签订量</div>
                <div class="code-name">#icon-kehudingdan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fapiaokaiju"></use>
                </svg>
                <div class="name">客户待申请开票</div>
                <div class="code-name">#icon-fapiaokaiju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-querenshouhuo"></use>
                </svg>
                <div class="name">客户已确认收货</div>
                <div class="code-name">#icon-querenshouhuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daifahuo"></use>
                </svg>
                <div class="name">待发货</div>
                <div class="code-name">#icon-daifahuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kehushoukuan"></use>
                </svg>
                <div class="name">今日客户付款金额</div>
                <div class="code-name">#icon-kehushoukuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chukudan"></use>
                </svg>
                <div class="name">出库单</div>
                <div class="code-name">#icon-chukudan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daishenqing1"></use>
                </svg>
                <div class="name">待申请</div>
                <div class="code-name">#icon-daishenqing1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daizhidan"></use>
                </svg>
                <div class="name">待制单</div>
                <div class="code-name">#icon-daizhidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daiqueren"></use>
                </svg>
                <div class="name">待确认</div>
                <div class="code-name">#icon-daiqueren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daifuhe"></use>
                </svg>
                <div class="name">待复核</div>
                <div class="code-name">#icon-daifuhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiao-daishenhehetong-moren"></use>
                </svg>
                <div class="name">待审核合同</div>
                <div class="code-name">#icon-tubiao-daishenhehetong-moren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daishenqing2"></use>
                </svg>
                <div class="name">待申请</div>
                <div class="code-name">#icon-daishenqing2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hetongdaishenhe"></use>
                </svg>
                <div class="name">合同待审核</div>
                <div class="code-name">#icon-hetongdaishenhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daifukuan"></use>
                </svg>
                <div class="name">待付款</div>
                <div class="code-name">#icon-daifukuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baoguofahuo-xianxing"></use>
                </svg>
                <div class="name">发货锁批货转申请</div>
                <div class="code-name">#icon-baoguofahuo-xianxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hetongdaiqueren"></use>
                </svg>
                <div class="name">合同调整待确认</div>
                <div class="code-name">#icon-hetongdaiqueren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daiqianyue"></use>
                </svg>
                <div class="name">合同待签约</div>
                <div class="code-name">#icon-daiqianyue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fapiao"></use>
                </svg>
                <div class="name">发票处理</div>
                <div class="code-name">#icon-fapiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongzhimima"></use>
                </svg>
                <div class="name">锲约锁认证</div>
                <div class="code-name">#icon-zhongzhimima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-renzheng"></use>
                </svg>
                <div class="name">企业认证</div>
                <div class="code-name">#icon-renzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziti"></use>
                </svg>
                <div class="name">自提信息修改</div>
                <div class="code-name">#icon-ziti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dengdaiquerenshouhuo"></use>
                </svg>
                <div class="name">等确认收货</div>
                <div class="code-name">#icon-dengdaiquerenshouhuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dizhi"></use>
                </svg>
                <div class="name">收货地址修改</div>
                <div class="code-name">#icon-dizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daiqianshu"></use>
                </svg>
                <div class="name">货转待签署</div>
                <div class="code-name">#icon-daiqianshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yinhangkaihu"></use>
                </svg>
                <div class="name">银行开户</div>
                <div class="code-name">#icon-yinhangkaihu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ketihuo"></use>
                </svg>
                <div class="name">已发货可提货</div>
                <div class="code-name">#icon-ketihuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-LIMS_picijieguoluru"></use>
                </svg>
                <div class="name">锁批申请结果</div>
                <div class="code-name">#icon-LIMS_picijieguoluru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daishenqing"></use>
                </svg>
                <div class="name">待确认申请的提货单</div>
                <div class="code-name">#icon-daishenqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gouwuche"></use>
                </svg>
                <div class="name">购物车</div>
                <div class="code-name">#icon-gouwuche</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
