@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

@include b(notice-bar) {
	display: flex;
	padding: $wot-notice-bar-padding;
	align-items: center;
	font-size: $wot-notice-bar-fs;
	border-radius: $wot-notice-bar-border-radius;
	position: relative;
	box-sizing: border-box;
	@include when(warning) {
		background: $wot-notice-bar-warning-bg;
		color: $wot-notice-bar-warning-color;
	}
	@include when(info) {
		background: $wot-notice-bar-info-bg;
		color: $wot-notice-bar-info-color;
	}
	@include when(danger) {
		background: $wot-notice-bar-danger-bg;
		color: $wot-notice-bar-danger-color;
	}
	@include edeep(prefix) {
		padding-right: 4px;
		font-size: $wot-notice-bar-prefix-size;
	}

	@include edeep(suffix) {
		text-align: center;
		font-size: $wot-notice-bar-close-size;
		display: inline-block;
		background-color: $wot-notice-bar-close-bg;
		color: $wot-notice-bar-close-color;
		padding: 0;
		border-radius: 0px 8px 0px 4px;
		position: absolute;
		right: 0;
		top: 0;
	}
	@include e(wrap) {
		position: relative;
		flex: 1;
		height: $wot-notice-bar-line-height;
		overflow: hidden;
		line-height: $wot-notice-bar-line-height;
	}
	@include e(content) {
		position: absolute;
		white-space: nowrap;
	}
	@include m(ellipse) {
		.wd-notice-bar__content {
			position: static;
			@include lineEllipsis;
		}
	}
	@include m(wrap) {
		.wd-notice-bar__wrap {
			height: auto;
		}
		.wd-notice-bar__content {
			position: static;
			white-space: normal;
		}
	}
}
