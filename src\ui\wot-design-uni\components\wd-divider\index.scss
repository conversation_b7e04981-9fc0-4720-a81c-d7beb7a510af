@use '../common/abstracts/variable' as *;
@use '../common/abstracts/mixin' as *;

.wot-theme-dark {
	@include b(divider) {
		color: $wot-dark-color3;

		@include e(line) {
			background: $wot-dark-color-gray;
		}
	}
}

@include b(divider) {
	position: relative;
	display: flex;
	padding: $wot-divider-padding;
	margin: $wot-divider-margin;
	align-items: center;
	color: $wot-divider-color;
	font-size: $wot-divider-fs;

	&::after,
	&::before {
		flex: 1;
		display: block;
		box-sizing: border-box;
		border-style: solid;
		border-color: $wot-divider-line-color;
		border-width: $wot-divider-line-height 0 0;
	}

	&::before {
		content: '';
	}

	@include m(center, left, right) {
		&::after {
			content: '';
			margin-left: $wot-divider-content-left-margin;
		}

		&::before {
			margin-right: $wot-divider-content-right-margin;
		}
	}

	@include m(left) {
		&::before {
			max-width: $wot-divider-content-left-width;
		}
	}

	@include m(right) {
		&::after {
			max-width: $wot-divider-content-right-width;
		}
	}

	@include when(hairline) {
		&::before,
		&::after {
			transform: scaleY(0.5);
		}
	}

	@include when(dashed) {
		&::before,
		&::after {
			border-style: dashed;
		}
	}

	@include m(vertical) {
		display: inline-block;
		width: $wot-divider-vertical-line-width;
		height: $wot-divider-vertical-height;
		margin: $wot-divider-vertical-content-margin;
		padding: 0;
		vertical-align: middle;

		&::before {
			height: 100%;
			border-width: 0 0 0 $wot-divider-vertical-line-width;
		}

		&::after {
			display: none;
		}

		@include when(hairline) {
			&::before {
				transform: scaleX(0.5);
			}
		}
	}
}
