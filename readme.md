# 目录结构

```bash
├── .prettierrc.js                          # 代码格式化预设文件
├── auto-imports.d.ts
├── components.d.ts
├── dist                                    # 打包输出目录
├── index.html
├── node_modules
├── package.json
├── pnpm-lock.yaml
├── readme.md
├── src
│   ├── App.vue
│   ├── components                          # 通用组件目录，目录组织结构符合 easycom 规范
│   │   ├── DIWAppPage
│   │   │   └── DIWAppPage.vue
│   │   ├── DIWListView
│   │   │   └── DIWListView.vue
│   │   ├── DIWLogin
│   │   │   └── DIWLogin.vue
│   │   ├── DIWProtect
│   │   │   └── DIWProtect.vue
│   │   ├── DIWRoot
│   │   │   └── DIWRoot.vue
│   │   ├── DIWScrollView
│   │   │   └── DIWScrollView.vue
│   │   └── DIWSection
│   │       └── DIWSection.vue
│   ├── config                              # 配置文件目录
│   │   └── appconfig.ts
│   ├── framework                           # 框架代码目录，公共接口都在 index.ts 中，其余文件都是实现细节，不要 import 这些文件
│   │   ├── devel.ts
│   │   ├── error.ts
│   │   ├── fx.ts
│   │   ├── index.ts                        # 只有这一个文件会被 AutoImport 扫描
│   │   ├── nav.ts
│   │   ├── page.ts
│   │   ├── scroll.ts
│   │   └── style.ts
│   ├── main.ts
│   ├── manifest.json
│   ├── mod                                 # 模块目录
│   │   ├── deliver                         # 提货单模块
│   │   │   └── components                  # 提货单相关的组件
│   │   ├── devel                           # 一些开发相关的参考代码，不会出现在最终生产版本中
│   │   │   ├── components
│   │   │   │   ├── demoHome1.vue
│   │   │   │   ├── form1.vue
│   │   │   │   ├── list1.vue
│   │   │   │   └── main.vue
│   │   │   ├── home.vue
│   │   │   ├── homeTab.vue
│   │   │   ├── sampleHost.vue
│   │   │   ├── sampleHostCustom.vue
│   │   │   ├── sampleHostPull.vue
│   │   │   └── sampleHostPullCustom.vue
│   │   ├── main                            # 主模块，包含登录、注册、工作台等页面
│   │   │   ├── components                  # 主模块相关的组件
│   │   │   ├── login.vue
│   │   │   ├── register.vue
│   │   │   └── workbench.vue
│   │   ├── mall                            # 商城模块
│   │   │   ├── cart.vue                    # 购物车页
│   │   │   ├── components                  # 商城相关的组件
│   │   │   ├── home.vue                    # 商城首页
│   │   │   └── me.vue                      # 用户信息页
│   │   └── order                           # 订单模块
│   │       └── components                  # 订单相关的组件
│   ├── pages.json
│   ├── static                              # 资源目录
│   │   └── icons
│   │       ├── apps.png
│   │       └── apps_active.png
│   ├── store                               # store 目录
│   │   ├── area.ts                         # 省市县
│   │   ├── dict.ts                         # 静态字典
│   │   └── system.ts                       # api 调用、登录、注销、当前登录的用户信息等
│   ├── theme.json
│   ├── types                               # 存放 d.ts 的目录
│   │   ├── api.d.ts
│   │   ├── list.d.ts
│   │   ├── scroll.d.ts
│   │   └── style.d.ts
│   ├── ui
│   │   └── wot-design-uni                  # wot-design-uni 的代码，会定制开发，也会持续从官方新版本合并
│   ├── uni.d.ts
│   ├── uni.scss                            # 根据 uni-app 的设计，scss 在适合的场合应该引用 uni.scss 中适合的变量
│   └── util                                # 一些与框架无关的通用代码（比如 store 依赖的那些代码）放这里，不会被 AutoImport 扫描，需要 import 才能使用
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
├── uno.config.ts                           # UnoCSS 配置文件
└── vite.config.ts                          # Vite 配置文件
```

# 约定

1. 使用统一的预设进行代码格式化。
2. 需要用到 HBuilderX 的“运行到手机或模拟器”功能。直接在 HBuilderX 中打开 src 目录的上一级目录，就可以方便的运行到模拟器和微信小程序开发者工具中。
3. 浏览器用于可视化开发，同步运行到 app 端看效果。
4. 尽可能考虑微信小程序的兼容性。
5. 当你发现需要在页面中写一些`// #ifdef`这种条件编译的注释时，考虑下是不是可以封装成为通用的组件或函数。
6. 导航不要写相对路径，统一写绝对路径。导航组件 DIWNavigator，导航接口用 framework 中封装好的 navigateTo、invokeTo、navigateBack。
7. 后期需要分包时，上一条约定的价值就体现了。
8. 微信小程序仅用于预览和真机调试，不要上传版本。
9. 注意 .vue 文件不能是空白内容，这会会导致小程序、App 编译出错。

# 关于 app 版本

1. 最终会打包出两个 app 来，其中一个是给客户去下单的，另一个是给商家、服务商、平台使用的。即：客户版和商家版。
2. 开发阶段，启动看到的就是客户版。如果登录后的租户类型不是“客户”，则会 reLaunch 到商家版。
3. 一些通用的功能，比如合同、提货单相关的功能，客户版和商家版都需要，但是又有所区别。这种情况应该**从页面级别进行区分**，即：客户版做一个页面，商家版做一个页面。如有必要，可以通过组件去复用。

# 关于 [wot-design-uni](https://wot-design-uni.cn/)

1. MIT 协议。为了满足一些可能的定制化需求，目前是直接从 v1.7.1 版本克隆了一份源代码，纳入项目的代码管理中。
2. 由于使用了一些老旧的 Sass API，目前会产生一些警告，但不影响使用。
3. 今后会定期从 wot-design-uni 合并新版本的代码，因此尽量保持最小改动。

# 关于页面和组件

1. 页面统一用 DIWAppPage 作为跟元素
2. 页面和组件区分目录存放
3. 页面不能当成组件用，不要被其他组件 import

# 关于滚动和下拉刷新

1. 页面级的滚动性能最佳
2. DIWListView 封装了一些下拉刷新和加载更多的通用功能
3. DIWAppPage 和 DIWScrollView 都有相关的代码来支持 DIWListView

# 关于 useFramework

framework 的代码大量使用 provide/inject 机制来实现组件之间的接口交互，因此大多数接口函数都要通过 useFramework 来获取和使用。
换句话说，这些函数不是简单的全局函数，而是携带了**上下文**的函数。比如这两个常用的函数：

1. protect 用于调用一系列异步函数，捕获并提示错误。protect 适用于那些不提交数据、不改变状态的接口调用（查询操作）。
2. protectOp 也是用于调用一系列异步函数，捕获并提示错误。如果没有发生错误，则会提示操作成功的信息。并且在所有操作开始前，还可以让用户进行交互确认。protectOp 适用于那些提交数据、改变状态的接口调用（增、删、改操作）。

当页面用 DIWAppPage 作为根组件时，protect 和 protectOp 展现提示消息的方式可能会不同于那些没有使用 DIWAppPage 的页面。

# 关于开发辅助功能

如果以 DIWAppPage 作为页面的根组件，在屏幕上快速点击 3 次（有时候需要 4 次），就能唤醒一个菜单，提供了一些功能，可以用来覆盖一些测试场景。目前支持如下功能：

1. 让下一次网络调用失败。
2. 让网络调用持续失败。这是一个开关。
3. 给每一个网络调用都增加一个不超过 5 秒的随机延迟。这也是一个开关。

# 关于 DIWListView

移动端的列表页面需要精细化开发，因此 DIWListView 只封装了最基础的功能。不管接口是不是分页的，用 DIWListView 都能简化开发。同时 DIWListView 也不限于仅仅调用一个接口。
概念上，加载数据分为“初始化加载”和“加载更多”这两种情况。第一种情况往往需要加载来自多个接口的不同数据，用于展示或后续处理。第二种情况一般只加载一类数据，也就是通常所说的分页数据。
DIWListView 的每一行展示也可以各不相同，这些都是数据驱动的，所以如何合理设计和组织数据是重点。具体例子可以参考 demoHome1.vue 中的一些用法。

# 关于原子样式

用 UnoCSS 是很方便的，但是有一条需要注意的原则：如果你是在做一个公共的组件，那么应该尽可能避免使用原子样式，而是使用一套有明确命名，并且从命名上能看出彼此关系的 class 体系。
这样有利于通过 CSS 去统一调整公共组件。页面和私有组件可以充分使用原子样式。

原子 CSS 采用默认的预设，写法上与 Tailwind CSS 保持一致，同时很多写法更简洁，有时候还能避免微信小程序的兼容性问题。

例如，根据 Tailwind CSS 的文档，本来应该这么写：

`w-[100px]`

而这种写法，在微信小程序中是不支持的，因为 WXSS 不支持转义的写法。而 UnoCSS 的预设还支持你这么写：

`w-100px`

效果上是一样的，微信小程序也能支持。

以 WXSS 的最终效果作为衡量依据。**对于任何 UnoCSS 预设中的原子 CSS 写法，只要是 WXSS 表现上有问题的，一律不采用。**

[Tailwind CSS 文档](https://tailwindcss.com/docs/styling-with-utility-classes)

[UnoCSS 游乐场](https://unocss.dev/play/)

[UnoCSS 速查](https://unocss.dev/interactive/)

# 关于页面参数传递

1. 使用 navigateTo 和 invokeTo 可以通过 params 字段来传递参数，这种用法适合传递少量数据，参数是通过 url 传递的
2. 当从一个页面进入下一个页面时需要传递大量数据时，invokeTo 支持一种传递大量数据的方式，参数不通过 url 传递，此时需要用 usePageInitData 来获取参数的引用

# DIWAppPage 在用户试图退出页面时提示确认

DIWAppPage 有一个 dirty 属性，当这个属性的值为 true 时，用户离开页面的时候会弹出提示消息，提示消息的内容可以通过 leaveMessage 属性进行定制。

# DIWCascadar 和 DIWSelect 组件

DIWCascadar 支持级联数据的展现并选取最末端的节点。DIWSelect 支持从自定义的选项列表中选取一个。DIWSelect 的另外一种模式可以用来选择省市县。

# DIWFile 组件

DIWFile 组件的model-value 支持逗号分隔的多个文件ID列表。

# 关于多标签页面的实现方式

多标签页由标签区和内容区构成。根据实际情况，有两种实现：

1. 每一个标签页的内容都保留，此时使用 swiper 和 swiper-item 来实现内容区。标签区就用 wd-tabs 实现。
2. 只保留当前活动的标签页内容，标签区用 wd-tabs 实现，内容区直接呈现当前活动内容就行。此时应该支持手势，通过左右滑动可以切换标签页。具体实现可以参考使用了 useSwipe 的代码。
3. wd-tabs 本身就直接支持内容区的显示以及左右滑动切换。不建议使用。
