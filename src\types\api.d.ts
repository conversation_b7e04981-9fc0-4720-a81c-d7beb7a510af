interface SessionInfo {
	userId: string;
	avatarUrl: string;
	tenantId: string;
	accessToken: string;
	username: string;
	permissions: string[];

	// 用户基本信息
	name: string;
	nickname: string;
	phone: string;
	email?: string;
	syncJobNumber: string; // 工号

	// 工厂信息
	factoryCode: string;
	factoryName: string;

	// 部门列表
	deptList: Array<{
		deptId: string;
		name: string;
		sortOrder: number;
		syncDeptCode: string;
		syncDeptType?: string;
	}>;

	// 角色列表
	roleList: Array<{
		roleId: string;
		roleName: string;
		roleCode: string;
		roleDesc: string;
		dsType: number;
		dsScope?: string;
		createTime: string;
		delFlag: string;
	}>;

	// 岗位列表
	postList: Array<{
		postId: string;
		postCode: string;
		postName: string;
		postSort: number;
		remark: string;
		createTime: string;
		delFlag: string;
		syncId?: string;
	}>;
	userInfo: any; //后端返回的完整用户信息
}

interface ApiGetOptions {
	url: string;
	params?: Record<string, any>;
	header?: Record<string, string>;
}

interface ApiPostOptions {
	url: string;
	params?: Record<string, any>;
	data?: string | AnyObject | ArrayBuffer;
	header?: Record<string, string>;
}

interface ApiPutOptions {
	url: string;
	params?: Record<string, any>;
	data?: string | AnyObject | ArrayBuffer;
	header?: Record<string, string>;
}

interface ApiDeleteOptions {
	url: string;
	params?: Record<string, any>;
	data?: string | AnyObject | ArrayBuffer;
	header?: Record<string, string>;
}

type MethodType = 'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT';

interface ApiRequestOptions {
	method?: MethodType;
	url: string;
	params?: Record<string, any>;
	data?: string | AnyObject | ArrayBuffer;
	header?: Record<string, string>;
	parse?: 0 | 1 | 2;
}

interface ApiUploadOptions {
	url: string;
	files: any | any[];
	formData?: Record<string, any>;
	parse?: 0 | 1;
}

interface ApiDownloadOptions {
	url: string;
	params?: Record<string, any>;
	header?: Record<string, string>;
}
