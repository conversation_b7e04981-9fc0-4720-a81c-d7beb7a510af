<template>
	<DIWAppPage class="mall-product">
		<DIWRoot>
			<DIWScrollView v-if="initialLoading">
				<view class="loading-container">
					<wd-loading size="large" />
				</view>
			</DIWScrollView>

			<DIWScrollView v-else-if="detail && detail.status !== 1">
				<view class="product-invalid">
					<wd-icon name="warning" size="96rpx" color="#ff5000" />
					<text class="invalid-title">商品已失效</text>
					<text class="invalid-desc">{{ getStatusMessage(detail.status) }}</text>
					<wd-button type="primary" size="medium" @click="backToHome">返回首页</wd-button>
				</view>
			</DIWScrollView>

			<DIWScrollView v-else-if="detail">
				<!-- 刷新指示器 -->
				<view class="refresh-indicator" v-if="refreshing">
					<wd-loading size="medium" />
					<text class="refresh-text">更新中...</text>
				</view>

				<!-- 商品图片轮播区域 -->
				<view class="product-swiper">
					<!-- <view class="video-container" v-show="hasVideo && activeTab === 'video'">
						<video :src="detail.videoUrl" autoplay loop controls class="product-video" v-show="activeTab === 'video'"></video>
					</view> -->
					<DIWSwiper
						height="750rpx"
						autoplayVideo
						autoplay
						stopAutoplayWhenVideoPlay
						:list="formattedImages"
						:muted="false"
						:indicator="{ type: 'fraction' }"
						indicatorPosition="top-left"
					/>
					<!-- <view class="video-btn-container" v-if="hasVideo">
						<view class="video-btn" @click="changeTab('video')">视频</view>
						<view class="image-btn" @click="changeTab('image')">图片</view>
					</view> -->
				</view>

				<!-- 价格区域 -->
				<view class="price-section">
					<view class="price-container">
						<text class="price-symbol">￥</text>
						<text class="price-value">{{ Currency(detail.price) }}</text>
					</view>
				</view>

				<!-- 商品信息区域 -->
				<view class="product-info">
					<view class="product-name">{{ detail.name }}</view>
				</view>

				<!-- 商家信息区 -->
				<view class="seller-section" v-if="hasSession">
					<view class="info-item">
						<text class="info-label">品牌</text>
						<text class="info-value">{{ detail.brandName }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">商家</text>
						<text class="info-value">{{ detail.sellerName }}</text>
					</view>
				</view>

				<!-- 仓库属地选择 -->
				<view class="stock-section" v-if="hasSession">
					<view class="section-title">
						<text class="title-text">仓库属地</text>
					</view>
					<view class="stock-options">
						<wd-radio-group v-model="selectedStock" :inline="false" checked-color="#ff5000" shape="button" cell class="stock-radio-group">
							<wd-radio v-for="stock in detail.stockList" :key="stock.key" :label="stock.text" :value="stock.code">
								<text>{{ stock.text }}</text>
							</wd-radio>
						</wd-radio-group>
					</view>

					<view class="stock-alert" v-if="selectedStock === '0'">
						<wd-icon name="warning" size="28rpx" color="#f90" />
						<text class="alert-text">选择以上'任意城市'作为提货地时，最终提货地点以提货时仓库的实际库存情况为准进行选择</text>
					</view>

					<!-- 可售数量信息 -->
					<view class="info-item">
						<text class="info-label">可售数量</text>
						<text class="info-value highlight">{{ availQuantity.label }} 吨</text>
					</view>

					<view class="friendly-tip" v-if="showFriendlyTip">
						<wd-icon name="info" size="28rpx" color="#ff5000" />
						<text class="tip-text">{{ detail.friendlyTip }}</text>
					</view>

					<view class="error-message" v-if="stockErrorMsg">
						<wd-icon name="warning" size="28rpx" color="#ff5000" />
						<text class="error-text">{{ stockErrorMsg }}</text>
					</view>

					<view class="input-tip" v-if="showInputTip">
						<wd-icon name="info" size="28rpx" color="#ff5000" />
						<text class="tip-text">请先选择仓库属地</text>
					</view>

					<!-- 温馨提示 -->
					<view class="info-item">
						<text class="info-label">温馨提示</text>
						<text class="info-value">不支持7天无理由退换货</text>
					</view>

					<view class="error-message" v-if="errorMessage">{{ errorMessage }}</view>
				</view>

				<!-- 商品详情区域 -->
				<view class="detail-section">
					<view class="section-title">
						<text class="title-text">商品详情</text>
					</view>
					<view class="rich-text-container">
						<!-- 使用富文本组件显示商品描述 -->
						<DIWRichTextEditor v-model="detail.desc" />
					</view>
					<view class="detail-notice">
						<text class="notice-text">合同签署后请尽快提货。</text>
						<text class="notice-text">自提时，如需运输可联系推荐物流公司15393710737，价格由您与物流公司自行商议。</text>
					</view>
				</view>

				<!-- 底部按钮占位，防止内容被遮挡 -->
				<view class="bottom-placeholder"></view>
			</DIWScrollView>
		</DIWRoot>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { debounce } from '@/ui/wot-design-uni/components/common/util';
import { useSystemStore } from '@/store/system';
import { storeToRefs } from 'pinia';
// 导入SwiperList类型
import type { SwiperList } from '@/components/DIWSwiper/types';
import { useProductStore } from '@/store/imProduct';
const { getProductDetail } = useProductStore();

const systemStore = useSystemStore();
const { sessionInfo } = storeToRefs(systemStore);
const { protect, apiGet, apiRequest, usePageQuery, parseImageFiles } = useFramework();
const pq = usePageQuery();

interface ProductDetail {
	productNo: string;
	id: string;
	productType: number;
	sellerId: string;
	name: string;
	brandName: string;
	friendlyTip: string;
	price: number;
	sellerName: string;
	desc: any[];
	videoUrl: string;
	imageUrls: string[];
	stockList: { key: string; code: string; text: string; avail: number; limitBig?: boolean }[];
	favoriteFlag: boolean;
	status?: number;
}

// 页面状态
const initialLoading = ref(true);
const refreshing = ref(false);
const detail = ref<ProductDetail | null>(null);
const selectedStock = ref('');
const quantity = ref(0);
const stockErrorMsg = ref('');
const errorMessage = ref('');
const isScrolled = ref(false);
// const hasVideo = computed(() => Boolean(detail.value?.videoUrl));
// const activeTab = ref('video');
// function changeTab(tab: string) {
// 	activeTab.value = tab;
// }
// 判断是否登录
const hasSession = computed(() => !!sessionInfo.value);

// 计算属性
const availQuantity = computed(() => {
	if (hasSession.value) {
		if (detail.value) {
			const item = detail.value.stockList.find((e) => e.code === selectedStock.value);
			const anyCity = detail.value.stockList[detail.value.stockList.length - 1];
			if (item) {
				const limit = Math.min(item.limitBig ? 1000 : parseFloat('' + item.avail), anyCity.limitBig ? 1000 : parseFloat('' + anyCity.avail));

				return {
					limitBig: item.limitBig,
					label: item.limitBig ? '1000+' : '' + limit,
					limit: item.limitBig ? 999999 : limit,
				};
			} else {
				return {
					limit: 0,
					label: anyCity.avail,
					limitBig: false,
				};
			}
		}
	}
	return {
		limit: 0,
		label: '***',
		limitBig: false,
	};
});

watch(
	[sessionInfo],
	() => {
		nextTick(() => {
			reload(false);
		});
	},
	{ immediate: false }
);
// 显示友好提示
const showFriendlyTip = computed(() => {
	return !!detail.value?.friendlyTip;
});

// 是否显示输入提示
const showInputTip = computed(() => {
	return selectedStock.value === '';
});

// 格式化图片数据以匹配轮播组件
const formattedImages = computed<SwiperList[]>(() => {
	if (!detail.value?.imageUrls) return [];
	let imageUrls = detail.value.imageUrls.map((item) => {
		return {
			value: item,
			type: 'image' as const,
		};
	});

	// #ifndef APP-PLUS
	return detail.value.videoUrl ? [{ value: detail.value.videoUrl, type: 'video' as const }, ...imageUrls] : imageUrls;
	// #endif
	// #ifdef APP-PLUS
	return imageUrls;
	// #endif
});

// 获取商品详情
onMounted(() => {
	reload(false);
});

// 货币格式化方法
function Currency(value: number | string) {
	return formatCurrency(value);
}

// 处理数量变更
const handleQuantity = debounce(async (flag: boolean) => {
	// 如果是blur事件触发，检查上下限
	if (flag) {
		if (quantity.value > availQuantity.value.limit) {
			quantity.value = availQuantity.value.limit;
		}
		if (quantity.value < 0 || !quantity.value) {
			quantity.value = 0.025;
		}
	}

	// 如果没有必要的参数，直接返回
	if (!detail.value || !selectedStock.value || quantity.value <= 0) {
		return;
	}

	// 数量必须是0.025的倍数
	const defaultNumber = availQuantity.value.limit > 0.025 ? 0.025 : availQuantity.value.limit;
	if (quantity.value < defaultNumber) {
		stockErrorMsg.value = `最小值为${defaultNumber}`;
		quantity.value = defaultNumber;
		return;
	}

	const multiple = Math.round(quantity.value / 0.025);
	const validQuantity = (multiple * 1000 * 0.025) / 1000;

	if (quantity.value !== validQuantity) {
		stockErrorMsg.value = '请输入0.025的倍数';
		quantity.value = validQuantity;
		return;
	}

	try {
		const { data: response } = await apiRequest({
			method: 'GET',
			url: 'stock/stockOpt/checkStock',
			parse: 2,
			params: {
				productId: detail.value.id,
				city: selectedStock.value,
				quantity: quantity.value,
			},
		});
		console.log(response);

		// 根据接口返回结果处理
		if (response.data === true) {
			stockErrorMsg.value = '';
		}
		if (response.code === 1) {
			stockErrorMsg.value = response.msg || '库存不足，请减少购买数量';
		}
	} catch (error: any) {
		console.error('校验库存数量失败:', error);
		stockErrorMsg.value = error.msg || '校验库存失败，请稍后重试';
	}
}, 300);

// 监听城市选择变化
watch(selectedStock, () => {
	stockErrorMsg.value = '';
	if (selectedStock.value && quantity.value > 0) {
		handleQuantity(false);
	}
});
// 加载商品详情
function reload(isRefresh = true) {
	if (!isRefresh) {
		initialLoading.value = true;
	} else {
		refreshing.value = true;
	}

	protect(async () => {
		const d = await getProductDetail(pq.value.id as string);
		console.log(d);
		let desc: any[] = [];
		try {
			desc = JSON.parse(d.productDesc);
		} catch (err) {}

		const imageUrls: string[] = [];
		let videoUrl = '';

		// 优化视频处理逻辑，添加防御性检查
		if (d.videoFileIds) {
			const videoData = parseImageFiles(d.videoFileIds);
			if (videoData && videoData.length > 0) {
				videoUrl = videoData[0].url;
			}
		}

		// 处理图片数据
		if (d.imageFileIds) {
			const imageData = parseImageFiles(d.imageFileIds);
			if (imageData && imageData.length > 0) {
				imageData.forEach((item: any) => {
					if (item && item.url) {
						imageUrls.push(item.url);
					}
				});
			}
		}

		detail.value = {
			productNo: d.productNo,
			id: d.id,
			name: d.name,
			productType: d.productType,
			sellerId: d.sellerId,
			brandName: d.brandName,
			sellerName: d.sellerName,
			price: d.price,
			friendlyTip: d.friendlyTipContent,
			status: d.status,
			desc: Array.isArray(desc) ? desc : [],
			favoriteFlag: d.favoriteFlag,
			stockList: d.productStock
				.filter((e: Record<string, any>) => e.currentStockStr !== '0.000')
				.map((e: Record<string, any>, index: number) => ({
					key: `k_${index}`,
					code: e.localCity,
					text: e.localCityStr,
					avail: e.currentStockStr,
					limitBig: e.currentStockStr === '1000+',
				}))
				.sort((a: { code: string }, b: { code: string }) => (a.code === '0' ? 1 : b.code === '0' ? -1 : 0)),
			videoUrl,
			imageUrls,
		};
	}).finally(() => {
		initialLoading.value = false;
		refreshing.value = false;
	});
}

// 获取商品状态消息
function getStatusMessage(status?: number) {
	switch (status) {
		case 0:
			return '该商品正在审核中，暂时无法查看';
		case 2:
			return '该商品未通过审核，无法查看';
		case 7:
			return '该商品已下架，无法查看';
		case 8:
			return '该商品为草稿状态，暂时无法查看';
		case 9:
			return '该商品已被删除，无法查看';
		default:
			return '抱歉，您查看的商品已失效';
	}
}

// 监听页面滚动
onPageScroll((e) => {
	// 当滚动超过一定距离时，给导航栏添加背景
	isScrolled.value = e.scrollTop > 50;
});
</script>

<style lang="scss" scoped>
.mall-product {
	background-color: #f8f8f8;
}

/* 顶部导航栏 */
.nav-header {
	position: fixed;
	top: 20rpx;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	z-index: 999;
	/* 适配刘海屏 */
	padding-top: constant(safe-area-inset-top);
	padding-top: env(safe-area-inset-top);
	transition: all 0.3s ease;
}

.nav-header-with-bg {
	background-color: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	top: 0;
	padding-top: calc(constant(safe-area-inset-top) + 20rpx);
	padding-top: calc(env(safe-area-inset-top) + 20rpx);
	padding-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-area {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 20rpx 0 20rpx 20rpx;
	background-color: rgba(255, 255, 255, 0.7);
}

.cart-area {
	height: 90rpx;
	width: 90rpx;
	border-radius: 45rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	margin-right: 40rpx;
	background-color: rgba(255, 255, 255, 0.7);
}

.cart-badge {
	background-color: #fff;
	color: #ff5000;
	font-size: 24rpx;
	min-width: 40rpx;
	height: 40rpx;
	border-radius: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: bold;
	margin-right: 20rpx;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}

/* 商品失效样式 */
.product-invalid {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 30rpx;

	.invalid-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin: 30rpx 0 20rpx;
	}

	.invalid-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
		text-align: center;
	}
}
.video-container {
	height: 750rpx;
	width: 750rpx;
}

/* 轮播图区域 */
.product-swiper {
	width: 100%;
	position: relative;
	background: #fff;
}

.product-video {
	width: 750rpx;
	height: 750rpx;
	z-index: 999;
	background: #000;
}
.video-btn-container {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	bottom: 0;
	left: 0;
	height: 60rpx;
	width: 80px;
	background: rgba(0, 0, 0, 0.5);
}
/* 价格区域 */
.price-section {
	background: #fff;
	padding: 0rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.price-container {
	display: flex;
	align-items: baseline;
}
.stock-radio-group {
	display: flex;
	justify-content: start;
	align-items: center;
	flex-wrap: wrap;
}
.wd-radio.is-button-radio {
	display: inline-flex;
	min-width: 33.3333%;
	width: auto;
	padding: 24rpx 24rpx 0rpx 0rpx;
	box-sizing: border-box;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	.wd-radio__label {
		width: 100%;
		max-width: inherit;
	}
}
.price-symbol {
	font-size: 32rpx;
	color: #ff5000;
	font-weight: bold;
}

.price-value {
	font-size: 48rpx;
	color: #ff5000;
	font-weight: bold;
}

.favorite-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-items: center;
	width: 80rpx;
}

.favorite-text {
	width: 80rpx;
	font-size: 24rpx;
	color: #666;
	margin-top: 6rpx;
	text-align: center;

	&.favorite-active {
		color: #ff5000;
	}
}

/* 商品信息区域 */
.product-info {
	background: #fff;
	padding: 0rpx 30rpx 20rpx;
	border-bottom: 1rpx solid #eee;
}

.product-name {
	font-size: 32rpx;
	color: #333;
	font-weight: bold;
	line-height: 1.4;
	margin-bottom: 10rpx;
}

.product-no {
	font-size: 24rpx;
	color: #999;
}

/* 商家信息区域 */
.seller-section {
	background: #fff;
	margin-top: 20rpx;
	padding: 20rpx 30rpx 20rpx 30rpx;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.info-label {
	width: 140rpx;
	font-size: 28rpx;
	color: #666;
}

.info-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;

	&.highlight {
		color: #ff5000;
		font-weight: bold;
	}
}

/* 仓库选择区域 */
.stock-section {
	background: #fff;
	margin-top: 20rpx;
	padding: 30rpx;
}

.section-title {
	// margin-bottom: 20rpx;
	position: relative;
	padding-left: 20rpx;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 30rpx;
		background-color: #ff5000;
		border-radius: 3rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
}

.stock-options {
	margin-bottom: 20rpx;
}

.stock-alert {
	display: flex;
	align-items: center;
	background: #fff9f0;
	padding: 20rpx;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
}

.alert-text {
	font-size: 24rpx;
	color: #f90;
	margin-left: 10rpx;
	line-height: 1.4;
}

.friendly-tip {
	display: flex;
	align-items: flex-start;
	margin: 20rpx 0;
}

.tip-text {
	font-size: 24rpx;
	color: #ff5000;
	margin-left: 10rpx;
	line-height: 1.4;
	flex: 1;
}

/* 购买数量区域 */
.quantity-section {
	display: flex;
	align-items: center;
	margin: 30rpx 0;

	/* #ifdef MP-WEIXIN */
	:deep(.wd-input-number) {
		z-index: 1;
	}
	/* #endif */
}

.error-message {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.error-text {
	font-size: 24rpx;
	color: #ff5000;
	margin-left: 10rpx;
}

.input-tip {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

/* 协议同意区域 */
.agreement-section {
	display: flex;
	align-items: center;
	margin: 10rpx 0;
}

.agreement-link {
	font-size: 28rpx;
	color: #ff5000;
	margin-left: 10rpx;
}

/* 底部操作按钮 */
.action-buttons {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	margin: 40rpx 0 20rpx;
}

.cart-btn {
	flex: 1;
	border-radius: 40rpx;
	background-color: #ff5000 !important;
	color: #fff;
	border: 1rpx solid #ff5000 !important;
}

.buy-btn {
	flex: 1;
	border-radius: 40rpx;
	border-radius: 40rpx;
	background-color: #ff5000 !important;
	color: #fff;
	border: 1rpx solid #ff5000 !important;
}

/* 未登录状态 */
.login-section {
	background: #fff;
	margin-top: 20rpx;
	padding: 60rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.login-tip {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 40rpx;
}

/* 商品详情区域 */
.detail-section {
	background: #fff;
	margin-top: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.rich-text-container {
	padding: 20rpx 0;
}

.detail-notice {
	margin-top: 30rpx;
}

.notice-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	display: block;
	margin-bottom: 10rpx;
}

/* 下单须知弹窗 */
.terms-popup {
	padding: 30rpx;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	overflow-y: scroll;
}

.terms-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 20rpx;
}

.terms-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff5000;
}

.terms-content {
	max-height: 50vh;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
	flex: 1;
}

.terms-item {
	margin-bottom: 20rpx;
}

.terms-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	word-break: break-all;
	white-space: normal;
}

.terms-footer {
	padding-top: 20rpx;
	border-top: 1rpx solid #eee;
	margin-top: 20rpx;
}

/* 刷新指示器 */
.refresh-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 10rpx 20rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 30rpx;
	position: fixed;
	top: 20rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 99;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.refresh-text {
	font-size: 24rpx;
	color: #333;
	margin-left: 10rpx;
}

/* 底部按钮占位，防止内容被遮挡 */
.bottom-placeholder {
	height: 160rpx;
	margin-bottom: 20rpx;
}

/* 底部固定操作按钮 */
.fixed-action-buttons {
	z-index: 100;
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #fff;
	:deep(.diw-bottom-bar__container) {
		flex-direction: column;
		align-items: start;
		padding: 0rpx 32rpx 24rpx 32rpx;
	}
	/* #ifdef MP-WEIXIN */
	padding-bottom: env(safe-area-inset-bottom) !important;
	/* #endif */
}

.login-reminder {
	width: 100%;
	padding: 10rpx 0;
}

.terms-reminder {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
	// background: #fff9f0;
	padding: 10rpx;
	border-radius: 6rpx;

	text {
		font-size: 24rpx;
		color: #ff5000;
		margin-left: 8rpx;
	}
}

.buttons-container {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
	width: 100%;
}

/* 微信小程序专用按钮样式 */
/* #ifdef MP-WEIXIN */
.mp-cart-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	background-color: #ff5000 !important;
	color: #fff !important;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	&[disabled] {
		background-color: #f0883a !important;
		color: #fff !important;
	}
}

.mp-buy-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	background-color: #ff5000 !important;
	color: #fff !important;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	&[disabled] {
		background-color: #f0883a !important;
		color: #fff !important;
	}
}
/* #endif */

:deep(.wd-radio-group.is-button) {
	padding: 0;
}
:deep(.wd-swiper__track) {
	border-radius: 0 !important;
}

/* 添加自定义样式 */
.mall-login-btn {
	background-color: #ff5000 !important;
	border-color: #ff5000 !important;
}
</style>
