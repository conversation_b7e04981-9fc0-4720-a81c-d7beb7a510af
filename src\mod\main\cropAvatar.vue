<template>
	<DIWAppPage>
		<DIWProtect>
			<wd-img-cropper v-model="showCropper" :img-src="imageSrc" @confirm="handleConfirm" @cancel="handleCancel" />
		</DIWProtect>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { usePageQuery, navigateBack, usePageResult } = useFramework();
const { finishPage } = usePageResult();

const pq = usePageQuery();

const imageSrc = ref('');

watch(
	pq,
	() => {
		if (pq.value) {
			if (typeof pq.value.url === 'string') {
				imageSrc.value = pq.value.url;
			}
		}
	},
	{ immediate: true }
);

const showCropper = ref(true);

function createTempImage(d: string) {
	if (d.startsWith('data:image/')) {
		const s = d.indexOf(';base64,');
		if (s >= 0) {
			const bytes = atob(d.substring(s + 8));
			const byteNumbers = new Array(bytes.length);
			for (let i = 0; i < bytes.length; i++) {
				byteNumbers[i] = bytes.charCodeAt(i);
			}

			const byteArray = new Uint8Array(byteNumbers);
			const blob = new File([byteArray], '', { type: d.substring(5, s) });
			return blob;
		}
	}

	return d;
}

function handleConfirm(f: { tempFilePath: string }) {
	// #ifdef APP-PLUS || MP-WEIXIN
	finishPage({ tempFilePath: f.tempFilePath });
	// #endif
	// #ifdef H5
	finishPage({ tempFilePath: createTempImage(f.tempFilePath) });
	// #endif
}

function handleCancel() {
	navigateBack();
}
</script>
