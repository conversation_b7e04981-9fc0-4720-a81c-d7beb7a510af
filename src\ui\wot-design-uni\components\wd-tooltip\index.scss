@use './../common/abstracts/_mixin.scss' as *;
@use './../common/abstracts/variable.scss' as *;

.wot-theme-dark {
	@include b(tooltip) {
		@include e(pos) {
			background: $wot-dark-background4;
			color: $wot-tooltip-color;
		}

		@include triangleArrow($wot-tooltip-arrow-size, $wot-dark-background4);
	}
}

@include b(tooltip) {
	position: relative;
	display: inline-block;

	@include edeep(pos) {
		position: absolute;
		min-width: 138px;
		min-height: 36px;
		font-size: $wot-tooltip-fs;
		backdrop-filter: blur($wot-tooltip-blur);
		background-clip: padding-box;
		border-radius: $wot-tooltip-radius;
		background: $wot-tooltip-bg;
		color: $wot-tooltip-color;
		text-align: center;
		box-sizing: border-box;
		z-index: $wot-tooltip-z-index;
	}

	@include e(hidden) {
		left: -100vw;
		bottom: -100vh;
		visibility: hidden;
	}

	@include e(container) {
		line-height: $wot-tooltip-line-height;
		font-size: $wot-tooltip-fs;
	}

	@include e(inner) {
		padding: $wot-tooltip-padding;
		white-space: nowrap;
		line-height: $wot-tooltip-line-height;
	}

	@include edeep(close-icon) {
		font-size: 12px;
		position: absolute;
		right: -8px;
		top: -10px;
		transform: scale(0.5);
		padding: 10px;
	}

	@include triangleArrow($wot-tooltip-arrow-size, $wot-tooltip-bg);
}
