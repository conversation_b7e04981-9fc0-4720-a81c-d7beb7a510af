<template>
  <DIWAppPage :title="pageTitle">
    <view class="p-4">
      <wd-form ref="formRef" :model="formData" label-width="100px">
        <wd-form-item label="调拨名称" prop="allocationName" :rules="[{ required: true, message: '请输入调拨名称' }]">
          <wd-input v-model="formData.allocationName" placeholder="请输入调拨名称" />
        </wd-form-item>
        
        <wd-form-item label="出库仓库" prop="outWarehouseId" :rules="[{ required: true, message: '请选择出库仓库' }]">
          <wd-select v-model="formData.outWarehouseId" placeholder="请选择出库仓库">
            <wd-option v-for="item in warehouseList" :key="item.id" :label="item.name" :value="item.id" />
          </wd-select>
        </wd-form-item>
        
        <wd-form-item label="入库仓库" prop="inWarehouseId" :rules="[{ required: true, message: '请选择入库仓库' }]">
          <wd-select v-model="formData.inWarehouseId" placeholder="请选择入库仓库">
            <wd-option v-for="item in warehouseList" :key="item.id" :label="item.name" :value="item.id" />
          </wd-select>
        </wd-form-item>
        
        <wd-form-item label="出库时间" prop="outAllocationTime" :rules="[{ required: true, message: '请选择出库时间' }]">
          <wd-datetime-picker v-model="formData.outAllocationTime" type="date" placeholder="请选择出库时间" />
        </wd-form-item>
        
        <wd-form-item label="到达时间" prop="arrivalAllocationTime" :rules="[{ required: true, message: '请选择到达时间' }]">
          <wd-datetime-picker v-model="formData.arrivalAllocationTime" type="date" placeholder="请选择到达时间" />
        </wd-form-item>
        
        <wd-form-item label="配送方式" prop="deliveryMethod" :rules="[{ required: true, message: '请选择配送方式' }]">
          <wd-radio-group v-model="formData.deliveryMethod">
            <wd-radio :value="1">汽运</wd-radio>
            <wd-radio :value="2">公铁联运</wd-radio>
          </wd-radio-group>
        </wd-form-item>
        
        <wd-form-item label="备注" prop="remark">
          <wd-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
        </wd-form-item>
      </wd-form>
      
      <view class="mt-8 flex justify-between">
        <wd-button @click="handleCancel">取消</wd-button>
        <wd-button type="primary" @click="handleSubmit">提交</wd-button>
      </view>
    </view>
  </DIWAppPage>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';

const { apiGet, apiPost, navigateBack, showMessage, protect, usePageQuery, validateForm } = useFramework();

const pq = usePageQuery();
const formRef = ref();
const allocationType = computed(() => Number(pq.type) || 1);
const pageTitle = computed(() => allocationType.value === 1 ? '添加调拨单' : '添加移库单');

const formData = reactive({
  allocationName: '',
  outWarehouseId: '',
  inWarehouseId: '',
  outAllocationTime: '',
  arrivalAllocationTime: '',
  deliveryMethod: 1,
  remark: '',
  allocationType: allocationType.value,
});

const warehouseList = ref([]);

onMounted(() => {
  loadWarehouseList();
});

// 加载仓库列表
async function loadWarehouseList() {
  try {
    const res = await apiGet('stock/warehouse/list');
    if (res && Array.isArray(res)) {
      warehouseList.value = res;
    }
  } catch (error) {
    console.error('加载仓库列表失败', error);
  }
}

// 提交表单
function handleSubmit() {
  protect(async () => {
    await validateForm(formRef.value);
    
    const res = await apiPost('stock/warehouseAllocation/add', formData);
    if (res) {
      showMessage('添加成功');
      navigateBack();
    }
  });
}

// 取消
function handleCancel() {
  navigateBack();
}
</script>