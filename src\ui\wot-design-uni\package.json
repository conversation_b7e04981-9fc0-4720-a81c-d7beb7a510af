{"id": "wot-design-uni", "name": "wot-design-uni", "displayName": "wot-design-uni 基于vue3+Typescript的高颜值组件库", "version": "1.11.1", "license": "MIT", "description": "一个基于Vue3+TS开发的uni-app组件库，提供70+高质量组件，支持暗黑模式、国际化和自定义主题。", "keywords": ["wot-design-uni", "国际化", "组件库", "vue3", "暗黑模式"], "main": "index.ts", "repository": "https://github.com/Moonofweisheng/wot-design-uni.git", "engines": {"HBuilderX": "^3.8.7", "uni-app": "^4.06", "uni-app-x": ""}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/wot-design-uni", "darkmode": "√", "i18n": "√", "widescreen": "x"}, "vetur": {"tags": "tags.json", "attributes": "attributes.json"}, "web-types": "web-types.json", "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "x", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "x", "android": {"extVersion": "", "minVersion": "26"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "√"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}, "peerDependencies": {"vue": ">=3.2.47"}}