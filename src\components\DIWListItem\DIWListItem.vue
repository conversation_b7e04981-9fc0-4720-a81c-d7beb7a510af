<template>
	<view class="diw-listitem">
		<view class="diw-listitem__frame shadow-md" hover-class="diw-listitem__frame--hover" @click="handleFrameClick">
			<view v-if="props.showHeader && $slots.header" class="diw-listitem__header" @click="handleHeaderClick">
				<slot name="header" />
			</view>
			<view v-if="$slots.default" class="diw-listitem__content" @click="handleContentClick">
				<slot name="default" />
			</view>
			<view v-if="props.showFooter && $slots.footer" class="diw-listitem__footer" @click="handleFooterClick">
				<slot name="footer" />
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{ showHeader?: boolean; showFooter?: boolean }>(), { showHeader: true, showFooter: true });

const emit = defineEmits<{ click: [any]; headerClick: [any]; contentClick: [any]; footerClick: [any] }>();

function handleFrameClick(ev: any) {
	emit('click', ev);
}

function handleHeaderClick(ev: any) {
	emit('headerClick', ev);
}

function handleContentClick(ev: any) {
	emit('contentClick', ev);
}

function handleFooterClick(ev: any) {
	emit('footerClick', ev);
}
</script>
