<template>
	<DIWAppPage mode="1">
		<DIWScrollView>
			<wd-skeleton v-if="!rootNode" animation="gradient" theme="paragraph" />
			<template v-else>
				<view v-for="item in displayItems" :key="item.node.id" :class="'tree-node tree-node__level' + item.level" hover-class="tree-node__hover">
					<wd-checkbox v-model="item.node.checked" custom-shape-class="xxxx" @change="handleCheck(item.node)">{{ item.node.name }} </wd-checkbox>
					<template v-if="!item.node.isLeaf">
						<wd-button v-if="item.node.open" size="small" type="icon" icon="arrow-down" @click="item.node.open = false" />
						<wd-button v-else size="small" type="icon" icon="arrow-right" @click="item.node.open = true" />
					</template>
				</view>
			</template>
		</DIWScrollView>
		<template #bottomBar>
			<DIWBottomBar>
				<wd-button size="small" type="icon" icon="menu-fold" @click="expandAll(false)" />
				<wd-button size="small" type="icon" icon="menu-unfold" @click="expandAll(true)" />
				<view class="flex-1" />
				<wd-button size="small" type="icon" icon="check" @click="save" />
			</DIWBottomBar>
		</template>
	</DIWAppPage>
</template>

<script setup lang="ts">
const { apiGet, apiPut, protect, protectOp, showLoading, hideLoading, usePageQuery, navigateBack } = useFramework();

const pq = usePageQuery();

interface Node {
	id: string;
	name: string;
	isLeaf: boolean;
	open: boolean;
	checked: boolean;
	children: Node[];
	parent: Node | null;
}

const rootNode = ref<Node | null>(null);

const displayItems = computed(() => {
	const result: Array<{ level: number; node: Node }> = [];

	if (rootNode.value) {
		function visit(level: number, node: Node) {
			if (level > 0) {
				result.push({ level, node });
			}

			if (node.open && !node.isLeaf) {
				const nextLevel = level + 1;
				for (const child of node.children) {
					visit(nextLevel, child);
				}
			}
		}

		visit(0, rootNode.value);
	}

	return result;
});

function expandAll(expand: boolean) {
	function visit(level: number, node: Node) {
		if (level > 0) {
			if (!node.isLeaf) {
				node.open = expand;
			}
		}

		if (!node.isLeaf) {
			const nextLevel = level + 1;
			for (const child of node.children) {
				visit(nextLevel, child);
			}
		}
	}

	if (rootNode.value) {
		visit(0, rootNode.value);
	}
}

function handleCheck(node: Node) {
	let check = node.checked;

	function visit(node: Node) {
		node.checked = check;

		if (!node.isLeaf) {
			for (const child of node.children) {
				visit(child);
			}
		}
	}

	visit(node);

	if (check) {
		let p = node.parent;
		while (p) {
			p.checked = true;
			p = p.parent;
		}
	} else {
		let p = node.parent;
		while (p) {
			if (!p.children.find((e) => e.checked)) {
				p.checked = false;
			}
			p = p.parent;
		}
	}
}

async function loadTreeData(type: string, id: string): Promise<{ nodes: Array<Record<string, any>>; checkedIdList: string[] }> {
	if (type === '1') {
		// 客户
		const d = await Promise.all([apiGet('admin/tenant/tree/menu'), apiGet('admin/buyerManage/info/' + encodeURI(id))]);
		const menuId: string = d[1].menuId;
		return { nodes: d[0], checkedIdList: menuId.substring(1, menuId.length - 1).split(',') };
	}

	if (type === '2') {
		// 商家
		const d = await Promise.all([apiGet('admin/tenant/tree/menu'), apiGet('admin/sellerInfo/' + encodeURI(id))]);
		return { nodes: d[0], checkedIdList: d[1].menuIdList };
	}

	if (type === '3') {
		// 服务商
		const d = await Promise.all([apiGet('admin/tenant/tree/menu'), apiGet('admin/serverInfo/' + encodeURI(id))]);
		return { nodes: d[0], checkedIdList: d[1].menuIdList };
	}

	// 角色
	const d = await Promise.all([apiGet('admin/menu/tree'), apiGet('admin/menu/tree/' + encodeURI(id))]);
	return { nodes: d[0], checkedIdList: d[1] };
}

watchEffect(() => {
	if (pq.value.id) {
		protect(async () => {
			try {
				showLoading();
				const td = await loadTreeData(pq.value.type, pq.value.id);

				const checkedIds = new Set<string>();
				td.checkedIdList.forEach((id) => checkedIds.add(id));

				function visit(parent: Node | null, level: number, node: Record<string, any>): Node {
					const self: Node = {
						id: level === 0 ? 'root' : node.id,
						name: level === 0 ? 'root' : node.name,
						isLeaf: true,
						open: true,
						checked: level === 0 ? false : checkedIds.has(node.id),
						children: [],
						parent,
					};

					if (Array.isArray(node.children)) {
						const nextLevel = level + 1;
						const ls = [...node.children];
						ls.sort((a, b) => a.sortOrder - b.sortOrder);
						for (const child of ls) {
							self.children.push(visit(self, nextLevel, child));
						}
					}

					self.isLeaf = self.children.length === 0;

					if (self.checked) {
						let p = parent;
						while (p) {
							p.checked = true;
							p = p.parent;
						}
					}

					return self;
				}

				rootNode.value = visit(null, 0, { children: td.nodes });
			} finally {
				hideLoading();
			}
		});
	}
});

function collectCheckedIds() {
	const result: string[] = [];

	if (rootNode.value) {
		function visit(level: number, node: Node) {
			if (level > 0) {
				if (node.checked) {
					result.push(node.id);
				}
			}

			if (!node.isLeaf) {
				const nextLevel = level + 1;
				for (const child of node.children) {
					visit(nextLevel, child);
				}
			}
		}

		visit(0, rootNode.value);
	}

	return result;
}

async function saveTreeData(type: string, id: string) {
	if (type === '0') {
		// 角色
		return await apiPut({ url: 'admin/role/menu', data: { roleId: id, menuIds: collectCheckedIds().join(',') } });
	}

	if (type === '1') {
		// 客户
		return await apiPut({ url: 'admin/buyerManage/info/updateAuth', data: { id, menuIdList: collectCheckedIds() } });
	}

	if (type === '2') {
		// 商家
		return await apiPut({ url: 'admin/sellerInfo/updateAuth', data: { id, menuIdList: collectCheckedIds() } });
	}

	if (type === '3') {
		// 服务商
		return await apiPut({ url: 'admin/serverInfo/updateAuth', data: { id, menuIdList: collectCheckedIds() } });
	}
}

async function save() {
	protectOp(async () => {
		await saveTreeData(pq.value.type, pq.value.id);

		navigateBack();
	});
}
</script>

<style lang="scss" scoped>
.tree-node {
	display: flex;
	flex-direction: row;
	align-items: center;
	position: relative;
	background: white;

	&__hover {
		background-color: $uni-bg-color-hover;
	}

	@for $i from 1 through 8 {
		&__level#{$i} {
			:deep(.wd-checkbox) {
				flex: 1;
				padding-top: 10px;
				padding-bottom: 10px;
				padding-left: $i * 8px;
				white-space: nowrap;
			}
		}
	}
}

.tree-node + .tree-node {
	&::after {
		position: absolute;
		display: block;
		content: '';
		width: 100%;
		height: 1px;
		left: 0;
		top: 0;
		transform: scaleY(0.5);
		background-color: $uni-border-color;
	}
}
</style>
