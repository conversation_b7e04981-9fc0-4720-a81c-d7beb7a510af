<template>
	<view class="notification-section">
		<view class="notification-header-wrapper">
			<view class="notification-header">
				<view class="title-wrapper">
					<view class="title-indicator"></view>
					<text class="title">{{ t('notification') }}</text>
				</view>
				<view class="notification-info" @click="handleNotificationClick">
					<text class="unread-text">{{ t('unreadNewMessage') }}</text>
					<view class="unread-badge">{{ notificationData.unreadCount }}</view>
					<wd-icon name="arrow-right" size="24rpx" color="#86909C" />
				</view>
			</view>

			<view class="notification-content">
				<text class="notification-text">{{ notificationData.latestNotification.title }}</text>
				<text class="notification-date">{{ notificationData.latestNotification.date }} </text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用国际化
const { t } = useI18n();

const props = defineProps<{
	needRefresh: boolean;
}>();
const { apiGet, navigateTo, protect } = useFramework();

async function getNotificationData() {
	// const d = await apiGet('mesmultidata/DiwNotification/list');
	// console.log(d);
}

watch(
	() => props.needRefresh,
	async (newVal) => {
		if (newVal) {
			await getNotificationData();
		}
	}
);

// 模拟通知数据
const notificationData = ref({
	unreadCount: 12,
	latestNotification: {
		title: '香格一厂关于管电路板的通知',
		date: '2025.7.11',
	},
});

// 处理通知点击事件
function handleNotificationClick() {
	protect(async () => {
		await navigateTo('/mod/message/notificationList');
	});
}
</script>

<style lang="scss" scoped>
.notification-section {
	margin-top: -36rpx;
	width: 750rpx;
	height: 288rpx;
	background-image: url('/static/icons/<EMAIL>');
	background-size: 750rpx 220rpx;
	background-repeat: no-repeat;
	background-position: 0 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-radius: 24rpx;
}
.notification-header-wrapper {
	width: 702rpx;
	display: flex;
	flex-direction: column;
}
.notification-header {
	padding: 54rpx 36rpx 0 32rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title-wrapper {
		display: flex;
		align-items: center;

		.title-indicator {
			width: 8rpx;
			height: 32rpx;
			background: #0082f0;
			border-radius: 4rpx;
			margin-right: 16rpx;
		}

		.title {
			font-size: 30rpx;
			font-weight: normal;
			line-height: 24rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #1d2129;
		}
	}

	.notification-info {
		display: flex;
		align-items: center;
		gap: 8rpx;

		.unread-text {
			font-size: 24rpx;
			font-weight: normal;
			line-height: 32rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #4e5969;
		}

		.unread-badge {
			padding: 2rpx 8rpx;
			border-radius: 12rpx;
			background: #f62323;

			font-size: 20rpx;
			font-weight: normal;
			line-height: 24rpx;
			text-align: center;
			letter-spacing: 0rpx;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

.notification-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 36rpx 36rpx 0 36rpx;

	.notification-text {
		font-size: 24rpx;
		font-weight: normal;
		line-height: 24rpx;
		letter-spacing: 0rpx;
		color: #1d2129;
		width: 500rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.notification-date {
		font-size: 20rpx;
		font-weight: normal;
		line-height: 24rpx;
		letter-spacing: 0rpx;
		color: #4b5563;
	}
}
</style>
