<!-- 故障详情页面 -->
<template>
	<DIWAppPage>
		<template #navbar>
			<wd-navbar title="故障详情" left-arrow @click-left="navigateBack()" :border="false" :fixed="true" placeholder />
		</template>

		<view class="detail-container" v-if="faultDetail">
			<!-- 基本信息 -->
			<view class="info-section">
				<view class="section-title">基本信息</view>
				<view class="info-content">
					<view class="info-row">
						<text class="label">机台号:</text>
						<text class="value">{{ faultDetail.machineNumber }}</text>
					</view>
					<view class="info-row">
						<text class="label">故障类型:</text>
						<view class="fault-type" :class="getFaultTypeClass(faultDetail.status)">
							{{ faultDetail.faultType }}
						</view>
					</view>
					<view class="info-row">
						<text class="label">上报时间:</text>
						<text class="value">{{ formatDateTime(faultDetail.createTime) }}</text>
					</view>
					<view class="info-row">
						<text class="label">当前状态:</text>
						<view class="status-badge" :class="getStatusClass(faultDetail.status)">
							{{ getStatusText(faultDetail.status) }}
						</view>
					</view>
					<view v-if="faultDetail.processTime" class="info-row">
						<text class="label">处理时间:</text>
						<text class="value">{{ formatDateTime(faultDetail.processTime) }}</text>
					</view>
				</view>
			</view>

			<!-- 故障描述 -->
			<view class="desc-section">
				<view class="section-title">故障描述</view>
				<view class="desc-content">
					{{ faultDetail.faultDescription }}
				</view>
			</view>

			<!-- 故障图片 -->
			<view v-if="faultDetail.images && faultDetail.images.length > 0" class="images-section">
				<view class="section-title">故障图片</view>
				<view class="images-grid">
					<view v-for="(image, index) in faultDetail.images" :key="index" class="image-item" @click="previewImage(image, index)">
						<image :src="image" class="fault-image" mode="aspectFill" />
					</view>
				</view>
			</view>

			<!-- 语音记录 -->
			<view v-if="faultDetail.voiceFile" class="voice-section">
				<view class="section-title">语音记录</view>
				<view class="voice-player">
					<view class="play-btn" @click="toggleVoicePlay">
						<wd-icon :name="isVoicePlaying ? 'pause' : 'play'" size="32rpx" color="#1C64FD" />
					</view>
					<view class="voice-info">
						<text class="voice-duration">{{ faultDetail.voiceDuration || '0:00' }}</text>
						<text class="voice-tip">点击播放语音描述</text>
					</view>
				</view>
			</view>

			<!-- 处理记录 -->
			<view v-if="faultDetail.processRecords && faultDetail.processRecords.length > 0" class="process-section">
				<view class="section-title">处理记录</view>
				<view class="process-timeline">
					<view v-for="(record, index) in faultDetail.processRecords" :key="index" class="timeline-item">
						<view class="timeline-dot" :class="getProcessStatusClass(record.status)"></view>
						<view class="timeline-content">
							<view class="process-header">
								<text class="process-title">{{ record.title }}</text>
								<text class="process-time">{{ formatDateTime(record.time, 'MM-dd HH:mm') }}</text>
							</view>
							<view v-if="record.description" class="process-desc">
								{{ record.description }}
							</view>
							<view v-if="record.operator" class="process-operator"> 处理人: {{ record.operator }} </view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</DIWAppPage>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { formatDateTime } from '@/util/number';

const { t } = useI18n();
const { navigateTo, navigateBack, usePageQuery } = useFramework();
const pq = usePageQuery();

// 故障详情数据
const faultDetail = ref<any>(null);
const isVoicePlaying = ref(false);

// 获取故障详情
const loadFaultDetail = async () => {
	// 模拟API调用
	return new Promise((resolve) => {
		setTimeout(() => {
			const mockDetail = {
				id: pq.value.id || '1',
				machineNumber: '024号细纱机',
				faultType: '停车',
				faultDescription: '机台突然停车，疑似电机故障，需要检查电机连接线路。现场检查发现电机有异响，建议立即停机检修，避免进一步损坏。',
				status: 'completed',
				createTime: '2024-01-15 14:30:00',
				processTime: '2024-01-15 15:45:00',
				images: ['/static/icons/<EMAIL>', '/static/icons/<EMAIL>'],
				voiceFile: '/voice/fault_record_001.mp3',
				voiceDuration: '0:32',
				processRecords: [
					{
						title: '故障上报',
						description: '工人发现机台异常停车，立即上报',
						time: '2024-01-15 14:30:00',
						operator: '张三',
						status: 'completed',
					},
					{
						title: '维修人员接单',
						description: '维修人员李四接收故障单，开始处理',
						time: '2024-01-15 14:35:00',
						operator: '李四',
						status: 'completed',
					},
					{
						title: '现场检查',
						description: '到达现场进行故障诊断，确认电机故障',
						time: '2024-01-15 14:50:00',
						operator: '李四',
						status: 'completed',
					},
					{
						title: '更换电机',
						description: '更换故障电机，重新调试设备',
						time: '2024-01-15 15:20:00',
						operator: '李四',
						status: 'completed',
					},
					{
						title: '故障解决',
						description: '设备恢复正常运行，故障处理完成',
						time: '2024-01-15 15:45:00',
						operator: '李四',
						status: 'completed',
					},
				],
			};
			faultDetail.value = mockDetail;
			resolve(mockDetail);
		}, 500);
	});
};

// 获取故障类型样式
const getFaultTypeClass = (status: string) => {
	switch (status) {
		case 'completed':
			return 'success';
		case 'processing':
			return 'warning';
		case 'pending':
			return 'error';
		default:
			return '';
	}
};

// 获取状态样式
const getStatusClass = (status: string) => {
	switch (status) {
		case 'completed':
			return 'success';
		case 'processing':
			return 'warning';
		case 'pending':
			return 'error';
		default:
			return '';
	}
};

// 获取状态文本
const getStatusText = (status: string) => {
	switch (status) {
		case 'completed':
			return '已完成';
		case 'processing':
			return '处理中';
		case 'pending':
			return '待处理';
		default:
			return '未知';
	}
};

// 获取处理记录状态样式
const getProcessStatusClass = (status: string) => {
	return status === 'completed' ? 'completed' : 'pending';
};

// 预览图片
const previewImage = (current: string, index: number) => {
	uni.previewImage({
		current: index,
		urls: faultDetail.value.images,
	});
};

// 切换语音播放
const toggleVoicePlay = () => {
	if (isVoicePlaying.value) {
		// 停止播放
		uni.stopVoice();
		isVoicePlaying.value = false;
	} else {
		// 开始播放
		uni.playVoice({
			filePath: faultDetail.value.voiceFile,
			complete: () => {
				isVoicePlaying.value = false;
			},
		});
		isVoicePlaying.value = true;
	}
};

// 页面初始化
onMounted(() => {
	loadFaultDetail();
});
</script>

<style lang="scss" scoped>
.detail-container {
	padding: 32rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
}

.info-section,
.desc-section,
.images-section,
.voice-section,
.process-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-content {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.info-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.label {
	font-size: 28rpx;
	color: #666;
	min-width: 160rpx;
}

.value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	text-align: right;
}

.fault-type,
.status-badge {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;

	&.success {
		background: #f6ffed;
		color: #52c41a;
		border: 1rpx solid #b7eb8f;
	}

	&.warning {
		background: #fffbe6;
		color: #faad14;
		border: 1rpx solid #ffe58f;
	}

	&.error {
		background: #fff2f0;
		color: #ff4d4f;
		border: 1rpx solid #ffccc7;
	}
}

.desc-content {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.images-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.image-item {
	aspect-ratio: 1;
	border-radius: 12rpx;
	overflow: hidden;
	background: #f5f5f5;
}

.fault-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.voice-player {
	display: flex;
	align-items: center;
	gap: 24rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.play-btn {
	width: 64rpx;
	height: 64rpx;
	background: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(28, 100, 253, 0.15);
}

.voice-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.voice-duration {
	font-size: 32rpx;
	font-weight: 600;
	color: #1c64fd;
}

.voice-tip {
	font-size: 24rpx;
	color: #999;
}

.process-timeline {
	position: relative;
	padding-left: 40rpx;
}

.timeline-item {
	position: relative;
	padding-bottom: 48rpx;

	&:last-child {
		padding-bottom: 0;
	}

	&:not(:last-child)::after {
		content: '';
		position: absolute;
		left: -32rpx;
		top: 32rpx;
		width: 4rpx;
		height: calc(100% - 16rpx);
		background: #e8e8e8;
	}
}

.timeline-dot {
	position: absolute;
	left: -40rpx;
	top: 8rpx;
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;

	&.completed {
		background: #52c41a;
	}

	&.pending {
		background: #d9d9d9;
	}
}

.timeline-content {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.process-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.process-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.process-time {
	font-size: 24rpx;
	color: #999;
}

.process-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.process-operator {
	font-size: 24rpx;
	color: #999;
}
</style>
