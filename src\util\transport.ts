import { appconfig } from '@/config/appconfig';

function createTransport(url: string): IMTransport {
	const socketTask = uni.connectSocket({ url, complete() {} });
	return {
		onOpen(callback) {
			socketTask.onOpen(callback);
		},
		onClose(callback) {
			socketTask.onClose(callback);
		},
		onError(callback) {
			socketTask.onError(callback);
		},
		onMessage(callback) {
			socketTask.onMessage((res) => callback(res.data));
		},
		close() {
			socketTask.close({});
		},
		send(data) {
			socketTask.send({ data });
		},
	};
}

export function connectWS(accessToken: string, tenantId: string) {
	return connectWS2(appconfig.imUrl, accessToken, tenantId);
}

export function connectHub(accessToken: string, tenantId: string) {
	return connectWS2(appconfig.hubUrl, accessToken, tenantId);
}

export function connectWS2(url: string, accessToken: string, tenantId: string) {
	let abortFlag = false;
	let completed = false;

	const task = new Promise<IMTransport>((resolve, reject) => {
		const transport = createTransport(`${url}?access_token=${encodeURIComponent(accessToken)}&TENANT-ID=${encodeURIComponent(tenantId)}`);

		transport.onOpen(() => {
			if (!abortFlag) {
				completed = true;
				resolve(transport);
			}
		});

		transport.onError(() => {
			if (!abortFlag && !completed) {
				reject(new Error('Connection failed'));
			}
		});
	});

	function abort() {
		abortFlag = true;
	}

	return {
		task,
		abort,
	};
}
