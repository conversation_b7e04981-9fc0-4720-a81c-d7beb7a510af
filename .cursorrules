# 代码格式化
- 使用Prettier进行代码格式化
- 统一的代码风格和命名规范
- 遵循Vue.js的组件命名和结构规范

# 项目运行环境
- 使用HBuilderX进行开发
- 通过"运行到手机或模拟器"功能进行测试
- 浏览器用于视觉开发，应用可在模拟器中同步运行
- uniapp需要兼容H5、安卓、ios三端
- 使用rpx作为单位进行开发，必要时可以使用%、vw、vh等单位，但不允许使用px单位

# 文件结构

## 根目录
- `.prettierrc.js`：代码格式化预设文件
- `package.json`：项目依赖管理配置
- `vite.config.ts`：Vite配置文件
- `src/`：源代码目录

## 源代码目录结构

```plaintext
src/
├── App.vue                 # 应用入口文件
├── components/             # 常用组件目录，符合easycom规范
│   ├── DIWAppPage/        # 应用页面组件
│   ├── DIWListView/       # 列表视图组件
│   ├── DIWLogin/          # 登录组件
│   ├── DIWProtect/        # 保护组件
│   ├── DIWRoot/           # 根组件
│   ├── DIWScrollView/     # 滚动视图组件
│   └── DIWSection/        # 页面分区组件
├── config/                # 配置文件目录
│   └── appconfig.ts       # 应用配置文件
├── framework/             # 框架代码目录
│   ├── devel.ts           # 开发相关代码
│   ├── error.ts           # 错误处理代码
│   ├── fx.ts              # 框架核心代码
│   ├── index.ts           # AutoImport扫描文件
│   ├── nav.ts             # 导航相关代码
│   ├── page.ts            # 页面相关代码
│   ├── scroll.ts          # 滚动相关代码
│   └── style.ts           # 样式相关代码
├── mod/                  # 模块目录
│   ├── main/              # 主模块，包含登录、注册、工作台等页面
├── store/                # store目录
│   ├── area.ts            # 省市县级数据
│   ├── dict.ts            # 静态字典
│   └── system.ts          # API调用、登录、登出等系统功能
├── types/                # d.ts文件存储目录
│   ├── api.d.ts           # API相关类型定义
│   ├── list.d.ts          # 列表相关类型定义
│   ├── scroll.d.ts        # 滚动相关类型定义
│   └── style.d.ts         # 样式相关类型定义
├── ui/                  # UI组件目录
│   └── wot-design-uni/   # 自定义开发的UI组件
├── util/                # 一些不依赖于框架的公共代码
├── locale/              # 国际化文件

# DIWAppPage 行为
- 使用DIWAppPage作为页面根元素
- 当用户尝试退出页面时，会弹出确认提示
- 提示信息可通过leaveMessage属性自定义
- 具有dirty属性，当值为true时，离开页面会弹出确认提示
- 在APP-PLUS中，使用safe-area-inset-top、safe-area-inset-bottom、lockScroll等属性来处理安全区域和锁定滚动
- 在MP-WEIXIN中，使用safe-area-inset-top、safe-area-inset-bottom、lockScroll等属性来处理安全区域和锁定滚动
- 需要使用@/ui/wot-design-uni/components/common/util.ts中的函数
- 不需要再引入vue中vue3内置方法，这些方法会自动引入；
- 需要使用const pq = usePageQuery()来获取路由参数
- 使用DIWAuth组件来控制权限

# 可访问性功能
- 快速点击屏幕3-4次可唤醒菜单，提供以下功能：
  - 使下一个网络调用失败
  - 让网络调用连续失败
  - 为每个网络调用添加不超过5秒的随机延迟 

# 项目约定
- 使用绝对路径进行导航
- 导航使用框架封装的DIWNavigator组件
- 导航接口使用框架封装的navigateTo、invokeTo和navigateBack
- 尽可能考虑安卓、ios的兼容性
- 使用条件编译处理平台特定代码
- 页面不能作为组件使用，也不应被其他组件导入
- formatDateTime 统一用这个来格式化时间日期 （import { formatDateTime } from '@/util/number';） 
- 国际化使用i18n，使用@/locale/index.ts中的函数，使用%home%这样的方式来使用国际化 
- 在项目中始终遵守使用并对应补充@/locale/ug-CN.json和@/locale/zh-CN.json中的内容，不直接使用中文，也不直接使用维语，使用t()函数来获取国际化内容
- 使用国际化
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

在/Users/<USER>/ck/Programmar/Front/mesphere-app/src/locale/zh-CN.json 和 /Users/<USER>/ck/Programmar/Front/mesphere-app/src/locale/ug-CN.json 添加 key 时要判断是否存在，不存在则添加，存在的则需要按照以下规则新增 key,模块名+key,例如：




# 滚动和下拉刷新
- 优先使用页面级滚动
- DIWListView组件封装了下拉刷新和加载更多的功能
- DIWAppPage和DIWScrollView提供了对DIWListView的支持

# useFramework 使用规范
- 框架代码广泛使用provide/inject机制实现组件间的接口交互
- 大多数接口函数必须通过useFramework获取和使用
- 保护机制函数：
  - protect：用于不提交数据或不改变状态的异步函数调用，如查询操作
  - protectOp：用于提交数据和改变状态的异步函数调用，如添加、删除和修改操作
  - 使用DIWAppPage作为根组件的页面，提示消息显示方式可能与不使用DIWAppPage的页面不同
  - 组件内下拉刷新可使用 injectDIWScrollManager，而页面直接使用onPullDownRefresh生命周期


# wot-design-uni 组件规范
- 使用MIT协议
- 从v1.7.1版本克隆源代码并纳入项目管理
- 定期从wot-design-uni合并新版本代码
- 使用过程中可能会因使用旧版Sass API而产生警告，但不影响使用
- wot-design-uni网址：https://wot-design-uni.cn/（当提示使用fetch mcp时，进行查询后实现功能）
- 一些debounce\throttle\debounceTime\throttleTime等函数，请使用@/ui/wot-design-uni/components/common/util.ts中的函数 

# 原子样式规范
- 使用UnoCSS进行原子样式设计
- 对于公共组件，避免使用原子样式，而应使用可通过CSS统一调整的、名称关系明确的类
- 原子CSS使用预设，写作方式类似于Tailwind CSS
- 以WXSS的最终效果为基准，任何在UnoCSS预设中可能与WXSS表现有问题的原子CSS写法都不应使用


# 版本控制
- 项目最终将打包两个应用版本：
  - 客户版：用于客户下单
  - 商家/服务提供商/平台版：用于商家等使用
- 客户版是用户启动应用时看到的初始版本
- 登录后，如果租户类型不是"客户"，应用将重新启动到商家版本
- 对于合同和提单等在两个版本中都需要但实现不同的功能，应通过页面级别区分
- 可通过组件复用提高代码复用率


# 文件组件规范
- DIWFile组件的model-value支持逗号分隔的多个文件ID列表

# 跨平台开发注意事项
- 优先使用flex布局进行开发，以兼容多端运行
- 使用HBuilderX作为开发工具
- 考虑H5、安卓、ios的兼容性
- 使用绝对路径进行导航
- 使用框架提供的导航组件和接口


# 代码组织和结构
- 页面和组件应存放在不同的目录中
- 所有页面都应使用DIWAppPage作为根元素
- 页面不应作为组件使用，也不应被其他组件导入
- 遵循项目约定的命名规范和代码结构

